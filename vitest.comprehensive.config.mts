/**
 * Comprehensive Vitest Configuration for All Tests
 * Generates unified coverage reports for all test types
 * Includes unit, integration, e2e, GDPR, accessibility, and performance tests
 */

import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import { resolve } from 'path'

export default defineConfig({
  plugins: [tsconfigPaths({ root: '.' }), react()],
  test: {
    name: 'comprehensive-test-suite',
    environment: 'jsdom', // Default environment, can be overridden per test
    setupFiles: ['./vitest.setup.ts'],
    include: [
      // Unit Tests
      'src/app/**/*.test.{ts,tsx}',
      'src/utilities/__tests__/**/*.test.ts',
      'tests/unit/**/*.test.ts',
      'tests/components/**/*.test.tsx',

      // Integration Tests
      'tests/int/**/*.int.spec.ts',

      // E2E Tests (Vitest E2E)
      'tests/e2e/**/*.e2e.spec.ts',
      'tests/e2e/**/*.spec.ts',

      // GDPR Tests
      'tests/gdpr/**/*.test.ts',

      // Accessibility Tests
      'tests/accessibility/**/*.test.ts',

      // Performance Tests
      'tests/performance/**/*.test.ts',

      // Email Tests
      'tests/email/**/*.test.ts'
    ],
    exclude: [
      // Global Test Fixtures
      'tests/fixtures/**',
      'tests/mocks/**',
      'tests/helpers/**',
      'tests/global-setup.ts',
      'tests/global-teardown.ts',

      // Playwright E2E Tests (handled separately)
      'tests/e2e/playwright/**',
      'tests/e2e/**/*.spec.ts',

      // Documentation and config
      '**/*.md',
      '**/*.config.*',
      '**/*.setup.*',
      '**/*.d.ts'
    ],
    coverage: {
      provider: 'v8',
      reporter: [
        'text',
        'json',
        'html',
        'lcov',
        'cobertura',  // XML format for CI/CD integrations
        'clover'      // Additional Java format
      ],
      all: true, // Track all files, not just tested ones
      include: [
        'src/**/*.ts',
        'src/**/*.tsx',
        'tests/**/*.test.ts',
        'tests/**/*.test.tsx',
        'tests/**/*.spec.ts'
      ],
      exclude: [
        // Generated files
        'src/payload-types.ts',

        // Type definitions
        '**/*.d.ts',

        // Configuration files
        '**/*.config.*',
        '**/*.setup.*',

        // Test fixtures and helpers (don't need coverage)
        'tests/fixtures/**',
        'tests/mocks/**',
        'tests/helpers/**',
        'tests/global-setup.ts',
        'tests/global-teardown.ts',

        // E2E test helpers
        'tests/e2e/global-setup.ts',
        'tests/e2e/global-teardown.ts',

        // Documentation
        '**/*.md',

        // Development scripts
        'scripts/**'
      ],
      reportsDirectory: './coverage/comprehensive',

      // Graceful thresholds based on current codebase state
      thresholds: {
        global: {
          statements: 75,  // Current codebase typical coverage
          branches: 70,
          functions: 75,
          lines: 75
        },
        // Test-specific thresholds
        'src/app/(payload)/**': {
          statements: 85,
          branches: 80,
          functions: 85,
          lines: 85
        },
        'src/utilities/**': {
          statements: 90,
          branches: 85,
          functions: 90,
          lines: 90
        },
        // Tests are expected to have high coverage
        'tests/gdpr/**': {
          statements: 95,
          branches: 90,
          functions: 95,
          lines: 95
        },
        'tests/unit/**': {
          statements: 90,
          branches: 85,
          functions: 90,
          lines: 90
        },
        'tests/int/**': {
          statements: 85,
          branches: 80,
          functions: 85,
          lines: 85
        }
      },
      // Always generate reports, even on test failures
      reportOnFailure: true
    },

    // Global test settings
    globals: true,
    testTimeout: 10000,  // Standard 10s timeout
    hookTimeout: 5000,

    // Environment-specific settings
    environmentOptions: {
      jsdom: {
        resources: 'usable',
        pretendToBeVisual: true,
        includeNodeLocations: true
      }
    },

    // Use standard thread pool configuration
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true
      }
    },

    // Standard reporters
    reporters: ['default']
  },

  // File system aliases for Payload CMS
  resolve: {
    alias: {
      // Ensure proper Payload CMS resolution
      '@/utilities': resolve('./src/utilities'),
      '@payload-config': resolve('./src/payload.config.ts')
    }
  }
})