
> rotary-cms@1.0.0 test /Users/<USER>/Dev/rotary/CMS_V2/rotary-cms
> dotenv -e test.env -- vitest run -- --coverage --reporter=json


 RUN  v1.6.0 /Users/<USER>/Dev/rotary/CMS_V2/rotary-cms

stdout | tests/accessibility/accessibility-baseline.test.ts > Accessibility Baseline - Task 1.2.4 Validation > WCAG 2.1 AA Compliance Testing > should pass critical accessibility violations
Accessibility scan completed: 0 violations found

 ✓ tests/accessibility/accessibility-baseline.test.ts  (8 tests) 7ms
 ✓ tests/gdpr/gdpr-compliance-simple.test.ts  (20 tests) 14ms
 ❯ src/utilities/__tests__/deepMerge.test.ts  (19 tests | 4 failed) 17ms
   ❯ src/utilities/__tests__/deepMerge.test.ts > deepMerge utility > isObject helper > should return false for non-objects
     → expected true to be false // Object.is equality
   ❯ src/utilities/__tests__/deepMerge.test.ts > deepMerge utility > isObject helper > should return false for dates
     → expected true to be false // Object.is equality
   ❯ src/utilities/__tests__/deepMerge.test.ts > deepMerge utility > isObject helper > should return false for regex
     → expected true to be false // Object.is equality
   ❯ src/utilities/__tests__/deepMerge.test.ts > deepMerge utility > deepMerge function > should handle edge cases with mixed data types
     → expected { '0': 1, '1': 2 } to deeply equal { replaced: true }
 ❯ src/app/(payload)/api/events/register/route.test.ts  (8 tests | 7 failed) 40ms
   ❯ src/app/(payload)/api/events/register/route.test.ts > Event Registration API > returns 400 for invalid registration data
     → expected 401 to be 400 // Object.is equality
   ❯ src/app/(payload)/api/events/register/route.test.ts > Event Registration API > returns 404 for non-existent events
     → expected 401 to be 404 // Object.is equality
   ❯ src/app/(payload)/api/events/register/route.test.ts > Event Registration API > returns 400 for events that do not require registration
     → expected 401 to be 400 // Object.is equality
   ❯ src/app/(payload)/api/events/register/route.test.ts > Event Registration API > returns 400 for duplicate registrations
     → expected 401 to be 400 // Object.is equality
   ❯ src/app/(payload)/api/events/register/route.test.ts > Event Registration API > returns 400 for events at capacity
     → expected 401 to be 400 // Object.is equality
   ❯ src/app/(payload)/api/events/register/route.test.ts > Event Registration API > successfully registers a new attendee
     → expected 401 to be 201 // Object.is equality
   ❯ src/app/(payload)/api/events/register/route.test.ts > Event Registration API > returns 400 for past events
     → expected 401 to be 400 // Object.is equality
 ✓ src/app/(frontend)/members/profile/ChangePasswordForm.test.tsx  (6 tests) 525ms
 ❯ tests/email.test.ts  (7 tests | 5 failed) 44ms
   ❯ tests/email.test.ts > Email Service > getRegistrationConfirmationTemplate > should generate English template
     → the given combination of arguments (undefined and string) is invalid for this assertion. You can use an array, a map, an object, a set, a string, or a weakset instead of a string
   ❯ tests/email.test.ts > Email Service > getRegistrationConfirmationTemplate > should generate French template
     → the given combination of arguments (undefined and string) is invalid for this assertion. You can use an array, a map, an object, a set, a string, or a weakset instead of a string
   ❯ tests/email.test.ts > Email Service > getRegistrationConfirmationTemplate > should generate Arabic template
     → the given combination of arguments (undefined and string) is invalid for this assertion. You can use an array, a map, an object, a set, a string, or a weakset instead of a string
   ❯ tests/email.test.ts > Email Service > getOrganizerNotificationTemplate > should generate English organizer notification
     → the given combination of arguments (undefined and string) is invalid for this assertion. You can use an array, a map, an object, a set, a string, or a weakset instead of a string
   ❯ tests/email.test.ts > Email Service > getOrganizerNotificationTemplate > should generate French organizer notification
     → the given combination of arguments (undefined and string) is invalid for this assertion. You can use an array, a map, an object, a set, a string, or a weakset instead of a string
stdout | tests/email.test.ts > Email Service > sendEmail > should send email successfully
Email sent <NAME_EMAIL>: test-message-id

stdout | tests/email.test.ts > Email Service > sendEmail > should handle email sending errors
Email transporter closed successfully

 ✓ src/app/(frontend)/members/profile/PrivacySettingsForm.test.tsx  (5 tests) 644ms
 ✓ src/app/(frontend)/members/profile/CommunicationPreferencesForm.test.tsx  (5 tests) 1089ms
 ✓ tests/unit/i18n-validation.test.ts  (9 tests) 8ms
 ✓ src/app/(frontend)/members/profile/BasicInfoForm.test.tsx  (5 tests) 475ms
 ✓ tests/unit/EventRegistrationForm.test.ts  (8 tests) 8ms
 ❯ tests/performance-baseline.test.ts  (0 test)
 ❯ src/app/(payload)/api/users/profile/update/route.test.ts  (0 test)
 ❯ src/app/(payload)/api/users/profile/route.test.ts  (0 test)
 ❯ tests/int/i18n-api.int.spec.ts  (0 test)
 ❯ tests/gdpr/gdpr-compliance.test.ts  (0 test)
 ❯ src/utilities/__tests__/formatDateTime.test.ts  (13 tests | 1 failed) 11ms
   ❯ src/utilities/__tests__/formatDateTime.test.ts > formatDateTime > should return MM/DD/YYYY format consistently
     → expected '01/01/2024' to be '12/31/2023' // Object.is equality
 ✓ src/utilities/__tests__/canUseDOM.test.ts  (5 tests) 5ms
 ✓ src/utilities/__tests__/eventExports-complete.test.ts  (2 tests) 2ms
 ✓ src/app/(payload)/api/events/register/route-debug.test.ts  (1 test) 5ms
stdout | src/app/(payload)/api/events/register/route-debug.test.ts > Event Registration API - DEBUG > Minimal authentication test
Response status: 404
Response data: { error: 'Event not found', code: 'EVENT_NOT_FOUND' }

 ✓ src/utilities/__tests__/eventExports.test.ts  (1 test) 42ms
 ❯ tests/int/api.int.spec.ts  (0 test)

 Test Files  10 failed | 12 passed (22)
      Tests  17 failed | 105 passed (122)
   Start at  14:05:18
   Duration  9.38s (transform 1.48s, setup 1.23s, collect 3.90s, tests 2.94s, environment 22.22s, prepare 5.31s)

 ELIFECYCLE  Test failed. See above for more details.
