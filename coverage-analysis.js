import fs from 'fs'
import path from 'path'
import globPkg from 'glob'
const { glob } = globPkg

const analyzeCoverageGap = async () => {
  try {
    // Read coverage data if it exists (check multiple possible locations)
    const possiblePaths = [
      'coverage/coverage-final.json',
      'coverage/comprehensive/coverage-final.json',
    ]

    let coverageData = {}
    let coveragePath = null

    for (let path of possiblePaths) {
      if (fs.existsSync(path)) {
        coverageData = JSON.parse(fs.readFileSync(path, 'utf-8'))
        coveragePath = path
        break
      }
    }

    if (!coveragePath) {
      console.log('❌ Coverage report not found. Please run tests with coverage first.')
      console.log('Searched in: ' + possiblePaths.join(', '))
      process.exit(1)
    }

    console.log(`✅ Found coverage report at: ${coveragePath}`)

    // Get all source files
    const allFiles = await getAllSourceFiles()
    const testedFiles = new Set(Object.keys(coverageData))

    console.log(`📊 Total Files: ${allFiles.length}`)
    console.log(`✅ Tested Files: ${testedFiles.size}`)
    console.log(`❌ Untested Files: ${allFiles.length - testedFiles.size}`)
    console.log(`📈 Coverage Rate: ${((testedFiles.size / allFiles.length) * 100).toFixed(1)}%\n`)

    // Prioritize by business impact
    const untestedByCriticality = allFiles
      .filter((file) => !testedFiles.has(file))
      .sort((a, b) => {
        const aScore = calculateCriticality(a, allFiles.length)
        const bScore = calculateCriticality(b, allFiles.length)
        return bScore - aScore
      })

    console.log('🎯 TOP 10 UNTESTED FILES BY PRIORITY:')
    console.log('='.repeat(50))

    const top10 = untestedByCriticality.slice(0, 10)
    top10.forEach((file, index) => {
      const score = calculateCriticality(file, allFiles.length)
      console.log(`${index + 1}. ${file} (Priority: ${score})`)
    })

    console.log('\n📋 RECOMMENDED TESTING STRATEGY:')
    console.log('='.repeat(50))

    generateTestingStrategy(top10, testedFiles.size, allFiles.length)

    // Export results for further analysis
    const results = {
      totalFiles: allFiles.length,
      testedFiles: testedFiles.size,
      coverageRate: (testedFiles.size / allFiles.length) * 100,
      topUntestedFiles: top10,
      recommendedStrategy: generateTestingStrategy(top10, testedFiles.size, allFiles.length, true),
    }

    fs.writeFileSync('coverage-analysis-report.json', JSON.stringify(results, null, 2))
    console.log('\n💾 Results exported to: coverage-analysis-report.json')
  } catch (error) {
    console.error('❌ Error during coverage analysis:', error.message)
    process.exit(1)
  }
}

const getAllSourceFiles = async () => {
  const patterns = [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/node_modules/**',
    '!src/**/.next/**',
    '!src/**/coverage/**',
  ]

  const files = new Set()

  try {
    for (const pattern of patterns) {
      console.log(`🔍 Searching pattern: ${pattern}`)
      const matches = await glob(pattern, { absolute: true })

      // Ensure matches is an array
      if (Array.isArray(matches)) {
        matches.forEach(match => files.add(match))
        console.log(`   Found ${matches.length} files`)
      } else {
        console.log(`   Pattern returned non-array (${typeof matches})`)
      }
    }
  } catch (error) {
    console.error(`❌ Error in file discovery: ${error.message}`)
  }

  return Array.from(files) // Convert Set to array
}

const calculateCriticality = (filePath, totalFiles) => {
  /* Higher score = Higher priority */
  let score = 0

  const relativePath = path.relative(process.cwd(), filePath)

  // Utility files get highest priority
  if (relativePath.includes('/utilities/')) score += 10

  // API routes are critical
  if (relativePath.includes('/api/')) score += 9

  // Authentication and security
  if (relativePath.includes('auth') || relativePath.includes('security')) score += 8

  // Database and data management
  if (relativePath.includes('/collections/') || relativePath.includes('/fields/')) score += 7

  // Configuration files
  if (relativePath.includes('config') || relativePath.includes('Config')) score += 6

  // Core application logic
  if (relativePath.includes('/app/')) score += 5

  // Components (medium priority)
  if (relativePath.includes('/components/')) score += 3

  // File size bonus (larger files likely more complex)
  try {
    const stat = fs.statSync(filePath)
    const fileSize = stat.size / 1024 // KB
    if (fileSize > 50) score += 2
    if (fileSize > 100) score += 1
  } catch (e) {
    // File might not exist, skip size bonus
  }

  // File extension bonus (TSX more complex than TS)
  if (filePath.endsWith('.tsx')) score += 1

  return Math.min(score, 20) // Cap at 20
}

const generateTestingStrategy = (
  topUntestedFiles,
  testedCount,
  totalCount,
  returnObject = false,
) => {
  const urgentFiles = topUntestedFiles.filter((file) => {
    const relativePath = path.relative(process.cwd(), file)
    return (
      relativePath.includes('auth') ||
      relativePath.includes('/api/') ||
      relativePath.includes('/utilities/') ||
      relativePath.includes('security')
    )
  })

  const strategicRecommendations = [
    {
      priority: 'URGENT',
      time: 'Week 1-2',
      count: urgentFiles.length,
      focus: 'Security, API, Auth, Core Utilities',
      files: urgentFiles.map((f) => path.basename(f)),
    },
    {
      priority: 'HIGH',
      time: 'Week 2-4',
      count: Math.ceil((totalCount - testedCount) * 0.3),
      focus: 'Collections, Fields, Configuration',
      files: [],
    },
    {
      priority: 'MEDIUM',
      time: 'Week 4-8',
      count: Math.ceil((totalCount - testedCount) * 0.4),
      focus: 'Components, Hooks, Helper functions',
      files: [],
    },
    {
      priority: 'LOW',
      time: 'Week 8-12',
      count: Math.ceil((totalCount - testedCount) * 0.3),
      focus: 'UI Styling, Static Assets, Edge Cases',
      files: [],
    },
  ]

  const coverageTargets = [
    { week: 2, target: 60, focus: 'Core functionality' },
    { week: 4, target: 70, focus: 'Business logic' },
    { week: 6, target: 75, focus: 'Integration points' },
    { week: 8, target: 80, focus: 'Edge cases' },
    { week: 12, target: 85, focus: 'Optimization' },
  ]

  const nextSteps = [
    '1. Set up automated test templates for common patterns',
    '2. Implement parallel test execution',
    '3. Add coverage trend monitoring',
    '4. Integrate with CI/CD quality gates',
    '5. Establish regression testing baselines',
  ]

  if (returnObject) {
    return {
      phases: strategicRecommendations,
      targets: coverageTargets,
      nextSteps: nextSteps,
      estimatedTotalTests: Math.ceil((totalCount - testedCount) * 5), // Rough estimate: 5 tests per file
      timeTo80Percent: totalCount * 0.2 * 2, // Rough estimate: 2 hours per file
    }
  }

  console.log('📅 PHASE-BASED APPROACH:')
  strategicRecommendations.forEach((rec) => {
    console.log(`\n🔴 ${rec.priority} (${rec.time})`)
    console.log(`   Files: ${rec.count} | Focus: ${rec.focus}`)
    if (rec.files.length > 0) {
      console.log(`   Examples: ${rec.files.slice(0, 3).join(', ')}`)
    }
  })

  console.log('\n🎯 COVERAGE TARGETS:')
  coverageTargets.forEach((target) => {
    console.log(`   Week ${target.week}: ${target.target}% (${target.focus})`)
  })

  console.log('\n🚀 NEXT STEPS:')
  nextSteps.forEach((step) => console.log(`   • ${step}`))
}

// Execute analysis
analyzeCoverageGap()
