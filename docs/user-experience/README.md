# **User Experience Documentation**
## **Rotary Club Tunis Doyen Website CMS**

**Version:** 1.0
**Author:** Monem
**Date:** August 27, 2025
**Related PRD Version:** 1.2
**Related Content Model Version:** 1.1
**Change Log:** `docs/audit-trail/ChangeLog.md`

---

### **Table of Contents**

1. User Flow Diagrams (Text-Based Flow Descriptions)
2. Wireframes (Text-Based Layout Descriptions)
3. Cross-References to Technical Implementation
4. Accessibility & Usability Considerations

---

### **1. User Flow Diagrams (Text-Based Flow Descriptions) – Revised**

---

**Public Visitor Flow: Homepage → Browse Events → Select Event → Register (RSVP) → Confirmation Page → Receive Email**

*User Role: Public Visitor*
*Goal: Register for a public event with confidence and receive confirmation.*

*Related Technical Elements:*
- Collections: `event_registrations`, `events`
- Hooks: `after_create` webhook for email notifications
- Features: reCAPTCHA v3, real-time validation, multilingual support

1.  User lands on the **Homepage**. The language toggle (FR/AR/EN) is visible in the top-right corner.
2.  User clicks the "Events" link in the main navigation.
3.  System displays the **Events List Page**. User can browse or use the search/filter bar (e.g., by date or cause).
4.  User clicks on an event card (e.g., "Clean Beach Day").
5.  System displays the **Event Detail Page**, showing title, date, location, description, and an "RSVP Now" button.
6.  **Capacity Check:** System checks if the event has reached its maximum capacity.
    *   **If at capacity:** Hides the "RSVP Now" button. Displays a message: "This event is full." Flow ends.
    *   **If not at capacity:** Proceeds to step 7.
7.  User clicks "RSVP Now."
8.  System displays a modal or new page with the **RSVP Form**:
    *   Fields: Name, Email, Phone (optional), Number of Guests.
    *   **Anti-Spam:** Includes a reCAPTCHA v3 check (invisible to user).
    *   Validation: Real-time feedback for invalid email.
    *   Language: Form labels and placeholders are in the current site language.
9.  User fills out the form and clicks "Submit."
10. System validates input:
    *   If invalid: Displays error messages in **red text, directly below the relevant field**. Example: For an invalid email, "Please enter a valid email address." Focus is set on the first invalid field.
    *   If valid: Proceeds to step 11.
11. System creates a registration record in the `event_registrations` collection.
12. System redirects user to a **Confirmation Page** with:
    *   A success message: "Thank you for registering for [Event Name]!"
    *   Event details (date, time, location).
    *   A unique registration ID.
    *   A "Back to Events" button.
13. In the background, the system triggers a webhook:
    *   Sends a confirmation email to the user's email address.
    *   Sends a notification email to the event organizer(s).
14. Flow ends. User can close the browser or explore other content.

---

**Member Flow: Login → Access Member Directory → View Profile → Edit Personal Information → Save Changes**

*User Role: Current Member*
*Goal: Update personal information securely and access private resources.*

*Related Technical Elements:*
- Collections: `members`
- Features: JWT authentication, role-based access control, multilingual bio editing
- Security: Secure login validation, profile data encryption

1.  User lands on the **Homepage** or a member-only page and clicks "Member Login" in the header.
2.  System displays the **Login Page** with fields for Email and Password.
3.  User enters credentials and clicks "Log In."
4.  System validates credentials:
    *   If invalid: Displays a **non-dismissible red banner at the top of the login form** with the message: "The email or password you entered is incorrect. Please try again or use the 'Forgot Password' link." Focus is set on the Email field.
    *   If valid: Proceeds to step 5.
5.  System checks user role:
    *   If role is "Member," "Editor," or "Admin": Redirects to the **Homepage** with a personalized welcome message (e.g., "Welcome back, Karim!"). The navigation now includes a "Member Directory" link.
    *   If role is not recognized (e.g., deactivated): Displays a **"Access Denied" page** with the message: "Your account does not have permission to access this area." Flow ends.
6.  User clicks "Member Directory" in the navigation.
7.  System displays the **Member Directory Page**:
    *   A searchable table of members (Name, Role, Club Role, Email/Phone visible only to logged-in users).
    *   A "My Profile" link in the header or sidebar.
8.  User clicks "My Profile."
9.  System displays the **Profile Edit Page**:
    *   Form with fields: Name, Email (read-only), Phone, Profile Photo (upload), Bio (in current language).
    *   Language toggle allows editing the Bio in FR, AR, or EN.
10. User makes changes and clicks "Save Changes."
11. System validates input (e.g., phone format):
    *   If invalid: Displays error messages in **red text below the relevant field**. Example: "Please enter a valid phone number."
    *   If valid: Proceeds to step 12.
12. System updates the member's record in the `members` collection.
13. System displays a success toast message: "Your profile has been updated."
14. Flow ends. User can continue browsing or log out.

---

**Admin/Editor Flow: Log in to CMS → Create New Event → Upload Image & Enter Multilingual Details → Publish → Event Appears on Public Calendar**

*User Role: Admin or Editor*
*Goal: Efficiently create and publish a new event in all three languages.*

*Related Technical Elements:*
- Collections: `events`, `members` (for organizers)
- Hooks: `before_publish` for multilingual validation
- Features: Payload CMS admin interface, multilingual tabs (FR/AR/EN), image upload

1.  User navigates to `/admin` and enters CMS credentials.
2.  System authenticates user and checks role:
    *   If role is "Admin" or "Editor": Grants access to the **CMS Dashboard**.
    *   If role is "Member" or invalid: Denies access.
3.  On the **Dashboard**, user clicks the "+ New" button and selects "Event."
4.  System opens the **Event Creation Form**.
5.  User uploads a cover image using the image field.
6.  User enters the event title, description, location, and date/time in the **French (FR)** tab.
7.  User switches to the **Arabic (AR)** tab and enters the same content in Arabic.
8.  User switches to the **English (EN)** tab and enters the same content in English.
9.  User assigns one or more organizers from the `members` collection using a relationship selector.
10. User sets the "Is Public" toggle to "Yes."
11. User clicks "Save as Draft" or "Publish."
12. System validates required fields (title, date, description in primary language):
    *   If invalid: Displays errors in a red box at the top of the form and highlights the relevant fields/tabs.
    *   If valid and "Save as Draft": Saves the entry in draft status. Flow ends.
    *   If valid and "Publish": Proceeds to step 13.
13. System runs the `before_publish` hook:
    *   Checks for missing translations in secondary languages.
    *   If primary language (e.g., FR) is complete: Publishes the event.
    *   If primary language is incomplete: Blocks publishing and displays a warning: "The [Primary Language] content is incomplete. Please complete all required fields before publishing."
14. System publishes the event and sets its status to "Published."
15. System makes the event visible on the public **Events List Page** and **Event Detail Page** in all three languages.
16. Flow ends.

---

**Content Publishing Flow: Editor drafts News Article → Submits for Approval → Admin Reviews → Publishes → Article Appears on Homepage & News Page**

*User Role: Editor → Admin*
*Goal: Ensure content quality and brand consistency before publication.*

*Related Technical Elements:*
- Collections: `news`, `projects` (relationships)
- Hooks: `before_publish` for content validation
- Features: Approval workflow, multilingual content, email notifications

1.  **Editor Action:** An Editor logs in to the CMS and creates a new "News" entry.
2.  Editor fills in the title, excerpt, and content in FR, AR, and EN tabs, uploads a cover image, and selects a related project.
3.  Editor clicks "Submit for Approval" (instead of "Publish").
4.  System saves the entry with a status of "Pending Review."
5.  System sends a notification email to all users with the "Admin" role.
6.  **Editor Feedback Loop:** The article's status in the Editor's content list is updated to "Pending Admin Review." The "Publish" button is disabled.
7.  **Admin Action:** An Admin logs in to the CMS and sees the pending article in the "Pending Approvals" section of the **Dashboard**.
8.  Admin clicks on the article to open it in the **Content Editor**.
9.  Admin reviews the content for accuracy, tone, and completeness.
10. Admin decides:
    *   If approved: Clicks "Publish." System changes status to "Published," runs `before_publish` hook, and makes the article live on the public site.
    *   If rejected: Clicks "Send Back." System changes status to "Draft," sends a notification email to the Editor, and **displays a "Rejection Notes" field** where the Admin can enter feedback (e.g., "Please add more details about the impact"). This note is saved with the article.
11. **Editor Notification:** If rejected, the Editor receives an email with a link to the article. In the CMS, the "Rejection Notes" are visible above the content editor, and the status is "Draft (Rejected)."
12. If published, the article appears in the "News" section of the **Homepage** and on the **News Page**.
13. Flow ends.

---

### **2. Wireframes (Text-Based Layout Descriptions) – Revised**

*(The wireframe descriptions remain unchanged from the previous version, as they already provided a strong structural foundation. The critical improvements have been made in the user flows, which directly inform how these wireframes will be implemented, particularly regarding error states, feedback, and edge cases.)*

---

### **3. Cross-References to Technical Implementation**

| **User Flow Element** | **Related Collection** | **Related Hook/Endpoint** | **Related Feature** |
|----------------------|----------------------|---------------------------|-------------------|
| Event Registration | `event_registrations` | `after_create` webhook | Email notifications |
| Capacity Checking | `events` (capacity field) | N/A | Real-time validation |
| Anti-Spam Protection | N/A | N/A | reCAPTCHA v3 integration |
| Member Login | `members` | JWT authentication | Role-based access control |
| Profile Editing | `members` | N/A | Multilingual bio support |
| Multilingual Content | All collections | `before_publish` hook | FR/AR/EN tabs |
| Content Approval | `news` (status field) | Email notifications | Workflow management |
| Image Upload | Media collection | N/A | Drag-and-drop interface |

---

### **4. Accessibility & Usability Considerations**

#### **Accessibility Features**
- **Screen Reader Support**: All error messages announced with ARIA live regions
- **Keyboard Navigation**: All interactive elements accessible via keyboard
- **Focus Management**: Proper focus indicators and logical tab order
- **Color Contrast**: Error messages meet WCAG 2.1 AA standards
- **Alt Text**: All images include descriptive alt attributes

#### **Usability Enhancements**
- **Progressive Disclosure**: Information revealed contextually to reduce cognitive load
- **Real-time Validation**: Immediate feedback prevents user frustration
- **Clear Error Messages**: Specific, actionable error descriptions
- **Mobile Optimization**: Touch-friendly interfaces with appropriate sizing

#### **Performance Considerations**
- **Lazy Loading**: Images and content loaded as needed
- **Caching Strategy**: Form validation rules cached for performance
- **Progressive Enhancement**: Core functionality works without JavaScript

---

### **5. Related Accessibility Resources**

For comprehensive guidelines on implementing accessible forms throughout the CMS, refer to:
- **Guidelines for Designing Accessible Forms**: `docs/user-experience/accessibility-forms.md`
  - Detailed WCAG 2.1 AA compliance requirements
  - Implementation examples for public and admin forms
  - Testing guidelines for accessibility validation
  - Cross-references to technical implementation

---

This enhanced plan now provides a comprehensive blueprint that anticipates user challenges, ensures clear communication at every step, and closes critical feedback loops. It equips the design and development teams to build a system that is not only functional but also resilient, user-friendly, and true to the Rotary Club Tunis Doyen's mission of being **People of Action**.

**Related Documentation:**
- Content Model & Architecture Guide: `docs/content-modeling/README.md`
- Technical Architecture: `docs/architecture/README.md`
- Document Cross-Reference: `docs/Document-Cross-Reference.md`
- Change Log: `docs/audit-trail/ChangeLog.md`