# **Guidelines for Designing Accessible Forms**
## **Rotary Club Tunis Doyen Website CMS**

**Version:** 1.0
**Author:** Monem
**Date:** August 27, 2025
**Related PRD Version:** 1.2
**Related Content Model Version:** 1.1
**Change Log:** `docs/audit-trail/ChangeLog.md`

---

### **Table of Contents**

1. General Principles for All Forms
2. Guidelines for Public Website Forms
3. Guidelines for CMS Admin Panel Forms
4. Specific WCAG Success Criteria Addressed
5. Implementation Cross-References
6. Testing Guidelines

---

### **1. General Principles for All Forms** *(Enhanced)*

*   **Clarity and Simplicity:** Use clear, concise language. Avoid jargon. Keep forms as short as possible, only asking for essential information.
*   **Visible Labels:** Every form field must have a visible, persistent label. Do not rely on placeholder text as a label.
*   **Logical Structure:** Use proper HTML semantics (`<form>`, `<fieldset>`, `<legend>`, `<label>`, `<input>`, `<button>`). This provides crucial context to assistive technologies.
*   **Keyboard Navigation:** All form elements must be fully operable using a keyboard (Tab, Shift+Tab, Enter, Space). Ensure a logical tab order that follows the visual flow.
*   **Focus Indicators:** Provide a clear, visible focus indicator (e.g., a border or outline) for all interactive elements when they receive keyboard focus. Never remove the default focus ring without providing a highly visible alternative.
*   **Color is Not the Only Indicator:** Do not use color alone to convey information (e.g., red text for errors). Always pair it with text, icons, or other visual cues.
*   **Touch Target Size:** Ensure all interactive elements (buttons, links, form fields, checkboxes, radio buttons) have a **minimum touch target size of 44x44 CSS pixels**. This is essential for users with motor impairments and for ease of use on mobile devices. Group labels with their controls so the entire label area is tappable.

---

### **2. Guidelines for Public Website Forms (e.g., Event RSVP, Contact Form)** *(Enhanced)*

*   **Visible and Associated Labels:**
    *   Use the `<label>` element with a `for` attribute that matches the `id` of the input (e.g., `<label for="email">Email</label><input type="email" id="email">`).
    *   For screen-reader-only labels (if a visible label isn't desired), use a class with `position: absolute; left: -10000px;` but ensure it's still accessible.

*   **Input Types and Attributes:**
    *   Use the correct `type` attribute (e.g., `type="email"`, `type="tel"`, `type="number"`). This triggers appropriate keyboards on mobile devices and enables basic browser validation.
    *   Use `autocomplete` attributes (e.g., `autocomplete="name"`, `autocomplete="email"`) to help users auto-fill information.
    *   **Mobile Keyboard Management:** Ensure the form is designed so that when a virtual keyboard appears, the active input field is not obscured. Use CSS and viewport units to create a flexible layout that adjusts to the reduced screen space.

*   **Error Prevention and Handling:**
    *   **Real-time Validation:** Provide immediate feedback as the user types (e.g., a green checkmark for a valid email format).
    *   **Clear Error Messages:**
        *   **Summary at Top:** When a form is submitted with errors, display a **summary at the top of the form** with the heading "Please correct the following errors:".
        *   **Anchor Links:** Make each error in the summary a **link** that jumps to the corresponding field when clicked.
        *   **Specific Messages:** Place specific error messages **immediately after** the relevant field or input.
        *   Use `aria-describedby` to programmatically associate the error message with the input.
        *   Example: `<input type="email" id="email" aria-describedby="email-error">` `<div id="email-error">Please enter a valid email address.</div>`
    *   **Error Styling:** Use a combination of **color (e.g., red border)**, an **icon (e.g., an exclamation mark)**, and **text** to indicate errors.

*   **Required Fields:**
    *   Clearly mark required fields with an asterisk (*) or the word "Required".
    *   Provide a key explaining the symbol (e.g., "Fields marked with * are required").
    *   Use the `required` attribute on the input for programmatic identification.

*   **Language and Context:**
    *   Ensure the language of the form content is correctly set using the `lang` attribute on the page or form (e.g., `<html lang="fr">`).
    *   For multilingual forms, ensure the language changes appropriately when the user switches languages.

*   **Dynamic Feedback with ARIA Live Regions:**
    *   Use **ARIA live regions** to announce dynamic changes to screen readers.
    *   **For non-critical, helpful updates:** Use `aria-live="polite"` on a container. Example: A character counter that says "120 characters remaining."
    *   **For critical, immediate feedback:** Use `aria-live="assertive"` on a container. Example: A real-time validation message that says "Password must be at least 8 characters long."
    *   This ensures that users who are not looking at the screen are still informed of important changes.

*   **Confirmation:**
    *   After successful submission, display a clear success message on a new page or within the form area.
    *   This message should be announced by screen readers using `role="alert"` for immediate attention.

---

### **3. Guidelines for CMS Admin Panel Forms (e.g., Event Creation, Content Editor)** *(Enhanced)*

*   **Complex Field Handling:**
    *   **Relationship Selectors (e.g., "Organizer"):** Use accessible autocomplete widgets or searchable dropdowns. Ensure they are fully keyboard-navigable and announce options as the user navigates.
    *   **Rich Text Editors:** Use a WCAG-compliant editor (like TinyMCE or CKEditor) that provides accessible toolbars and allows users to add alt text to images and create semantic content (headings, lists).
    *   **File/Image Uploads:** Provide clear instructions. Allow users to see the filename and, if possible, a thumbnail. Provide a way to remove or replace the file.

*   **Multilingual Tabs:**
    *   Clearly label language tabs (e.g., "French (FR)", "Arabic (AR)", "English (EN)").
    *   Use ARIA roles (`role="tablist"`, `role="tab"`, `role="tabpanel"`) to create a proper tab interface.
    *   Ensure the active tab is visually distinct and programmatically announced.
    *   When a user submits a form, clearly indicate if a required field is missing in a non-active tab (e.g., highlight the tab with an error icon and use `aria-live="assertive"` to announce "The Arabic tab has missing required fields").

*   **Action Buttons:**
    *   Use clear, descriptive labels for buttons (e.g., "Save as Draft", "Submit for Approval", "Publish", "Cancel").
    *   Visually group related actions (e.g., place "Save" and "Publish" together, separate from "Cancel").
    *   Ensure the primary action (e.g., "Publish") is visually prominent.

*   **Status and Feedback:**
    *   Clearly display the current status of a content entry (e.g., "Draft", "Pending Review", "Published").
    *   Use status indicators that are both color-coded and include text.
    *   Provide success and error messages for actions like saving or publishing, using `role="alert"` for critical messages and `aria-live="polite"` for non-critical updates.

*   **Keyboard Shortcuts (Optional but Recommended):**
    *   Consider implementing common keyboard shortcuts (e.g., Ctrl+S to save) and document them in a help section.

---

### **4. Specific WCAG Success Criteria Addressed** *(Updated)*

*   **1.3.1 Info and Relationships:** Using proper HTML structure and ARIA.
*   **1.4.1 Use of Color:** Not using color alone to convey meaning.
*   **1.4.4 Resize text:** (Implicitly supported by responsive design).
*   **1.4.10 Reflow:** (Implicitly supported by responsive design).
*   **2.1.1 Keyboard:** Full keyboard operability.
*   **2.1.3 Keyboard (No Exception):** All functionality available via keyboard.
*   **2.4.6 Headings and Labels:** Clear labels and headings.
*   **2.5.5 Target Size:** Ensures touch targets are large enough.
*   **3.3.1 Error Identification:** Clearly identifying input errors.
*   **3.3.2 Labels or Instructions:** Providing clear instructions.
*   **3.3.3 Error Suggestion:** Providing suggestions for correcting errors.
*   **4.1.2 Name, Role, Value:** Ensuring components are accessible via assistive technologies.
*   **4.1.3 Status Messages:** Using `aria-live` regions for dynamic updates.

---

### **5. Implementation Cross-References**

| **Accessibility Feature** | **Related Collection** | **Related Hook/Endpoint** | **Related User Flow** | **Technical Implementation** |
|---------------------------|----------------------|---------------------------|----------------------|-----------------------------|
| **ARIA Live Regions** | N/A | N/A | Real-time validation | `aria-live="polite"` for character counters |
| **Error Summaries** | N/A | N/A | Form submission errors | Anchor links jumping to fields |
| **Focus Management** | N/A | N/A | All form interactions | Keyboard navigation with visible focus rings |
| **Semantic HTML** | All collections | N/A | All user flows | `<fieldset>`, `<legend>`, `<label>` elements |
| **Touch Targets** | N/A | N/A | Mobile interactions | 44x44px minimum size requirement |
| **Multilingual Support** | All collections | N/A | Content creation | Proper `lang` attributes and RTL support |
| **Screen Reader Labels** | All collections | N/A | All form fields | `aria-describedby` for error associations |

---

### **6. Testing Guidelines**

#### **Automated Testing**
- **HTML Validation:** Use tools like HTML Validator to check semantic structure
- **ARIA Validation:** Use axe-core or similar tools to validate ARIA implementation
- **Color Contrast:** Automated tools to check WCAG color contrast requirements
- **Keyboard Navigation:** Automated tests for tab order and keyboard accessibility

#### **Manual Testing**
- **Screen Reader Testing:** Test with NVDA, JAWS, or VoiceOver
- **Keyboard-only Navigation:** Complete all user flows using only keyboard
- **Touch Target Testing:** Verify minimum 44px touch targets on mobile devices
- **Error Handling Testing:** Test all error states and validation messages
- **Multilingual Testing:** Verify proper language switching and RTL support

#### **User Testing**
- **Cognitive Accessibility:** Test with users who have cognitive impairments
- **Motor Accessibility:** Test with users who have motor skill challenges
- **Visual Accessibility:** Test with users who have visual impairments
- **Multilingual Users:** Test with users in different language contexts

---

This enhanced guide now provides a complete, state-of-the-art framework for building accessible forms that are not only compliant but also intuitive, efficient, and empowering for all users, embodying Rotary's commitment to inclusive service.

**Related Documentation:**
- User Experience Documentation: `docs/user-experience/README.md`
- Content Model & Architecture Guide: `docs/content-modeling/README.md`
- Technical Architecture: `docs/architecture/README.md`
- Document Cross-Reference: `docs/Document-Cross-Reference.md`
- Change Log: `docs/audit-trail/ChangeLog.md`