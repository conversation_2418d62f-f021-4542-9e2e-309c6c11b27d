# **Project Requirements Document (PRD)**

## **Rotary Club Tunis Doyen Website CMS**

**Version:** 1.2
**Author:** Monem
**Date:** August 27, 2025
**Approved By:** Rotary Club Tunis Doyen Leadership & Communications Committee

---

### **Revision History**

| Version | Date       | Author           | Changes Made                     | Approved By             |
|--------|------------|------------------|----------------------------------|--------------------------|
| 1.0    | 2025-08-27 | Monem            | Initial draft completed          | Leadership Committee     |
| 1.1    | 2025-08-27 | Monem            | Updated Section 6 with detailed analysis of rotary.org | Leadership Committee     |
| 1.2    | 2025-08-27 | Monem            | Enhanced Section 6 with additional user engagement features, multimedia details, and performance optimizations | Leadership Committee     |

---

### **Table of Contents**

1. Introduction
2. Stakeholder Identification
3. Business Requirements
4. Functional Requirements
5. Non-Functional Requirements
6. UI/UX & Design Requirements
7. System Integration & Technical Environment
8. Data Requirements
9. Assumptions and Constraints
10. Dependencies
11. Acceptance Criteria
12. Success Metrics
13. Risks and Mitigations
14. Approval Section

---

### **1. Introduction**

#### **1.1 Purpose**

This document defines the requirements for the development of a new **Content Management System (CMS)** for the **Rotary Club Tunis Doyen**. It will serve as a single source of truth for all stakeholders, guiding design, development, testing, and deployment.

#### **1.2 Scope**

The project involves replacing the current static website with a modern, multilingual CMS that supports dynamic content, event management, member access, and a responsive design.

**In Scope:**

* Multilingual content management (French, Arabic, English).
* Event creation and online registration with a reminder system.
* Member directory with role-based access.
* Content creation and publishing for news, projects, and photo galleries.
* Responsive design for all devices.
* Data migration from the legacy site.
* Admin and editor content management interface.

**Out of Scope:**

* E-commerce or donation payment processing.
* A private member forum.
* A native mobile application.
* Integration with HR, finance, or external CRM systems.

#### **1.3 Objectives**

* Improve the club’s digital presence and public engagement.
* Enable non-technical staff to manage content easily.
* Increase event participation by **30% within one year**.
* Provide secure, role-based access for members and staff.
* Launch the new site by **Q1 2026**.

#### **1.4 Definitions, Acronyms, and Abbreviations**

| Term | Definition |
| :--- | :--- |
| CMS | Content Management System |
| UX | User Experience |
| UI | User Interface |
| WCAG | Web Content Accessibility Guidelines |
| API | Application Programming Interface |
| MVP | Minimum Viable Product |
| RBA | Role-Based Access |

---

### **2. Stakeholder Identification**

| Stakeholder | Role | Responsibility |
| :--- | :--- | :--- |
| Rotary Club Leadership | Sponsor | Final approval, budget oversight |
| Communications Committee | Primary User & Content Manager | Content input, testing, feedback |
| Rotary Club Members | End User | Access member content, register for events |
| Public Visitors | End User | View content, contact club, register for events |
| Development Team | Builder | Design, develop, test, deploy |

---

### **3. Business Requirements**

| ID | Requirement | Priority |
| :--- | :--- | :--- |
| BR-001 | Replace outdated static website with a modern, maintainable CMS. | Must |
| BR-002 | Improve public engagement through better content and UX. | Must |
| BR-003 | Streamline event registration and member communication. | Must |
| BR-004 | Support multilingual content to reflect Tunisia’s linguistic diversity. | Must |
| BR-005 | Enhance member engagement through exclusive content access. | Should |

---

### **4. Functional Requirements**

| ID | Requirement | User Role | Priority |
| :--- | :--- | :--- | :--- |
| FR-001 | The system shall support content in French, Arabic, and English with a language toggle. | All | Must |
| FR-002 | Admins shall be able to create, edit, and publish news, projects, and gallery content. | Admin, Editor | Must |
| FR-003 | Users shall be able to register for events online and receive confirmation emails. | Public, Member | Must |
| FR-004 | Members shall be able to log in to access the private member directory. | Member | Must |
| FR-005 | Members shall be able to update their profile information. | Member | Should |
| FR-006 | The system shall notify event organizers of new registrations. | Admin | Should |
| FR-007 | Admins shall be able to manage user roles (Admin, Editor, Member). | Admin | Must |

**User Roles:**

* **Admin:** Full access to content, users, and settings.
* **Editor:** Can draft and edit content; limited publishing rights.
* **Member:** Can view member-only content, register for events, and manage their profile.
* **Public:** Can view public content, register for events, and use the contact form.

---

### **5. Non-Functional Requirements**

| Category | Requirement | Priority |
| :--- | :--- | :--- |
| **Performance** | Page load time should be under 3 seconds under normal load. | Must |
| **Scalability** | The system must support up to 20 concurrent users. | Must |
| **Security** | Passwords must be stored with hashing; implement Role-Based Access Control (RBAC) and protect against common vulnerabilities like SQL injection and XSS. | Must |
| **Availability** | The website should have a 99.5% uptime and be available 24/7. | Must |
| **Usability** | The CMS interface must be intuitive for non-technical editors. | Must |
| **Accessibility** | The website must be compliant with WCAG 2.1 AA standards. | Must |
| **Reliability** | Email notifications must be delivered within 1 minute of registration. | Should |
| **Maintainability** | The solution must have well-documented code and an admin guide. | Should |

---

### **6. UI/UX & Design Requirements** *(Updated & Enhanced)*

The design of the Rotary Club Tunis Doyen website must reflect the **global identity, professionalism, and action-oriented spirit** of Rotary International, as exemplified by the current [rotary.org](https://www.rotary.org) website. The goal is to create a site that feels like a natural extension of the international brand while highlighting the club's local impact. This enhanced version incorporates advanced user engagement features, multimedia integration, and performance optimizations to elevate the user experience and drive community involvement.

#### **Core Design Principles (Based on rotary.org)**

* **Hero-Driven Homepage:** The homepage must open with a **full-width, high-quality hero image or video** showcasing a Rotary project or event in Tunisia. This should be overlaid with the powerful tagline: **"We are People of Action"** (or a localized equivalent in French/Arabic), mirroring the central message of rotary.org. Include dynamic elements such as subtle animations or parallax scrolling for enhanced engagement.
* **Impact-Oriented Statistics:** Prominently display key impact metrics in a visually engaging way, similar to the three-pillar layout on rotary.org. For our club, this could be:
  * `XX` Projects Completed
  * `XX,000` Volunteer Hours
  * `XXX` Members Served
    Use animated counters or progress bars to make the data more interactive and compelling.
* **Bold Visuals & Minimal Text:** Use large, compelling images and concise, powerful headlines. Avoid dense paragraphs. Emulate the clean, spacious layout of rotary.org, ensuring high-resolution images optimized for fast loading.
* **Clear Calls to Action (CTAs):** Use prominent, action-oriented buttons (e.g., "Join Our Club", "Attend an Event", "Support a Project") in Rotary blue (#004A87) to guide user behavior. Implement hover effects and micro-interactions to increase click-through rates.
* **"People of Action" Narrative:** Structure content to tell stories of impact. Feature member spotlights, project success stories, and community testimonials to humanize the club's work. Include interactive elements like video testimonials or photo galleries with lightbox views.
* **Global Brand Alignment:**
  * **Colors:** Primary color must be **Rotary Blue (#004A87)**. Use white, black, and neutral grays for a clean, professional look. Incorporate accent colors sparingly for highlights, such as Rotary Gold (#FFB400) for icons or buttons.
  * **Logo:** The official Rotary International logo must be used correctly and prominently, typically in the top-left corner of the header. Ensure it links back to rotary.org for brand reinforcement.
  * **Typography:** Use clean, modern, and highly readable sans-serif fonts (e.g., Open Sans, Lato, Inter) similar to those on rotary.org. Maintain consistent font sizes and weights for hierarchy, with Arabic and French support for localization.
* **Multimedia Integration:** Embed high-quality videos, audio clips, and infographics to showcase projects. Ensure all media is accessible, with captions and transcripts for videos to meet WCAG standards.
* **User Engagement Features:** Add interactive components such as social media feed integration (e.g., live Twitter/Instagram embeds), donation widgets, event registration forms, and a newsletter signup popup to foster community participation.
* **Performance Optimization:** Design with fast-loading principles in mind, including lazy-loading images, optimized file sizes, and minimal use of heavy scripts to ensure a smooth experience across devices.

#### **Structural & Functional Design**

* **Navigation:** Implement a simple, sticky top navigation bar with clear menu items: **Home, About Us, Our Projects, Events, News, Members, Contact Us**. The **language selector** (FR/AR/EN) must be clearly visible in the header, ideally on the right side. Include a search bar for easy content discovery.
* **Responsiveness:** The design must be fully responsive, with a mobile-first approach. The layout should adapt seamlessly from desktop to mobile, maintaining readability and touch-friendly CTAs. Use flexible grids and media queries to handle various screen sizes.
* **Accessibility:** Adhere strictly to **WCAG 2.1 AA** standards. Ensure sufficient color contrast, keyboard navigability, and proper ARIA labeling for screen readers. Include alt text for all images and focus indicators for interactive elements.
* **Content Sections:** Include dedicated, visually distinct sections for:
  * **Our Causes:** Highlight the club's focus areas (e.g., Peace, Health, Education) with icons and brief descriptions. Use hover tooltips or expandable cards for more details.
  * **Latest News & Features:** A dynamic grid or carousel showcasing recent articles and events. Implement infinite scroll or pagination for better navigation.
  * **Upcoming Events:** A clear list or calendar view of future events. Add filtering options by category or date, and integrate with Google Calendar for easy event saving.
* **Footer Design:** Include a comprehensive footer with quick links, social media icons, contact information, and a newsletter signup. Mirror the structured footer of rotary.org for consistency.

#### **Design Deliverables**

* **Wireframes:** Low-fidelity layouts to establish structure and user flow.
* **High-Fidelity Mockups:** Pixel-perfect designs for key pages (Home, About, Projects, Events) using rotary.org as the primary visual benchmark.
* **Prototype:** An interactive prototype to demonstrate navigation and user interactions, including animations and transitions.
* **Style Guide:** A comprehensive guide detailing colors, fonts, icons, and component libraries for consistent application across the site.

> *Note: All design assets will be reviewed and approved by the Communications Committee to ensure a balance between global brand fidelity and local club identity. Enhancements focus on increasing user retention through interactive elements and ensuring the site is optimized for search engines and social sharing.*

---

### **7. System Integration & Technical Environment**

| Requirement | Detail |
| :--- | :--- |
| **Integrations** | None in the initial release. Future integration with a payment gateway (e.g., Stripe) for donations is a potential future feature. |
| **APIs** | An internal RESTful API will be used for front-end/back-end communication. No public-facing APIs are required at this stage. |
| **Technology Stack** | To be determined. A headless CMS (like Strapi) or a robust framework (e.g., Laravel, Django) is recommended for flexibility and ease of use. |
| **Hosting** | A cloud-based solution (e.g., AWS, OVH) with SSL/TLS encryption is required. |

---

### **8. Data Requirements**

| Data Type | Description | Retention Policy |
| :--- | :--- | :--- |
| User Profiles | Name, email, role, membership status | Retain while active; archive after 2 years of inactivity. |
| Event Data | Title, date, description, registration list | Retain for 3 years post-event. |
| Content | Articles, project descriptions, news | Retain indefinitely unless deleted by an admin. |
| Media | Images, PDFs, documents | Retain with associated content. |

**Data Migration:**

* Migrate existing content (articles, project summaries, contact info) from the legacy static site.
* The migration method will be manual or semi-automated.
* Quality assurance must be performed to ensure all migrated content is accurate and correctly formatted.

---

### **9. Assumptions and Constraints**

**Assumptions**

* The content provided by the club is sufficient for the initial website launch.
* The CMS will be used by non-technical editors, so the interface must be highly intuitive.
* All members have basic digital literacy and internet access.
* Stakeholders will provide timely feedback during the design and testing phases.

**Constraints**

* **Limited Budget:** May restrict the number of advanced features or third-party tools.
* **6-Month Timeline:** Requires a focused scope and a well-defined phased delivery.
* **Small Development Team:** The chosen solution must be maintainable and well-documented.

---

### **10. Dependencies**

| Dependency | Owner | Expected Delivery |
| :--- | :--- | :--- |
| Final content and branding assets | Communications Committee | Month 1 |
| Approval of design mockups | Leadership & Communications Committee | Month 2 |
| Legacy data export | Club Admin | Month 1 |
| Final testing feedback | Stakeholders | Month 5 |

---

### **11. Acceptance Criteria**

| Feature | Acceptance Criteria |
| :--- | :--- |
| **Multilingual CMS** | The website displays correctly in French, Arabic, and English with no broken layouts or missing translations. |
| **Event Registration** | A user can register for an event and receive a confirmation email within 1 minute. The organizer receives a notification. |
| **Member Login** | Authenticated members can access the member directory and edit their profile. |
| **Responsive Design** | The website functions and displays correctly on mobile, tablet, and desktop browsers. |
| **Content Publishing** | An editor can draft an article, and an admin can publish it. The article appears live on the site. |

---

### **12. Success Metrics**

| Metric | Target | Measurement Method |
| :--- | :--- | :--- |
| **Website Traffic** | 25% increase within 6 months | Google Analytics |
| **Event Registrations**| 30% increase within 1 year | CMS registration logs |
| **User Satisfaction** | Positive feedback from 80% of members surveyed | Post-launch survey |

---

### **13. Risks and Mitigations**

| Risk | Likelihood | Impact | Mitigation Strategy |
| :--- | :--- | :--- | :--- |
| Delay in content delivery from stakeholders | Medium | High | Set clear deadlines; assign content owners. |
| Difficulty migrating legacy data | Medium | Medium | Start data migration early in the process. |
| Low user adoption by members | Medium | Medium | Provide a simple user guide and offer a training session. |
| Security vulnerability | Low | High | Conduct regular security audits and use secure, updated frameworks. |

---

### **14. Approval Section**

We, the undersigned, acknowledge that this document reflects the agreed-upon requirements for the Rotary Club Tunis Doyen Website CMS.

| Name | Role | Signature | Date |
| :--- | :--- | :--- | :--- |
| [Name] | Club President | | |
| [Name] | Communications Chair | | |
| [Name] | Project Lead | | |

---

### ✅ **Summary of Key Enhancements**

This version of the PRD strengthens the design direction by:

1. **Directly referencing the live rotary.org site** as the gold standard.
2. **Incorporating the core "People of Action" message** into the design DNA.
3. **Specifying the use of impact statistics** in a visually prominent way.
4. **Detailing the required visual elements** (hero image, CTAs, color, typography) to achieve brand consistency.
5. **Adding advanced user engagement features, multimedia integration, and performance optimizations** to enhance interactivity, accessibility, and overall user experience.
