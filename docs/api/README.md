# **API Documentation**
## **Rotary Club Tunis Doyen Website CMS**

**Version:** 1.1
**Author:** Monem
**Date:** August 27, 2025
**Related PRD Version:** 1.2
**Related Content Model Version:** 1.1
**Change Log:** `docs/audit-trail/ChangeLog.md`

---

### **Table of Contents**

1. Core API Concepts
2. Public Endpoints (No Authentication Required)
3. Error Handling & Business Logic
4. Authentication & Security
5. Administration Endpoints (API Key or Session Required)
6. Webhooks
7. Implementation Cross-References
8. Developer Resources

---

### **1. Core API Concepts**

#### **API Versioning**
All endpoints are versioned with a `/v1/` prefix. This ensures future updates can be released without disrupting existing integrations.

#### **Localized Content Schema**
To ensure consistency, all multilingual content (titles, descriptions, etc.) uses a standardized, reusable object schema:

```json
{
  "title": {
    "fr": "string",
    "ar": "string",
    "en": "string"
  },
  "description": {
    "fr": "string",
    "ar": "string",
    "en": "string"
  }
}
```

#### **Rate Limiting**
- Public endpoints: 100 requests per minute per IP
- Authenticated endpoints: 1000 requests per minute per API key
- Headers included: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

#### **Content Negotiation**
- Accepts `Accept-Language` header for content localization
- Supports `Accept: application/json` with UTF-8 encoding
- API responses include `Content-Language` header indicating returned locale

---

### **2. Public Endpoints (No Authentication Required)**

#### **News & Articles**
`GET /api/v1/news`

**Purpose:** Retrieve a paginated list of published news articles.

**Query Parameters:**
- `limit` (number, optional): Max 50, default 10
- `offset` (number, optional): Default 0
- `category` (string, optional): Filter by category (e.g., community-service, fundraising)

**Headers:**
- `Accept-Language: fr,ar;q=0.9,en;q=0.8` (preferred languages in order)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "articles": [
      {
        "id": "news_001",
        "slug": "rotary-gala-2025",
        "title": {
          "fr": "Gala de bienfaisance annuel du Rotary",
          "ar": "حفل الرواد الخيري السنوي",
          "en": "Rotary Annual Charity Gala"
        },
        "excerpt": {
          "fr": "Un aperçu de notre gala de charité pour financer des projets d'eau potable.",
          "ar": "نظرة عامة على حفلنا الخيري لتمويل مشاريع مياه الشرب النظيفة.",
          "en": "A recap of our charity gala to fund clean water projects."
        },
        "publish_date": "2025-08-20T10:00:00Z",
        "featured_image": "https://cdn.rotarytunis.org/news/gala-2025.jpg"
      }
    ],
    "pagination": {
      "total": 25,
      "limit": 10,
      "offset": 0
    }
  }
}
```

#### **Events & Activities**
`GET /api/v1/events`

**Purpose:** Retrieve a paginated list of upcoming and past events.

**Query Parameters:**
- `limit` (number, optional): Default 10
- `offset` (number, optional): Default 0
- `status` (string, optional): Filter by upcoming, past, or all. Default upcoming

#### **Event Registration**
`POST /api/v1/events/:id/register`

**Purpose:** Register for an event.

**Request Schema:**
```json
{
  "name": "string",
  "email": "string",
  "phone": "string",
  "membership_status": "member" | "non-member" | "guest",
  "dietary_requirements": "string"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Inscription confirmée pour la Journée de la Santé Communautaire",
  "data": {
    "confirmation_id": "REG-2025-087",
    "event_title": "Journée de la Santé Communautaire"
  }
}
```

#### **Homepage & Static Content**
`GET /api/v1/pages/homepage`

**Purpose:** Retrieve all localized homepage content in a single request.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "hero": {
      "title": {
        "fr": "Nous sommes des Gens d'Action",
        "ar": "نحن أهل عمل",
        "en": "We are People of Action"
      },
      "subtitle": {
        "fr": "Connectant 1,2 million de membres dans le monde...",
        "ar": "نربط 1.2 مليون عضو حول العالم...",
        "en": "Connecting 1.2 million members worldwide..."
      },
      "cta_text": { ... },
      "background_image": "https://cdn.rotarytunis.org/hero/tunis-action.jpg"
    },
    "impact_stats": [
      {
        "icon": "hands",
        "number": "47 million",
        "label": {
          "fr": "Heures de bénévolat annuelles",
          "ar": "ساعة تطوع سنويا",
          "en": "Volunteer hours annually"
        }
      }
    ]
  }
}
```

---

### **3. Error Handling & Business Logic**

#### **Standard Error Response Format**
All API errors follow this consistent, detailed JSON structure to aid in debugging:

```json
{
  "error": true,
  "code": 400,
  "message": "Validation failed",
  "details": [
    { "field": "email", "message": "Must be a valid email address." },
    { "field": "name", "message": "Must be at least 2 characters long." }
  ],
  "request_id": "req_abc123def456"
}
```

#### **HTTP Status Codes**
- **400 Bad Request**: Validation errors on fields
- **401 Unauthorized**: Missing or invalid authentication credentials
- **403 Forbidden**: Valid credentials, but insufficient permissions
- **404 Not Found**: The requested resource does not exist
- **409 Conflict**: The request violates a business rule (e.g., duplicate registration)
- **422 Unprocessable Entity**: The request is valid but cannot be processed due to its state (e.g., registering for a past event)
- **429 Too Many Requests**: Rate limit exceeded

#### **Business Logic Validation**
- **Event Registration**: Prevents duplicate registrations, validates event capacity and dates
- **Content Publishing**: Enforces multilingual content requirements before publication
- **User Permissions**: Validates role-based access to administration endpoints
- **Data Integrity**: Ensures referential integrity across related collections

---

### **4. Authentication & Security**

#### **Hybrid Authentication Strategy**
The API uses both cookie-based sessions for admin users and API Key authentication for programmatic access.

#### **Session-Based Authentication Flow**
```
Request: POST /api/v1/auth/login with email and password
Response: Server sets an HttpOnly and Secure cookie
Returns: 200 OK with user object
Subsequent Requests: Browser automatically includes session cookie
```

#### **API Key Authentication**
- **Format**: Bearer token in Authorization header: `Authorization: Bearer rct_live_xyz123abc456def789`
- **Scopes**: Keys can be restricted to specific scopes (content:write, member:read, events:admin)
- **Security**: All API key operations are handled over HTTPS with rate limiting

#### **Security Measures**
- **HTTPS Only**: All API endpoints require secure connections
- **Input Validation**: Comprehensive server-side validation and sanitization
- **CORS Policy**: Configured for allowed origins only
- **Audit Logging**: All API operations are logged for security monitoring

---

### **5. Administration Endpoints (API Key or Session Required)**

#### **News & Articles**
`POST /api/v1/news`

**Purpose:** Create a new news article.

**Auth:** API Key with `content:write` scope

**Request Schema:**
```json
{
  "title": { "fr": "string", "ar": "string", "en": "string" },
  "excerpt": { "fr": "string", "ar": "string", "en": "string" },
  "content": { "fr": "string", "ar": "string", "en": "string" },
  "category": "community-service" | "fundraising" | "fellowship",
  "author_id": "string",
  "status": "draft" | "published"
}
```

`PATCH /api/v1/news/:id`

**Purpose:** Update one or more fields of an existing news article.

**Auth:** API Key with `content:write` scope

**Request Schema:** Accepts any subset of the POST schema fields

`DELETE /api/v1/news/:id`

**Purpose:** Delete a news article.

**Auth:** API Key with `content:admin` scope

#### **Events & Activities**
`POST /api/v1/events`

**Purpose:** Create a new event.

**Auth:** API Key with `content:write` scope

**Request Schema:**
```json
{
  "title": { "fr": "string", "ar": "string", "en": "string" },
  "description": { "fr": "string", "ar": "string", "en": "string" },
  "date": "2025-09-15T09:00:00Z",
  "location": "string",
  "category": "string",
  "max_attendees": "number",
  "image_url": "string"
}
```

`PATCH /api/v1/events/:id`

**Purpose:** Update one or more fields of an existing event.

**Auth:** API Key with `content:write` scope

`DELETE /api/v1/events/:id`

**Purpose:** Delete an event.

**Auth:** API Key with `events:admin` scope

---

### **6. Webhooks**

Webhooks allow external systems to receive real-time notifications about events in the CMS.

#### **Available Events**
- **`news.published`**: A new article has been published
- **`event.created`**: A new event has been created
- **`event.registration`**: A new registration has been submitted

#### **Webhook Payload Example**
```json
{
  "event": "event.registration",
  "timestamp": "2025-08-27T10:30:00Z",
  "data": {
    "event_id": "evt_123",
    "event_title": "Community Health Day",
    "registrant": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "confirmation_id": "REG-2025-087"
    }
  }
}
```

#### **Configuration**
- **Endpoint**: Configure webhook URLs in the CMS admin panel
- **Security**: Webhooks include HMAC signatures for verification
- **Retries**: Failed deliveries are retried with exponential backoff
- **Rate Limiting**: Webhook deliveries are rate-limited to prevent abuse

#### **Example Use Case**
Use the `event.registration` webhook to automatically send confirmation emails without the client app needing to wait for a separate service response.

---

### **7. Implementation Cross-References**

| **API Feature** | **Related Collection** | **Related Hook/Endpoint** | **Related User Flow** | **Technical Implementation** |
|-----------------|----------------------|---------------------------|----------------------|-----------------------------|
| **News API** | `news` | N/A | Content browsing | Pagination, filtering, localization |
| **Events API** | `events` | `after_create` webhook | Event registration | Capacity checking, email notifications |
| **Homepage API** | `home_page` | `/api/impact-stats` endpoint | Homepage display | Dynamic content aggregation |
| **Authentication** | `members` | JWT authentication | Login flows | Session management, API key validation |
| **Webhooks** | All collections | `after_create`, `before_publish` | All automation | Real-time notifications, external integration |
| **Multilingual Content** | All collections | N/A | All content creation | Localized schema, RTL support |

---

### **8. Developer Resources**

#### **Client Libraries**
- **JavaScript/Node.js**: `npm install @rotary-tunis/api-client`
- **Python**: `pip install rotary-tunis-api`
- **PHP**: Composer package available

#### **Postman Collection**
Download the complete API collection: `docs/api/rotary-tunis-api.postman_collection.json`

#### **Testing**
- **Sandbox Environment**: `https://api.sandbox.rotarytunis.org`
- **Production Environment**: `https://api.rotarytunis.org`
- **Rate Limits**: Sandbox has higher limits for development

#### **Support**
- **Documentation**: `docs/api/README.md`
- **Issues**: GitHub repository issues
- **Community**: Rotary Tunis developer forum

---

**Related Documentation:**
- Content Model & Architecture Guide: `docs/content-modeling/README.md`
- Technical Architecture: `docs/architecture/README.md`
- User Experience Documentation: `docs/user-experience/README.md`
- Accessibility Forms Guidelines: `docs/user-experience/accessibility-forms.md`
- UI Kit & Style Guide: `docs/design-system/README.md`
- Document Cross-Reference Matrix: `docs/Document-Cross-Reference.md`
- Change Log: `docs/audit-trail/ChangeLog.md`

This enhanced API documentation provides a complete, consistent, and intuitive developer experience, enabling rich integrations while maintaining security and performance standards.