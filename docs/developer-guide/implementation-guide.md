# Rotary Club Tunis Doyen CMS Documentation

## Introduction
Welcome to the Rotary Club Tunis Doyen CMS documentation. This guide will help you set up and use the CMS built with Next.js, Payload CMS, and Tailwind CSS. The CMS is designed to manage content for the Rotary Club website with features like internationalization, GDPR compliance, and audit trails.

## Setup Instructions

### 1. <PERSON>lone the Repository
```bash
git clone <repository-url>
cd rotary-club-cms
```

### 2. Install Dependencies
```bash
pnpm install
```

### 3. Configure Environment Variables
Create a `.env` file in the root directory with the following variables:
```env
MONGODB_URI=mongodb://localhost:27017/rotary-club
PAYLOAD_SECRET=your_payload_secret
NEXT_PUBLIC_API_URL=http://localhost:3000
```

**Tip:** Consider adding a `.env.example` file in your repository to list all required environment variables without their values. This helps developers set up their environment correctly and prevents them from accidentally committing their secrets.

Example `.env.example`:
```env
MONGODB_URI=
PAYLOAD_SECRET=
NEXT_PUBLIC_API_URL=
```

### 4. Run the Development Server
```bash
pnpm dev
```

## Content Modeling

### Global Collection
- **Purpose**: Store site-wide settings such as club address, social links, default SEO metadata.
- **Fields**:
  - `siteTitle` (localized text)
  - `contactEmail` (email)
  - `socialLinks` (array of social media links)

### Members Collection
- **Purpose**: Manage member profiles with GDPR compliance.
- **Fields**:
  - `name` (text)
  - `role` (text)
  - `bio` (localized rich text)
  - `contactEmail` (email, GDPR-compliant)
  - `consent` (group for GDPR compliance)
  - `status` (select: 'active', 'anonymized')

### Pages, News, Events Collections
- **Purpose**: Manage content for the website.
- **Fields**:
  - `title` (localized text)
  - `slug` (localized text)
  - `content` (rich text)
  - `featuredImage` (upload)
  - `publishDate` (date)
  - `author` (relationship to Members collection)

## Internationalization (i18n) Workflow

### Localized Fields
Use Payload’s `localized: true` for fields like `title`, `slug`, and `bio`.

Example:
```javascript
{
  name: 'title',
  type: 'text',
  localized: true,
  required: true,
}
```

### Auto-Translation Hook
Use a translation API to auto-translate fields.

Example:
```javascript
beforeChange: [
  async ({ data, req }) => {
    if (data.title?.en && !data.title.fr && !data.title_auto_translated) {
      const translated = await translateText(data.title.en, 'fr');
      data.title.fr = translated;
      data.title_auto_translated = true;
    }
    return data;
  },
],
```

## GDPR Compliance Workflow

### Consent Management
Fields for tracking consent:
```javascript
{
  name: 'consent',
  type: 'group',
  fields: [
    { name: 'given', type: 'checkbox', required: true },
    { name: 'date', type: 'date', defaultValue: () => new Date() },
    {
      name: 'policyVersion',
      type: 'relationship',
      relationTo: 'privacyPolicies',
      required: true,
    },
  ],
}
```

### Anonymization Endpoint
Create a custom endpoint to anonymize member data.

Example:
```javascript
// payload.config.ts
export default buildConfig({
  endpoints: [
    {
      path: '/members/:id/anonymize',
      method: 'post',
      handler: async (req) => {
        const { id } = req.params;
        const anonymizedData = {
          name: 'Anonymous',
          contactEmail: null,
          bio: 'This user has requested anonymization.',
          status: 'anonymized',
        };
        const updatedMember = await req.payload.update({
          collection: 'members',
          id,
          data: anonymizedData,
        });
        return updatedMember;
      },
    },
  ],
});
```

## Audit Trail Implementation

### AuditLogs Collection
Fields:
- `action` (select: 'create', 'update', 'delete', 'anonymize')
- `collection` (text)
- `documentId` (text)
- `user` (relationship to Users collection)
- `changes` (array of changes)

### Deep Diffing Hooks
Use a library like `just-diff` to compare complex fields.

Example:
```javascript
afterChange: [
  async ({ doc, previousDoc, req }) => {
    if (!previousDoc) return;
    const changes = diff(previousDoc, doc); // This returns an array
    if (changes.length > 0) {
      await req.payload.create({
        collection: 'auditLogs',
        data: {
          action: 'update',
          collection: 'members',
          documentId: doc.id,
          user: req.user.id,
          changes: changes, // Store the array of changes directly
        },
      });
    }
  },
],
```

## Security Workflow

### Two-Factor Authentication (2FA)
Use a community plugin like `payload-plugin-2fa`. Enforce 2FA for `Admin` roles.

### API Key Management
Create an `apiKeys` collection to manage scoped API keys.

Example:
```javascript
{
  slug: 'apiKeys',
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'key',
      type: 'text',
      access: {
        create: () => false, // Auto-generated
        read: () => false, // Prevent retrieval after creation
        update: () => false,
      },
      hooks: {
        beforeChange: [generateAPIKey],
      },
    },
    {
      name: 'permissions',
      type: 'select',
      options: ['read', 'write', 'full'],
      defaultValue: 'read',
    },
  ],
}
```

**Note:** The generated API key should only be displayed to the user once upon creation for security reasons. The `read: () => false` access control prevents the key from being retrieved via the API after creation.

## Deployment & Monitoring

### CI/CD Pipeline
Use GitHub Actions for testing and deployment.

Example workflow file:
```yaml
name: CI/CD Pipeline
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: pnpm/action-setup@v2
      - run: pnpm install
      - run: pnpm test
```

### Monitoring
Centralize logs using ELK Stack. Set up alerts for failed logins, GDPR-related actions, and high traffic events.

## Testing Strategies

### Unit Tests
Test individual hooks and endpoints using Vitest.

Example:
```javascript
import { expect, test } from 'vitest';
import { autoGenerateSlug } from './hooks';

test('autoGenerateSlug generates correct slug', () => {
  const title = 'Hello World';
  const slug = autoGenerateSlug(null, title);
  expect(slug).toBe('hello-world');
});
```

### Integration Tests
Test API endpoints and database interactions.

Example:
```javascript
import { test, expect } from '@playwright/test';

test('create member', async ({ request }) => {
  const response = await request.post('/api/members', {
    data: {
      name: 'John Doe',
      role: 'Member',
      bio: 'A new member',
      contactEmail: '<EMAIL>',
      consent: { given: true, policyVersion: '1' },
    },
  });
  expect(response.ok()).toBeTruthy();
});
```

### Mocking in Tests
When testing hooks or endpoints that rely on external services (e.g., translation API), it's best practice to mock these services to avoid making actual API calls, which can be slow and incur costs.

Example:
```javascript
import { expect, test, vi } from 'vitest';
import { translateText } from './hooks';

vi.mock('./hooks', () => ({
  translateText: vi.fn(() => Promise.resolve('Translated Text')),
}));

test('auto-translation hook', async () => {
  const data = { title: { en: 'Hello World' } };
  const result = await beforeChangeHook({ data });
  expect(result.title.fr).toBe('Translated Text');
});
```

## Best Practices

### Code Organization
Keep collections, hooks, and endpoints in separate directories. Use meaningful names for fields and collections.

### Error Handling
Implement proper error handling for hooks and endpoints.

Example:
```javascript
try {
  const translated = await translateText(data.title.en, 'fr');
  data.title.fr = translated;
} catch (error) {
  console.error('Translation failed:', error);
  // Handle error gracefully
}
```

### Performance Optimization
Use lazy loading for images and non-critical sections. Implement caching for frequently accessed data.

### Documentation
Keep documentation up-to-date with code changes. Use comments to explain complex logic.

---

This enhanced documentation should help developers understand and implement the workflow efficiently. If you need further details or specific sections explained, feel free to ask!