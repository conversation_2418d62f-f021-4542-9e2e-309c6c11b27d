# MongoDB Collation Settings for Arabic Language Support

This guide provides instructions for verifying and configuring MongoDB collation settings to properly support Arabic language in the Rotary Club Tunis Doyen CMS.

## What is Collation?

Collation in MongoDB determines the rules for comparing and sorting strings. Proper collation settings are crucial for applications that handle non-English text, especially right-to-left languages like Arabic, to ensure correct alphabetical ordering, case insensitivity, and accent sensitivity.

## Verifying Arabic Collation Settings

### 1. Check Collation Locale and Strength

To verify if your MongoDB collection is configured with the Arabic collation locale and appropriate strength level, run the following command in the MongoDB shell:

```javascript
db.getCollectionInfos({name: "${collection_name}"})[0].options.collation
```

Replace `${collection_name}` with the actual name of your collection (e.g., "members", "pages", etc.).

The expected output should include:

```javascript
{
  "locale": "ar",
  "strength": 2,
  // other collation options may be present
}
```

**Notes:**
- `locale: "ar"` specifies the Arabic language collation rules
- `strength: 2` provides case-insensitive comparison and sorting

### 2. Confirm UTF8MB4 Unicode Encoding

MongoDB uses UTF-8 encoding by default, but you should confirm your application's connection settings are properly configured:

1. Check your MongoDB connection string for any encoding parameters
2. Review your database configuration files
3. For MongoDB Atlas users, encoding is automatically set to UTF-8

### 3. Validate Tunisian Lexical Order Implementation

Tunisian Arabic may have specific lexical ordering requirements. To validate the implementation:

1. **Review Application Sorting Logic**:
   - Examine how your application sorts Arabic text
   - Check for any custom collators or sorting functions

2. **Test with Sample Data**:
   - Create a test collection with Arabic text samples
   - Run the following test to observe sorting behavior:

   ```javascript
   db.createCollection("arabic_test", {
     collation: { locale: "ar", strength: 2 }
   });
   
   db.arabic_test.insertMany([
     { name: "تونس" },
     { name: "الجزائر" },
     { name: "المغرب" },
     { name: "مصر" },
     { name: "ليبيا" }
   ]);
   
   db.arabic_test.find().sort({ name: 1 }).pretty();
   ```

3. **Consult Database Administrator**:
   - For specific Tunisian Arabic collation configurations
   - To verify if any custom collation settings have been applied

## Setting Up Arabic Collation

If your collections don't have the proper Arabic collation settings, you can create or modify collections with the appropriate collation:

### Creating a New Collection with Arabic Collation

```javascript
db.createCollection("collection_name", {
  collation: {
    locale: "ar",
    strength: 2,
    numericOrdering: true
  }
});
```

### For Existing Collections

MongoDB doesn't allow changing collation on existing collections directly. You'll need to:

1. Create a new collection with the desired collation
2. Copy all documents to the new collection
3. Rename collections to replace the old one

```javascript
// Create new collection with Arabic collation
db.createCollection("collection_name_new", {
  collation: {
    locale: "ar",
    strength: 2,
    numericOrdering: true
  }
});

// Copy documents
db.collection_name.find().forEach(function(doc) {
  db.collection_name_new.insert(doc);
});

// Rename collections (backup old one first)
db.collection_name.renameCollection("collection_name_old");
db.collection_name_new.renameCollection("collection_name");
```

### In Payload CMS Configuration

When defining collections in Payload CMS, you can specify MongoDB collation settings:

```javascript
const Members = {
  slug: 'members',
  admin: {
    // admin config
  },
  mongodb: {
    collation: {
      locale: 'ar',
      strength: 2,
      numericOrdering: true
    }
  },
  fields: [
    // fields definition
  ]
};
```

## Troubleshooting

### Common Issues

1. **Incorrect Sorting Order**: If Arabic text isn't sorting correctly, verify the collation settings are applied to the collection and any queries that involve sorting.

2. **Search Not Finding Arabic Text**: Ensure indexes used for text search have the same collation settings as the collection.

3. **Case Sensitivity Problems**: If searches are unexpectedly case-sensitive, check that the strength level is set to 2.

### Testing Collation

You can test your collation settings with:

```javascript
db.collection_name.find().collation({ locale: "ar", strength: 2 }).sort({ arabic_field: 1 });
```

## Additional Resources

- [MongoDB Collation Documentation](https://www.mongodb.com/docs/manual/reference/collation/)
- [Arabic Locale in MongoDB](https://www.mongodb.com/docs/manual/reference/collation-locales-defaults/#ar)
- [Payload CMS Collection Configuration](https://payloadcms.com/docs/configuration/collections)

---

**Note**: The exact implementation of Tunisian lexical order must be validated through testing as documentation may not specify this particular configuration.