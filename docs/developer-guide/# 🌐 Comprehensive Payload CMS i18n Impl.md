# 🌐 Comprehensive Payload CMS i18n Implementation Guide for Arabic RTL Support

Based on your current system state (no RTL implementation), here's the **complete implementation protocol** to achieve full Arabic support in both admin panel and public site. This follows Rotary International standards and addresses the critical gaps identified in your audit.

## 📦 PHASE 1: CORE INSTALLATION & CONFIGURATION

### 1. Install Required Dependencies

```bash
pnpm install @payloadcms/translations tailwindcss-rtl @fontsource/tajawal @fontsource/geist
```

**Purpose of Dependencies**:
- `@payloadcms/translations`: Provides built-in i18n translations for Payload CMS admin interface.
- `tailwindcss-rtl`: Extends Tailwind CSS to support RTL layouts.
- `@fontsource/tajawal`, `@fontsource/geist`: Fonts optimized for Arabic and English content, respectively.

### 2. Configure Payload CMS i18n and Localization (Critical Foundation)

**Important Distinction:**
- **i18n** = Admin interface translation (admin panel, error messages)
- **Localization** = Content translation (data fields per language)

**Key Corrections Made:**
- Replaced non-existent `@payloadcms/plugin-i18n` with `@payloadcms/translations`
- Updated configuration to use Payload's built-in i18n features
- Removed incorrect `rtl: true` from admin meta (not supported by Payload)

```typescript
// src/payload.config.ts
import { buildConfig } from 'payload'
import { en } from '@payloadcms/translations/languages/en'
import { ar } from '@payloadcms/translations/languages/ar'

export default buildConfig({
  // i18n configuration for admin interface translation
  i18n: {
    supportedLanguages: { en, ar },
    fallbackLanguage: 'en',
    translations: {
      en: {
        // Custom translations can be added here
        custom: {
          // Add custom keys as needed
        },
      },
      ar: {
        // Arabic custom translations
        custom: {
          // Add Arabic custom keys as needed
        },
      },
    },
  },

  // Core localization configuration (separate from i18n)
  localization: {
    locales: [
      {
        label: 'English',
        code: 'en'
      },
      {
        label: 'العربية',
        code: 'ar',
        rtl: true, // CRITICAL FOR ARABIC RTL SUPPORT
      },
      {
        label: 'Français',
        code: 'fr',
      }
    ],
    defaultLocale: 'en',
    fallback: true,
  },

  // Admin panel configuration
  admin: {
    meta: {
      titleSuffix: 'Rotary CMS',
    },
    css: './admin.css' // Custom RTL styling
  },

  // Database collation enforcement (FIXES SORTING)
  collections: [
    // Apply to ALL collections needing Arabic support
    withArabicCollation({
      slug: 'members',
      labels: {
        singular: 'Member',
        plural: 'Members'
      },
      fields: [
        // Your fields here
      ]
    }),
    // Repeat for other collections
  ]
})
```

### 3. Database Collation Implementation (Critical for Arabic)

```typescript
// src/payload/collections/configureCollation.ts
import { CollectionConfig } from 'payload/types'
export const withArabicCollation = (
  collection: CollectionConfig
): CollectionConfig => ({
  ...collection,
  hooks: {
    beforeValidate: [
      ({ data, req }) => {
        // Force Arabic collation for Arabic content
        if (req.locale === 'ar' && data.title?.ar) {
          return {
            ...data,
            _collation: { locale: 'ar', strength: 2 }
          }
        }
        return data
      }
    ]
  },
  indexes: [
    ...(collection.indexes || []),
    {
      key: { 'title.ar': 1 },
      name: 'title_ar_collation',
      collation: {
        locale: 'ar',
        strength: 2,
        numericOrdering: true
      }
    }
  ]
})
```

## 🎨 PHASE 2: RTL IMPLEMENTATION FOR BOTH ENVIRONMENTS

### 1. Admin Panel RTL Configuration

```css
/* src/admin.css */
/* Rotary-compliant RTL admin styling */
:root[data-locale="ar"] {
  --payload-colors-background: #fff;
  --payload-colors-text: #333;
  --payload-colors-brand: #006EB5; /* Rotary Blue */
  --payload-colors-highlight: #FCD116; /* Rotary Gold */
}
/* Admin panel RTL overrides */
[data-locale="ar"] .payload-admin {
  direction: rtl;
  text-align: right;
  font-family: 'Tajawal', system-ui, sans-serif;
}
/* Form elements RTL */
[data-locale="ar"] .collection-list .cell {
  text-align: right !important;
}
[data-locale="ar"] .rich-text {
  direction: rtl;
  text-align: right;
  font-family: 'Tajawal', system-ui, sans-serif;
  unicode-bidi: embed; /* Ensures proper handling of mixed text */
}
```

### 2. Next.js App Router Implementation (Recommended)

**Note:** Next.js i18n configuration in `next.config.js` is deprecated for App Router. Use URL-based routing instead:

```typescript
// next.config.js (App Router - no i18n config needed)
import { withPayload } from '@payloadcms/next/withPayload'

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Your other config...
}

export default withPayload(nextConfig)
```

**Alternative for Pages Router (if still using):**
```typescript
// next.config.js (Pages Router only)
const nextConfig = {
  i18n: {
    locales: ['en', 'ar'],
    defaultLocale: 'en',
    localeDetection: true,
  },
}
```

```typescript
// src/app/layout.tsx
export default function RootLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  return (
    <html lang={params.locale} dir={params.locale === 'ar' ? 'rtl' : 'ltr'}>
      <head />
      <body className={params.locale === 'ar' ? 'font-tajawal' : ''}>
        {children}
      </body>
    </html>
  )
}
```

```css
/* src/app/globals.css */
@import '@fontsource/tajawal/400.css';
@import '@fontsource/tajawal/700.css';
@import '@fontsource/geist/400.css';
:root {
  --rotary-blue: #006EB5;
  --rotary-gold: #FCD116;
}
.font-tajawal {
  font-family: 'Tajawal', 'Geist', sans-serif;
}
/* Rotary brand-compliant RTL styling */
html[dir="rtl"] {
  font-family: 'Tajawal', system-ui, sans-serif;
  direction: rtl;
  text-align: right;
}
/* Arabic-specific typography fixes */
.arabic-content {
  line-height: 1.8;
  letter-spacing: -0.02em;
  font-feature-settings: 'liga' 1, 'calt' 1, 'rlig' 1;
}
/* Standard numerals for Tunisia */
html[lang="ar"] {
  font-variant-numeric: lining-nums;
}
```

## ✍️ PHASE 3: LEXICAL EDITOR ARABIC RENDERING FIXES

### 1. Custom Arabic Node Implementation

```typescript
// src/payload/lexical/ArabicNode.ts
import { ElementNode, LexicalNode } from 'lexical'
export class ArabicParagraphNode extends ElementNode {
  static getType(): string {
    return 'arabic-paragraph'
  }
  static clone(node: ArabicParagraphNode): ArabicParagraphNode {
    return new ArabicParagraphNode(node.__key)
  }
  createDOM(): HTMLElement {
    const dom = document.createElement('p')
    dom.dir = 'rtl'
    dom.style.fontFamily = 'Tajawal, Geist'
    dom.style.unicodeBidi = 'embed'
    dom.style.lineHeight = '1.8'
    return dom
  }
  updateDOM(): boolean {
    return true
  }
}
// Register custom node
export const lexicalConfig = {
  nodes: [ArabicParagraphNode],
  theme: {
    ltr: 'text-left',
    rtl: 'text-right font-tajawal arabic-content'
  }
}
```

### 2. Register Custom Nodes with Payload

```typescript
// src/payload/lexical/index.ts
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { ArabicParagraphNode } from './ArabicNode'
export const lexicalEditorConfig = lexicalEditor({
  features: ({ defaultFeatures }) => [
    ...defaultFeatures,
    // Add custom Arabic node
    Feature(({ rootEditor }) => {
      rootEditor.registerNode(ArabicParagraphNode)
      return {
        paragraph: {
          node: 'arabic-paragraph',
          priority: 1,
        }
      }
    })
  ]
})
```

## 🧪 PHASE 4: VALIDATION & TESTING PROTOCOL

### 1. MongoDB Collation Verification

```bash
# Connect to your MongoDB instance
mongo
# Run collation test
use your_database
db.members.find().collation({ locale: 'ar', strength: 2 }).sort({ name: 1 })
# Expected output (Tunisian lexical order):
# أحمد → إبراهيم → بدر
```

### 2. Admin Panel RTL Verification

1. Log in to admin panel
2. Switch to Arabic locale (should appear in top-right)
3. Verify:
   - Entire UI direction is RTL
   - Text alignment is right
   - Navigation menus appear on right side
   - Form labels are right-aligned
   - Icons are mirrored (e.g., chevron points left)
   - Numerals are in standard format (123)

### 3. Public Site Arabic Validation Checklist

| Test Case               | Verification Method      | Expected Result               |
|-------------------------|--------------------------|-------------------------------|
| Route Structure         | Visit `/ar/members`      | Properly loads Arabic content |
| Font Rendering          | Inspect page elements    | Tajawal font applied          |
| Diacritic Support       | Check words with diacritics | `جَزَائِر` renders correctly |
| Number Direction        | View Arabic numbers      | Standard numerals (123)       |
| Sorting Order           | View member directory    | Sorted in Tunisian lexical order |
| Content Direction       | Inspect HTML             | `dir="rtl"` on html tag       |

### 4. Automated Testing Scripts

**Example: Jest Test for Numeral Handling**

```javascript
// __tests__/numeralHandling.test.js
describe('Arabic numeral handling', () => {
  it('should display standard numerals in Arabic locale', () => {
    // Mock or set up your environment to render components in Arabic locale
    render(<YourComponent locale="ar" />);
    expect(screen.getByText('123')).toBeInTheDocument();
  });
});
```

## ⚠️ CRITICAL IMPLEMENTATION NOTES

1. **Database Collation is Non-Negotiable**
   Without MongoDB collation `{ locale: 'ar', strength: 2 }`, Arabic sorting will fail regardless of frontend implementation.

2. **Rotary Brand Compliance Requirements**
   - Font pairing: Tajawal (Arabic) + Geist (English)
   - Color scheme: Rotary Blue (#006EB5) and Gold (#FCD116)
   - Text alignment: Right-aligned for all Arabic content
   - Numerals: Standard numerals (123) for Tunisia

3. **Lexical Editor Critical Fixes**
   The default Lexical configuration **does not properly handle**:
   - Arabic diacritic stacking
   - Proper word joining
   - Standard numerals for Tunisia (123)
   - Punctuation direction

4. **Deployment Strategy**
    Use this App Router approach for Next.js 15:

```typescript
// src/app/[locale]/page.tsx
export async function generateStaticParams() {
  return [{ locale: 'en' }, { locale: 'ar' }]
}

export default function Page({ params }: { params: { locale: string } }) {
  // Your page content
  return (
    <div>
      <h1>Current locale: {params.locale}</h1>
      {/* Your localized content */}
    </div>
  )
}
```

**For Payload Content:**
```typescript
// src/app/[locale]/page.tsx
import { getPayload } from 'payload'
import { cache } from 'react'

const getPage = cache(async (locale: string) => {
  const payload = await getPayload()
  const page = await payload.find({
    collection: 'pages',
    locale: locale,
    limit: 1,
  })
  return page.docs[0]
})

export default async function Page({ params }: { params: { locale: string } }) {
  const page = await getPage(params.locale)

  return (
    <div>
      <h1>{page.title}</h1>
      {/* Your page content */}
    </div>
  )
}
```

## 📎 APPENDIX: ROTARY COMPLIANCE RESOURCES

### RTL Implementation Verification Flow

```mermaid
graph TD
  A[Database Collation Check] -->|Pass| B[Payload RTL Config]
  B -->|Validated| C[Lexical Arabic Patch]
  C -->|Tested| D[Arabic UAT]
  D -->|Approved| E[Rotary Compliance Sign-off]
  A -->|Fail| F[Block Deployment]
```

### Arabic-Specific Test Cases (Mandatory for UAT)

```gherkin
Scenario: Arabic content rendering
  Given user selects Arabic locale
  When viewing member directory
  Then all text containers have dir="rtl"
  And Tajawal font is applied
  And diacritics render correctly (ً ٌ ٍ َ ُ ِ ّ)
  And sorting follows Tunisian lexical order
  And numbers use standard format (123)
```

### Performance Optimization Recommendations

1. **Database Indexing**:
   - Create indexes on frequently queried fields, especially those used in sorting and filtering.
   - Example:
     ```javascript
     indexes: [
       {
         key: { 'title.ar': 1 },
         name: 'title_ar_index',
         collation: { locale: 'ar', strength: 2 }
       }
     ]
     ```

2. **Caching**:
   - Use Redis or Memcached to cache frequently accessed data.
   - Implement caching for API responses, especially for public pages.

3. **Load Testing**:
   - Use tools like Apache JMeter or k6 to simulate high traffic and monitor performance.

4. **Content Delivery Network (CDN)**:
   - Use a CDN to serve static assets and improve load times globally.

### Security Best Practices

1. **API Key Management**:
   - Store API keys securely using environment variables or secret management tools.
   - Rotate API keys regularly and monitor their usage.

2. **User Permissions**:
   - Implement role-based access control (RBAC) to restrict access to sensitive data and functionalities.
   - Regularly audit user permissions and access logs.

3. **Regular Updates**:
   - Keep your dependencies up-to-date to patch vulnerabilities.
   - Schedule regular security audits and penetration testing.