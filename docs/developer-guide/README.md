# Developer Guide

## 📧 Email Configuration (Resend API)

### Overview
This project uses **Resend API** as the email provider for Payload CMS. The email service is configured to handle various automated communications including user notifications, form submissions, and admin communications.

### Configuration Details

**Email Provider**: Resend API
**Set as Default**: `<EMAIL>`
**Default Sender Name**: `Rotary Tunis Doyen`

### Environment Variables
The following environment variable must be configured:

```env
RESEND_API_KEY=your-resend-api-key-here
```

### Setup Instructions
1. Install dependencies (already completed):
   ```bash
   pnpm add resend @payloadcms/email-resend
   ```

2. Configure API key in your environment:
   - Develop locally: Add to `.env` file
   - Production: Set in your hosting environment

2. Email configuration in `src/payload.config.ts`:
   ```typescript
   email: resendAdapter({
     defaultFromAddress: '<EMAIL>',
     defaultFromName: 'Rotary Tunis Doyen',
     apiKey: process.env.RESEND_API_KEY as string,
   })
   ```

### Usage Examples

Payload CMS will automatically use this email configuration for:
- User account creation/verification emails
- Form submissions
- Password reset emails
- Custom admin notifications

### Troubleshooting
- Verify RESEND_API_KEY is set correctly
- Check email delivery logs in Resend dashboard
- Ensure SMTP fallback is configured if needed

---

## Contents

### Internationalization (i18n)
- [Comprehensive Payload CMS i18n Implementation](./# 🌐 Comprehensive Payload CMS i18n Impl.md)

### Database
- [MongoDB Arabic Collation Setup](./mongodb-arabic-collation.md)

### Implementation
- [General Implementation Guide](./implementation-guide.md)

---

*Last Updated: August 2025*
*Email Configuration Version: 1.0*
