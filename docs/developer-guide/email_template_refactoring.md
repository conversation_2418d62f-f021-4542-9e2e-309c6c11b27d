# Email Templating System Refactoring Strategy

## 1. Current State Assessment

The existing system implements a **hybrid email templating architecture** with several architectural pain points:

**Current Architecture:**
- **Custom placeholder system** using `{variableName}` syntax with regex-based interpolation
- **Dual template storage**: Hardcoded templates in `emailTemplates.ts` with database fallback via `EmailTemplates` collection
- **Manual HTML construction** with inline styles embedded directly in template strings
- **Basic sanitization** using `sanitize-html` with incomplete TypeScript configuration
- **Multilingual support** through separate template objects for English, French, and Arabic

**Key Pain Points:**
1. **Maintenance burden**: Templates are hardcoded as large string literals, making them difficult to maintain and version
2. **Limited templating features**: The custom `{variableName}` system lacks conditionals, loops, and advanced formatting
3. **TypeScript integration issues**: The `allowedStyles` configuration was removed due to type errors, reducing security
4. **Scalability concerns**: Adding new template types requires code changes rather than CMS configuration
5. **Testing complexity**: No dedicated testing infrastructure for template rendering and email delivery

**Root Cause Analysis:**
The `sanitize-html` TypeScript error stems from the library's type definitions expecting specific string literal types for CSS properties, but the current configuration attempts to use regex patterns which aren't properly typed.

## 2. Strategic Refactoring Approach

**Recommended Architecture: Component-Based Templating with React Email**

After analyzing three approaches (full CMS-driven, component-based templating, external service), I recommend **React Email with enhanced CMS integration** for the following reasons:

**Cost-Benefit Analysis:**

| Approach | Development Velocity | Maintainability | Testing Complexity | Team Expertise |
|----------|---------------------|-----------------|-------------------|----------------|
| Full CMS-driven | Medium | High | Medium | Low requirement |
| **React Email** | **High** | **Very High** | **Low** | **Medium** |
| External Service | Low | Medium | High | High requirement |

**Justification:**
- **Development Velocity**: React Email provides TypeScript-first components with excellent DX
- **Long-term Maintainability**: Component-based templates are easier to version, test, and refactor
- **Testing**: Built-in preview and testing capabilities
- **Team Expertise**: Leverages existing React knowledge while introducing manageable complexity

**Migration Strategy:**
1. **Parallel Implementation**: Build new system alongside existing one
2. **Gradual Migration**: Migrate templates one type at a time
3. **Backward Compatibility**: Maintain existing API contracts during transition
4. **Feature Parity**: Ensure all current functionality is preserved

## 3. Technical Implementation Plan

### Templating Engine Selection: React Email

**Rationale:**
- **TypeScript Support**: First-class TypeScript support with proper type inference
- **Performance**: Compiles to optimized HTML with minimal runtime overhead
- **Security**: Built-in XSS prevention and content sanitization
- **Ecosystem**: Rich component library and excellent tooling

**Template Syntax Comparison:**

```
`typescript path=src/utilities/emailTemplates.ts mode=EXCERPT
// Current System
html: `
  <div style="font-family: Arial, sans-serif;">
    <h1>Hello {userName}</h1>
    <p>Event: {eventTitle}</p>
  </div>
`
````

```typescript
// Proposed React Email System
import { Html, Head, Body, Container, Heading, Text } from '@react-email/components'

interface RegistrationEmailProps {
  userName: string
  eventTitle: string
  eventDate: string
  eventLocation: string
}

export const RegistrationEmail = ({ userName, eventTitle, eventDate, eventLocation }: RegistrationEmailProps) => (
  <Html>
    <Head />
    <Body style={bodyStyle}>
      <Container style={containerStyle}>
        <Heading style={headingStyle}>Hello {userName}</Heading>
        <Text>Event: {eventTitle}</Text>
        <Text>Date: {eventDate}</Text>
        <Text>Location: {eventLocation}</Text>
      </Container>
    </Body>
  </Html>
)
```

### Security & Sanitization Configuration

**Complete TypeScript Configuration for sanitize-html:**

```typescript
import sanitizeHtml, { IOptions } from 'sanitize-html'

// Properly typed sanitization configuration
const sanitizeOptions: IOptions = {
  allowedTags: [
    'div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'strong', 'em', 'ul', 'ol', 'li', 'a', 'br', 'hr',
    'table', 'thead', 'tbody', 'tr', 'th', 'td', 'img'
  ],
  allowedAttributes: {
    '*': ['style', 'class'],
    'a': ['href', 'name', 'target'],
    'img': ['src', 'alt', 'width', 'height'],
    'table': ['width', 'cellpadding', 'cellspacing', 'border']
  },
  allowedStyles: {
    '*': {
      'color': [/^#[0-9a-fA-F]{3,6}$/, /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/],
      'font-size': [/^\d+(?:px|em|rem|%)$/],
      'font-weight': [/^(?:normal|bold|bolder|lighter|\d{3})$/],
      'text-align': [/^(?:left|right|center|justify)$/],
      'margin': [/^\d+(?:px|em|rem|%)(?:\s+\d+(?:px|em|rem|%)){0,3}$/],
      'padding': [/^\d+(?:px|em|rem|%)(?:\s+\d+(?:px|em|rem|%)){0,3}$/],
      'background-color': [/^#[0-9a-fA-F]{3,6}$/, /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/]
    }
  },
  allowedSchemes: ['http', 'https', 'mailto'],
  allowedSchemesByTag: {
    img: ['http', 'https', 'data']
  }
}

// Type-safe sanitization function
export const sanitizeEmailContent = (html: string): string => {
  return sanitizeHtml(html, sanitizeOptions)
}
```

**TypeScript Error Resolution:**
The error occurs because `sanitize-html` v2.17.0 expects `allowedStyles` to use specific type patterns. The solution involves:

1. **Proper type imports**: Import `IOptions` interface
2. **Regex patterns**: Use properly typed regex patterns for CSS validation
3. **Type assertion**: Use type-safe configuration objects

### Template Management Architecture

**New Template Storage System:**

```typescript
// Enhanced EmailTemplate interface
interface EmailTemplateConfig {
  id: string
  name: string
  type: EmailTemplateType
  language: SupportedLanguage
  version: string
  component: React.ComponentType<any>
  props: Record<string, any>
  metadata: {
    createdAt: Date
    updatedAt: Date
    createdBy: string
    tags: string[]
  }
}

// Template registry with version control
class EmailTemplateRegistry {
  private templates = new Map<string, EmailTemplateConfig>()
  
  register(config: EmailTemplateConfig): void {
    const key = `${config.type}-${config.language}-${config.version}`
    this.templates.set(key, config)
  }
  
  async render(type: string, language: string, props: Record<string, any>): Promise<string> {
    const template = this.getLatestTemplate(type, language)
    if (!template) throw new Error(`Template not found: ${type}-${language}`)
    
    return await renderEmailComponent(template.component, props)
  }
  
  private getLatestTemplate(type: string, language: string): EmailTemplateConfig | undefined {
    // Implementation for version resolution
  }
}
```

**Multilingual Template Structure:**

```typescript
// Language-specific template organization
const templateStructure = {
  'registration_confirmation': {
    'en': RegistrationConfirmationEN,
    'fr': RegistrationConfirmationFR,
    'ar': RegistrationConfirmationAR
  },
  'organizer_notification': {
    'en': OrganizerNotificationEN,
    'fr': OrganizerNotificationFR,
    'ar': OrganizerNotificationAR
  }
}

// RTL support for Arabic templates
const arabicStyles = {
  direction: 'rtl' as const,
  textAlign: 'right' as const,
  fontFamily: 'Tajawal, Arial, sans-serif'
}
```

**Migration Path:**

1. **Phase 1**: Install React Email and create component versions of existing templates
2. **Phase 2**: Implement template registry and rendering system
3. **Phase 3**: Update email service to use new system with fallback
4. **Phase 4**: Migrate CMS integration and remove old templates

## 4. Implementation Roadmap

**Phase 1: Foundation Setup (Week 1-2)**
- Install React Email dependencies (`@react-email/components`, `@react-email/render`)
- Create base template components for existing email types
- Set up development preview environment
- **Effort**: 16-20 hours
- **Blockers**: Learning curve for React Email patterns

**Phase 2: Core System Implementation (Week 3-4)**
- Implement template registry and rendering engine
- Create TypeScript-safe sanitization configuration
- Build template versioning system
- **Effort**: 24-32 hours
- **Blockers**: Complex version resolution logic

**Phase 3: Integration & Migration (Week 5-6)**
- Update email service to use new system
- Implement CMS integration for template management
- Create migration scripts for existing templates
- **Effort**: 20-24 hours
- **Blockers**: Data migration complexity

**Phase 4: Testing & Optimization (Week 7-8)**
- Comprehensive testing suite implementation
- Performance optimization and caching
- Documentation and team training
- **Effort**: 16-20 hours
- **Blockers**: Email client compatibility testing

**Success Criteria:**
- All existing email functionality preserved
- 50% reduction in template maintenance time
- 100% TypeScript type safety
- Zero security vulnerabilities in sanitization

**Rollback Strategy:**
- Feature flags for gradual rollout
- Parallel system operation during transition
- Automated rollback triggers based on error rates

## 5. Testing & Quality Assurance

**Comprehensive Testing Strategy:**

```typescript
// Template rendering tests
describe('Email Template Rendering', () => {
  it('should render registration confirmation with all variables', async () => {
    const props = {
      userName: 'John Doe',
      eventTitle: 'Rotary Meeting',
      eventDate: '2024-01-15',
      eventLocation: 'Community Center'
    }
    
    const html = await renderTemplate('registration_confirmation', 'en', props)
    
    expect(html).toContain('John Doe')
    expect(html).toContain('Rotary Meeting')
    expect(html).toMatchSnapshot()
  })
  
  it('should handle missing variables gracefully', async () => {
    const props = { userName: 'John Doe' }
    
    const html = await renderTemplate('registration_confirmation', 'en', props)
    
    expect(html).not.toContain('{eventTitle}')
    expect(html).toContain('John Doe')
  })
})

// Email delivery integration tests
describe('Email Delivery Integration', () => {
  it('should send registration confirmation email', async () => {
    const mockSendEmail = vi.fn()
    vi.mocked(sendEmail).mockImplementation(mockSendEmail)
    
    await sendRegistrationConfirmation({
      userEmail: '<EMAIL>',
      userName: 'Test User',
      eventTitle: 'Test Event'
    })
    
    expect(mockSendEmail).toHaveBeenCalledWith({
      to: '<EMAIL>',
      subject: expect.stringContaining('Test Event'),
      html: expect.stringContaining('Test User')
    })
  })
})
```

**Visual Regression Testing:**

```typescript
// Email client compatibility tests
describe('Email Client Compatibility', () => {
  const clients = ['gmail', 'outlook', 'apple-mail', 'thunderbird']
  
  clients.forEach(client => {
    it(`should render correctly in ${client}`, async () => {
      const html = await renderTemplate('registration_confirmation', 'en', testProps)
      const screenshot = await captureEmailScreenshot(html, client)
      
      expect(screenshot).toMatchImageSnapshot({
        customDiffConfig: { threshold: 0.1 },
        customSnapshotIdentifier: `registration-${client}`
      })
    })
  })
})
```

**Performance Benchmarking:**

```typescript
// Template rendering performance tests
describe('Performance Benchmarks', () => {
  it('should render templates within acceptable time limits', async () => {
    const startTime = performance.now()
    
    await Promise.all([
      renderTemplate('registration_confirmation', 'en', testProps),
      renderTemplate('organizer_notification', 'fr', testProps),
      renderTemplate('registration_confirmation', 'ar', testProps)
    ])
    
    const endTime = performance.now()
    expect(endTime - startTime).toBeLessThan(500) // 500ms threshold
  })
})
```

**Testing Framework Integration:**
- **Unit Tests**: Vitest with React Testing Library
- **Integration Tests**: Supertest for API endpoints
- **Visual Tests**: Playwright with email client emulation
- **Performance Tests**: Vitest with custom performance matchers

## 6. Maintenance & Operations

**Template Management Processes:**

1. **Adding New Templates:**
   ```typescript
// 1. Create React component
   export const NewTemplateEN = ({ prop1, prop2 }: Props) => (
     <EmailLayout>
       <Heading>{prop1}</Heading>
       <Text>{prop2}</Text>
     </EmailLayout>
   )
   
   // 2. Register in template registry
   templateRegistry.register({
     type: 'new_template',
     language: 'en',
     component: NewTemplateEN,
     version: '1.0.0'
   })
   
   // 3. Add to CMS configuration
   // 4. Create tests
```

2. **Code Review Guidelines:**
   - All template changes require visual preview approval
   - Accessibility compliance verification (WCAG 2.1 AA)
   - Cross-client compatibility testing
   - Performance impact assessment

**Monitoring & Alerting:**

```typescript
// Email system health monitoring
class EmailSystemMonitor {
  async checkTemplateHealth(): Promise<HealthStatus> {
    const results = await Promise.allSettled([
      this.validateTemplateRendering(),
      this.checkSanitizationRules(),
      this.verifyMultilingualSupport()
    ])
    
    return this.aggregateHealthStatus(results)
  }
  
  async logTemplateUsage(templateType: string, language: string): Promise<void> {
    // Analytics and usage tracking
  }
}
```

**Troubleshooting Procedures:**

1. **Template Rendering Failures:**
   - Check component props validation
   - Verify template registry configuration
   - Review sanitization rule conflicts

2. **Email Delivery Issues:**
   - Validate SMTP configuration
   - Check rate limiting and quotas
   - Review email content for spam triggers

3. **Performance Degradation:**
   - Monitor template rendering times
   - Check database query performance
   - Review caching effectiveness

**Documentation Requirements:**
- Template development guide with examples
- CMS user manual for template management
- API documentation for email service endpoints
- Troubleshooting runbook for common issues

---

## Summary & Next Steps

**Key Decisions:**
1. **React Email** as the primary templating engine for type safety and maintainability
2. **Hybrid storage** approach maintaining CMS flexibility with component-based templates
3. **Gradual migration** strategy to minimize risk and ensure continuity
4. **Comprehensive testing** strategy including visual regression and performance benchmarks

**Immediate Next Steps:**
1. Install React Email dependencies: `npm install @react-email/components @react-email/render`
2. Create proof-of-concept registration confirmation template
3. Set up development preview environment
4. Begin Phase 1 implementation following the roadmap

**Success Metrics:**
- 50% reduction in template maintenance time
- 100% TypeScript type coverage
- Zero security vulnerabilities
- 99.9% email delivery success rate

This refactoring strategy provides a robust foundation for scalable, maintainable email templating while preserving all existing functionality and improving developer experience significantly.
I'll analyze the current email templating system in your rotary-cms codebase and provide a comprehensive refactoring strategy. Let me start by examining the existing implementation.

Now let me examine the package.json and TypeScript configuration to understand the current dependencies and constraints:

Now let me check the TypeScript configuration and examine any existing TypeScript errors:

Let me check for any existing TypeScript errors and examine the current sanitize-html usage:

Now let me examine the current testing setup to understand the testing patterns:

Let me examine one of the existing test files to understand the testing patterns, then check the vitest configuration:

Now let me check the vitest configuration:

Based on my comprehensive analysis of the rotary-cms email templating system, I'll now provide the detailed refactoring strategy you requested.

# Email Templating System Refactoring Strategy

## 1. Current State Assessment

The existing system implements a **hybrid email templating architecture** with several architectural pain points:

**Current Architecture:**
- **Custom placeholder system** using `{variableName}` syntax with regex-based interpolation
- **Dual template storage**: Hardcoded templates in `emailTemplates.ts` with database fallback via `EmailTemplates` collection
- **Manual HTML construction** with inline styles embedded directly in template strings
- **Basic sanitization** using `sanitize-html` with incomplete TypeScript configuration
- **Multilingual support** through separate template objects for English, French, and Arabic

**Key Pain Points:**
1. **Maintenance burden**: Templates are hardcoded as large string literals, making them difficult to maintain and version
2. **Limited templating features**: The custom `{variableName}` system lacks conditionals, loops, and advanced formatting
3. **TypeScript integration issues**: The `allowedStyles` configuration was removed due to type errors, reducing security
4. **Scalability concerns**: Adding new template types requires code changes rather than CMS configuration
5. **Testing complexity**: No dedicated testing infrastructure for template rendering and email delivery

**Root Cause Analysis:**
The `sanitize-html` TypeScript error stems from the library's type definitions expecting specific string literal types for CSS properties, but the current configuration attempts to use regex patterns which aren't properly typed.

## 2. Strategic Refactoring Approach

**Recommended Architecture: Component-Based Templating with React Email**

After analyzing three approaches (full CMS-driven, component-based templating, external service), I recommend **React Email with enhanced CMS integration** for the following reasons:

**Cost-Benefit Analysis:**

| Approach | Development Velocity | Maintainability | Testing Complexity | Team Expertise |
|----------|---------------------|-----------------|-------------------|----------------|
| Full CMS-driven | Medium | High | Medium | Low requirement |
| **React Email** | **High** | **Very High** | **Low** | **Medium** |
| External Service | Low | Medium | High | High requirement |

**Justification:**
- **Development Velocity**: React Email provides TypeScript-first components with excellent DX
- **Long-term Maintainability**: Component-based templates are easier to version, test, and refactor
- **Testing**: Built-in preview and testing capabilities
- **Team Expertise**: Leverages existing React knowledge while introducing manageable complexity

**Migration Strategy:**
1. **Parallel Implementation**: Build new system alongside existing one
2. **Gradual Migration**: Migrate templates one type at a time
3. **Backward Compatibility**: Maintain existing API contracts during transition
4. **Feature Parity**: Ensure all current functionality is preserved

## 3. Technical Implementation Plan

### Templating Engine Selection: React Email

**Rationale:**
- **TypeScript Support**: First-class TypeScript support with proper type inference
- **Performance**: Compiles to optimized HTML with minimal runtime overhead
- **Security**: Built-in XSS prevention and content sanitization
- **Ecosystem**: Rich component library and excellent tooling

**Template Syntax Comparison:**

````typescript path=src/utilities/emailTemplates.ts mode=EXCERPT
// Current System
html: `
  <div style="font-family: Arial, sans-serif;">
    <h1>Hello {userName}</h1>
    <p>Event: {eventTitle}</p>
  </div>
`
````

```typescript
// Proposed React Email System
import { Html, Head, Body, Container, Heading, Text } from '@react-email/components'

interface RegistrationEmailProps {
  userName: string
  eventTitle: string
  eventDate: string
  eventLocation: string
}

export const RegistrationEmail = ({ userName, eventTitle, eventDate, eventLocation }: RegistrationEmailProps) => (
  <Html>
    <Head />
    <Body style={bodyStyle}>
      <Container style={containerStyle}>
        <Heading style={headingStyle}>Hello {userName}</Heading>
        <Text>Event: {eventTitle}</Text>
        <Text>Date: {eventDate}</Text>
        <Text>Location: {eventLocation}</Text>
      </Container>
    </Body>
  </Html>
)
```

### Security & Sanitization Configuration

**Complete TypeScript Configuration for sanitize-html:**

```typescript
import sanitizeHtml, { IOptions } from 'sanitize-html'

// Properly typed sanitization configuration
const sanitizeOptions: IOptions = {
  allowedTags: [
    'div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'strong', 'em', 'ul', 'ol', 'li', 'a', 'br', 'hr',
    'table', 'thead', 'tbody', 'tr', 'th', 'td', 'img'
  ],
  allowedAttributes: {
    '*': ['style', 'class'],
    'a': ['href', 'name', 'target'],
    'img': ['src', 'alt', 'width', 'height'],
    'table': ['width', 'cellpadding', 'cellspacing', 'border']
  },
  allowedStyles: {
    '*': {
      'color': [/^#[0-9a-fA-F]{3,6}$/, /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/],
      'font-size': [/^\d+(?:px|em|rem|%)$/],
      'font-weight': [/^(?:normal|bold|bolder|lighter|\d{3})$/],
      'text-align': [/^(?:left|right|center|justify)$/],
      'margin': [/^\d+(?:px|em|rem|%)(?:\s+\d+(?:px|em|rem|%)){0,3}$/],
      'padding': [/^\d+(?:px|em|rem|%)(?:\s+\d+(?:px|em|rem|%)){0,3}$/],
      'background-color': [/^#[0-9a-fA-F]{3,6}$/, /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/]
    }
  },
  allowedSchemes: ['http', 'https', 'mailto'],
  allowedSchemesByTag: {
    img: ['http', 'https', 'data']
  }
}

// Type-safe sanitization function
export const sanitizeEmailContent = (html: string): string => {
  return sanitizeHtml(html, sanitizeOptions)
}
```

**TypeScript Error Resolution:**
The error occurs because `sanitize-html` v2.17.0 expects `allowedStyles` to use specific type patterns. The solution involves:

1. **Proper type imports**: Import `IOptions` interface
2. **Regex patterns**: Use properly typed regex patterns for CSS validation
3. **Type assertion**: Use type-safe configuration objects

### Template Management Architecture

**New Template Storage System:**

```typescript
// Enhanced EmailTemplate interface
interface EmailTemplateConfig {
  id: string
  name: string
  type: EmailTemplateType
  language: SupportedLanguage
  version: string
  component: React.ComponentType<any>
  props: Record<string, any>
  metadata: {
    createdAt: Date
    updatedAt: Date
    createdBy: string
    tags: string[]
  }
}

// Template registry with version control
class EmailTemplateRegistry {
  private templates = new Map<string, EmailTemplateConfig>()
  
  register(config: EmailTemplateConfig): void {
    const key = `${config.type}-${config.language}-${config.version}`
    this.templates.set(key, config)
  }
  
  async render(type: string, language: string, props: Record<string, any>): Promise<string> {
    const template = this.getLatestTemplate(type, language)
    if (!template) throw new Error(`Template not found: ${type}-${language}`)
    
    return await renderEmailComponent(template.component, props)
  }
  
  private getLatestTemplate(type: string, language: string): EmailTemplateConfig | undefined {
    // Implementation for version resolution
  }
}
```

**Multilingual Template Structure:**

```typescript
// Language-specific template organization
const templateStructure = {
  'registration_confirmation': {
    'en': RegistrationConfirmationEN,
    'fr': RegistrationConfirmationFR,
    'ar': RegistrationConfirmationAR
  },
  'organizer_notification': {
    'en': OrganizerNotificationEN,
    'fr': OrganizerNotificationFR,
    'ar': OrganizerNotificationAR
  }
}

// RTL support for Arabic templates
const arabicStyles = {
  direction: 'rtl' as const,
  textAlign: 'right' as const,
  fontFamily: 'Tajawal, Arial, sans-serif'
}
```

**Migration Path:**

1. **Phase 1**: Install React Email and create component versions of existing templates
2. **Phase 2**: Implement template registry and rendering system
3. **Phase 3**: Update email service to use new system with fallback
4. **Phase 4**: Migrate CMS integration and remove old templates

## 4. Implementation Roadmap

**Phase 1: Foundation Setup (Week 1-2)**
- Install React Email dependencies (`@react-email/components`, `@react-email/render`)
- Create base template components for existing email types
- Set up development preview environment
- **Effort**: 16-20 hours
- **Blockers**: Learning curve for React Email patterns

**Phase 2: Core System Implementation (Week 3-4)**
- Implement template registry and rendering engine
- Create TypeScript-safe sanitization configuration
- Build template versioning system
- **Effort**: 24-32 hours
- **Blockers**: Complex version resolution logic

**Phase 3: Integration & Migration (Week 5-6)**
- Update email service to use new system
- Implement CMS integration for template management
- Create migration scripts for existing templates
- **Effort**: 20-24 hours
- **Blockers**: Data migration complexity

**Phase 4: Testing & Optimization (Week 7-8)**
- Comprehensive testing suite implementation
- Performance optimization and caching
- Documentation and team training
- **Effort**: 16-20 hours
- **Blockers**: Email client compatibility testing

**Success Criteria:**
- All existing email functionality preserved
- 50% reduction in template maintenance time
- 100% TypeScript type safety
- Zero security vulnerabilities in sanitization

**Rollback Strategy:**
- Feature flags for gradual rollout
- Parallel system operation during transition
- Automated rollback triggers based on error rates

## 5. Testing & Quality Assurance

**Comprehensive Testing Strategy:**

```typescript
// Template rendering tests
describe('Email Template Rendering', () => {
  it('should render registration confirmation with all variables', async () => {
    const props = {
      userName: 'John Doe',
      eventTitle: 'Rotary Meeting',
      eventDate: '2024-01-15',
      eventLocation: 'Community Center'
    }
    
    const html = await renderTemplate('registration_confirmation', 'en', props)
    
    expect(html).toContain('John Doe')
    expect(html).toContain('Rotary Meeting')
    expect(html).toMatchSnapshot()
  })
  
  it('should handle missing variables gracefully', async () => {
    const props = { userName: 'John Doe' }
    
    const html = await renderTemplate('registration_confirmation', 'en', props)
    
    expect(html).not.toContain('{eventTitle}')
    expect(html).toContain('John Doe')
  })
})

// Email delivery integration tests
describe('Email Delivery Integration', () => {
  it('should send registration confirmation email', async () => {
    const mockSendEmail = vi.fn()
    vi.mocked(sendEmail).mockImplementation(mockSendEmail)
    
    await sendRegistrationConfirmation({
      userEmail: '<EMAIL>',
      userName: 'Test User',
      eventTitle: 'Test Event'
    })
    
    expect(mockSendEmail).toHaveBeenCalledWith({
      to: '<EMAIL>',
      subject: expect.stringContaining('Test Event'),
      html: expect.stringContaining('Test User')
    })
  })
})
```

**Visual Regression Testing:**

```typescript
// Email client compatibility tests
describe('Email Client Compatibility', () => {
  const clients = ['gmail', 'outlook', 'apple-mail', 'thunderbird']
  
  clients.forEach(client => {
    it(`should render correctly in ${client}`, async () => {
      const html = await renderTemplate('registration_confirmation', 'en', testProps)
      const screenshot = await captureEmailScreenshot(html, client)
      
      expect(screenshot).toMatchImageSnapshot({
        customDiffConfig: { threshold: 0.1 },
        customSnapshotIdentifier: `registration-${client}`
      })
    })
  })
})
```

**Performance Benchmarking:**

```typescript
// Template rendering performance tests
describe('Performance Benchmarks', () => {
  it('should render templates within acceptable time limits', async () => {
    const startTime = performance.now()
    
    await Promise.all([
      renderTemplate('registration_confirmation', 'en', testProps),
      renderTemplate('organizer_notification', 'fr', testProps),
      renderTemplate('registration_confirmation', 'ar', testProps)
    ])
    
    const endTime = performance.now()
    expect(endTime - startTime).toBeLessThan(500) // 500ms threshold
  })
})
```

**Testing Framework Integration:**
- **Unit Tests**: Vitest with React Testing Library
- **Integration Tests**: Supertest for API endpoints
- **Visual Tests**: Playwright with email client emulation
- **Performance Tests**: Vitest with custom performance matchers

## 6. Maintenance & Operations

**Template Management Processes:**

1. **Adding New Templates:**
   ```typescript
   // 1. Create React component
   export const NewTemplateEN = ({ prop1, prop2 }: Props) => (
     <EmailLayout>
       <Heading>{prop1}</Heading>
       <Text>{prop2}</Text>
     </EmailLayout>
   )
   
   // 2. Register in template registry
   templateRegistry.register({
     type: 'new_template',
     language: 'en',
     component: NewTemplateEN,
     version: '1.0.0'
   })
   
   // 3. Add to CMS configuration
   // 4. Create tests
   ```

2. **Code Review Guidelines:**
   - All template changes require visual preview approval
   - Accessibility compliance verification (WCAG 2.1 AA)
   - Cross-client compatibility testing
   - Performance impact assessment

**Monitoring & Alerting:**

```typescript
// Email system health monitoring
class EmailSystemMonitor {
  async checkTemplateHealth(): Promise<HealthStatus> {
    const results = await Promise.allSettled([
      this.validateTemplateRendering(),
      this.checkSanitizationRules(),
      this.verifyMultilingualSupport()
    ])
    
    return this.aggregateHealthStatus(results)
  }
  
  async logTemplateUsage(templateType: string, language: string): Promise<void> {
    // Analytics and usage tracking
  }
}
```

**Troubleshooting Procedures:**

1. **Template Rendering Failures:**
   - Check component props validation
   - Verify template registry configuration
   - Review sanitization rule conflicts

2. **Email Delivery Issues:**
   - Validate SMTP configuration
   - Check rate limiting and quotas
   - Review email content for spam triggers

3. **Performance Degradation:**
   - Monitor template rendering times
   - Check database query performance
   - Review caching effectiveness

**Documentation Requirements:**
- Template development guide with examples
- CMS user manual for template management
- API documentation for email service endpoints
- Troubleshooting runbook for common issues

---

## Summary & Next Steps

**Key Decisions:**
1. **React Email** as the primary templating engine for type safety and maintainability
2. **Hybrid storage** approach maintaining CMS flexibility with component-based templates
3. **Gradual migration** strategy to minimize risk and ensure continuity
4. **Comprehensive testing** strategy including visual regression and performance benchmarks

**Immediate Next Steps:**
1. Install React Email dependencies: `npm install @react-email/components @react-email/render`
2. Create proof-of-concept registration confirmation template
3. Set up development preview environment
4. Begin Phase 1 implementation following the roadmap

**Success Metrics:**
- 50% reduction in template maintenance time
- 100% TypeScript type coverage
- Zero security vulnerabilities
- 99.9% email delivery success rate

This refactoring strategy provides a robust foundation for scalable, maintainable email templating while preserving all existing functionality and improving developer experience significantly.
