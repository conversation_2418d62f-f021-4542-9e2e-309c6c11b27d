# **CSV Export Functionality Testing Plan**
**Task:** 1.1.7 - CSV Export Functionality Validation
**Status:** Implementation Complete ✅ | Testing Pending 🔬

## **🎯 TESTING OVERVIEW**

### **Implementation Under Test:**
- **CSV Export Utility:** `src/utilities/eventExports.ts`
- **API Endpoint:** `src/app/(payload)/api/events/[id]/export/route.ts`
- **Enhanced Admin UI:** `src/collections/Events.ts`

### **Testing Scope:**
1. **Unit Tests** - Utility function integrity
2. **Integration Tests** - API endpoint functionality
3. **End-to-End Tests** - Admin panel integration
4. **Performance Tests** - Large dataset handling
5. **Cross-browser Tests** - File download compatibility

---

## **🧪 UNIT TESTING SPECIFICATIONS**

### **1.1 generateAttendeesCSV() Function Tests**

#### **✅ Test Cases:**
```typescript
// Test 1: Empty attendee list
describe('generateAttendeesCSV - Empty Data', () => {
  const emptyEvent = { attendees: [] } as Event
  const result = generateAttendeesCSV(emptyEvent)

  expect(result).toContain('No attendees registered')
  expect(result).toContain('Attendee Name,Email Address')
})

// Test 2: Single attendee with all fields
describe('generateAttendeesCSV - Complete Data', () => {
  const completeAttendee = {
    userName: 'John Smith',
    userEmail: '<EMAIL>',
    registrationDate: new Date('2025-08-28'),
    status: 'registered',
    customFields: '{"diet": "vegetarian", "experience": "5 years"}',
    uploadedFiles: [{ fieldName: 'resume', file: 'mock' }]
  }
  // Test CSV contains all expected data
})

// Test 3: CSV escaping for special characters
describe('generateAttendeesCSV - CSV Escaping', () => {
  const specialAttendee = {
    userName: 'Name,with,commas',
    userEmail: 'email"with"<EMAIL>',
    customFields: '{"notes": "Multi\nline\ntext"}'
  }
  // Test proper escaping
})
```

---

## **🔗 INTEGRATION TESTING SPECIFICATIONS**

### **2.1 API Endpoint Tests**

#### **✅ GET Endpoint Tests:**
```typescript
// Test 1: Valid event ID with attendees
describe('/api/events/[id]/export - Success Case', async () => {
  const response = await fetch('/api/events/mock-event-id/export')
  const csvContent = await response.text()

  expect(response.status).toBe(200)
  expect(response.headers.get('content-type')).toBe('text/csv')
  expect(response.headers.get('content-disposition')).toContain('attachment')
  expect(csvContent).toContain('Attendee Name')
})

// Test 2: Event not found
describe('/api/events/[id]/export - Event Not Found', async () => {
  const response = await fetch('/api/events/invalid-id/export')

  expect(response.status).toBe(404)
})

// Test 3: Event with no attendees
describe('/api/events/[id]/export - Empty Attendees', async () => {
  const response = await fetch('/api/events/empty-event/export')
  const csvContent = await response.text()

  expect(response.status).toBe(200)
  expect(csvContent).toContain('No attendees registered')
})
```

#### **✅ POST Endpoint Tests:**
```typescript
// Test 4: Alternative POST method
describe('/api/events/[id]/export - POST Method', async () => {
  const response = await fetch('/api/events/mock-event-id/export', {
    method: 'POST'
  })

  expect(response.status).toBe(200)
})
```

---

## **🌐 END-TO-END TESTING SPECIFICATIONS**

### **3.1 Payload Admin Integration Tests**

#### **✅ Admin Event View Tests:**
```typescript
// Test 1: Admin can access event with attendee list
describe('Events Admin Panel - Attendee Visibility', () => {
  // Load event details page
  // Verify attendees field is visible and expanded
  // Check that attendee data displays properly
  // Confirm all fields (name, email, status, date) are shown
})

// Test 2: Admin attendee list formatting
describe('Events Admin Panel - Attendee Data Display', () => {
  // Verify each attendee row shows correct information
  // Check status badges display correctly
  // Confirm custom fields are displayed appropriately
  // Validate uploaded files show properly
})
```

---

## **⚡ PERFORMANCE TESTING SPECIFICATIONS**

### **4.1 Scalability Tests**

#### **✅ Large Dataset Tests:**
```typescript
// Test 1: Performance with 100 attendees
describe('CSV Export Performance - 100 Attendees', async () => {
  const largeEvent = createMockEvent(100) // Helper to create event with N attendees

  const startTime = Date.now()
  const csvContent = generateAttendeesCSV(largeEvent)
  const endTime = Date.now()

  expect(endTime - startTime).toBeLessThan(500) // < 500ms
  expect(csvContent.split('\n')).toHaveLength(101) // 100 rows + header
})

// Test 2: Performance with 1000 attendees
describe('CSV Export Performance - 1000 Attendees', async () => {
  // Similar test for larger datasets
  // Ensure no memory leaks
  // Verify processing time remains under 2 seconds
})
```

#### **✅ Memory Usage Tests:**
```typescript
// Test 3: Memory efficiency
describe('CSV Export - Memory Efficiency', () => {
  // Monitor memory usage during CSV generation
  // Ensure no memory leaks with large datasets
  // Verify garbage collection works properly
})
```

---

## **🌍 CROSS-BROWSER TESTING SPECIFICATIONS**

### **5.1 Download Functionality Tests**

#### **✅ Browser Compatibility Tests:**
```typescript
// Manual testing checklist for multiple browsers:

// ✅ Chrome (Desktop & Mobile)
describe('CSV Download - Chrome Compatibility', () => {
  // Test file download prompt appears
  // Verify filename formatting works correctly
  // Check that file opens properly in Excel/Google Sheets
})

// ✅ Firefox (Desktop & Mobile)
describe('CSV Download - Firefox Compatibility', () => {
  // Same tests as Chrome
  // Additional FF-specific download behavior
})

// ✅ Safari (Desktop & Mobile)
describe('CSV Download - Safari Compatibility', () => {
  // Test Safari's download handling
  // Verify proper filename handling
})

// ✅ Edge (Desktop & Mobile)
describe('CSV Download - Edge Compatibility', () => {
  // Test modern Edge download capabilities
})
```

---

## **🔧 API ENDPOINT SPECIFIC TESTS**

### **6.1 Error Handling Tests**

#### **✅ Error Scenarios:**
```typescript
// Test 1: Database connection error
describe('CSV Export - Database Errors', async () => {
  // Simulate database connection failure
  // Verify proper 500 error response
  // Check error logging
})

// Test 2: Invalid event ID format
describe('CSV Export - Invalid Parameters', async () => {
  // Test various invalid ID formats
  // Verify appropriate error responses
})

// Test 3: Unauthorized access attempts
describe('CSV Export - Security', async () => {
  // Test without proper authentication
  // Verify restricted access
  // Check admin-only permissions
})
```

---

## **📊 TEST DATA REQUIREMENTS**

### **7.1 Mock Data Creation**

#### **✅ Test Event Scenarios:**
```typescript
// Helper function for creating test events
const createMockEvent = (attendeeCount: number, customData?: any) => ({
  id: 'test-event-123',
  title: 'Mock Rotary Event',
  slug: 'mock-rotary-event',
  attendees: Array.from({ length: attendeeCount }, (_, i) => ({
    userId: `user-${i + 1}`,
    userEmail: `attendee${i}@example.com`,
    userName: `Attendee ${i + 1}`,
    registrationDate: new Date(Date.now() - Random.natural({ min: 1, max: 100 }) * 24 * 60 * 60 * 1000),
    status: ['registered', 'waitlisted', 'cancelled', 'attended'][Random.natural({ min: 0, max: 3 })],
    customFields: customData || null,
    uploadedFiles: Random.natural({ min: 0, max: 3 }) > 0 ? [{
      fieldName: 'registrationForm',
      file: 'mock-file-reference'
    }] : []
  })),
  ...otherRequiredFields
})
```

---

## **📈 TEST EXECUTION PLAN**

### **8.1 Testing Sequence:**

| Phase | Test Type | Duration | Environment |
|-------|-----------|----------|-------------|
| **Phase 1** | Unit Tests | 30 mins | Local Development |
| **Phase 2** | Integration Tests | 45 mins | Local Development |
| **Phase 3** | E2E Tests | 1 hour | Local Development |
| **Phase 4** | Performance Tests | 30 mins | Local Development |
| **Phase 5** | Cross-browser Tests | 1 hour | Multiple Browsers |
| **Phase 6** | Manual Testing | 30 mins | Payload Admin UI |

### **8.2 Success Criteria:**

✅ **Unit Test Pass Rate:** 100%
✅ **API Endpoint Functionality:** All endpoints respond correctly
✅ **File Download:** CSV files download and open properly
✅ **Performance:** < 500ms for < 1000 attendees
✅ **Cross-browser:** Functions correctly in all target browsers
✅ **Error Handling:** All error scenarios handled gracefully

---

## **🚨 ISSUE TRACKING & REPORTING**

### **9.1 Test Result Categories:**

- **✅ PASS:** Test completed successfully
- **❌ FAIL:** Test failed - must be fixed
- **⚠️ SKIP:** Test skipped due to prerequisite failure
- **⏳ PENDING:** Test not yet executed

### **9.2 Reporting Template:**

```markdown
**Test Report Summary:**
- Total Tests: XX
- Passed: XX (XX%)
- Failed: XX (XX%)
- Skipped: XX (XX%)
- Critical Issues: XX
- Performance Issues: XX
- Compatibility Issues: XX

**Next Actions:**
- [ ] Fix critical failures
- [ ] Address performance issues
- [ ] Test compatibility fixes
- [ ] Regression testing
- [ ] Production validation
```

---

## **🎯 EXECUTION STATUS**

**Date:** August 28, 2025
**Status:** Ready for execution 🔬
**Testing Target:** CSV Export Functionality (Tasks 1.1.6-1.1.7)

**Ready to proceed with comprehensive testing!** 🚀