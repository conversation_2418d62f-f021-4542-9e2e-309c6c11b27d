# GDPR Coverage Report Troubleshooting Guide

## Overview

This guide provides comprehensive solutions for common issues when setting up HTML coverage reports for GDPR compliance tests in Vitest, including configuration problems, integration issues, and Payload CMS-specific challenges.

## Table of Contents

1. [Quick Setup](#quick-setup)
2. [Common Issues & Solutions](#common-issues--solutions)
3. [Configuration Examples](#configuration-examples)
4. [Payload CMS Integration](#payload-cms-integration)
5. [Performance Optimization](#performance-optimization)
6. [Advanced Troubleshooting](#advanced-troubleshooting)

---

## Quick Setup

### 1. Basic GDPR Coverage Configuration

```bash
# Run GDPR tests with coverage
pnpm test:gdpr:coverage

# Generate HTML reports
pnpm test:gdpr:simple

# Run all coverage tests
pnpm test:coverage:all
```

### 2. Files Required

- `vitest.config.mts` - Main Vitest configuration
- `vitest.gdpr.config.mts` - GDPR-specific configuration
- `vitest.setup.ts` - Test setup with utilities
- `package.json` - Test scripts

---

## Common Issues & Solutions

### Issue 1: "html" does not exist in coverage config

**Problem:** TypeScript error when configuring HTML reporter

```bash
❌ No overload matches this call.
❌ Object literal may only specify known properties, and 'html' does not exist in type
```

**Solution:** Remove unsupported HTML configuration properties

```typescript
// ❌ BAD - This will cause TypeScript errors
coverage: {
  html: {
    subdir: 'html-report'
  }
}

// ✅ GOOD - HTML reports work automatically
coverage: {
  reporter: ['text', 'json', 'html'],
  reportsDirectory: './coverage/gdpr'
}
```

---

### Issue 2: Missing Payload CMS Required Fields

**Problem:** Tests fail with validation errors

```bash
ValidationError: The following fields are invalid: Name, Classification, Joining Date, Rotary District
```

**Solution:** Add required fields to test data

```typescript
// ✅ REQUIRED FIELDS for Payload CMS User collection
const validUserData = {
  name: { en: 'Test User' },           // Required
  classification: 'member',            // Required
  joiningDate: '2023-01-01',           // Required
  rotaryDistrict: '1234',              // Required
  email: '<EMAIL>',           // Required
  memberType: 'member' as const        // Required
}

// ✅ Use this in tests
await payload.create({
  collection: 'users',
  data: validUserData
})
```

---

### Issue 3: "Body is unusable" Errors in API Tests

**Problem:** API route tests fail when reusing the same Request object

```bash
TypeError: Body is unusable: Body has already been read
```

**Solution:** Create new Request objects for each test call

```typescript
// ❌ BAD - Reuses Request object
const request = new Request('http://localhost/api/test', {
  method: 'PUT',
  body: JSON.stringify(data)
})

for (let i = 0; i < requests; i++) {
  await PUT(request) // Only works once
}

// ✅ GOOD - Create fresh Request objects
const createRequest = (data: any) =>
  new Request('http://localhost/api/test', {
    method: 'PUT',
    body: JSON.stringify(data)
  })

for (let i = 0; i < requests; i++) {
  await PUT(createRequest(testData)) // Fresh request each time
}
```

---

### Issue 4: Threshold Assertion Errors

**Problem:** Tests fail with threshold assertion errors

```bash
AssertionError: expected 2555 to be greater than 2555
```

**Solution:** Fix comparison operators and retention periods

```typescript
// ❌ BAD - Incorrect comparison
expect(retentionPolicy.retentionPeriod).toBeGreaterThan(2555) // Fails on 2555 == 2555

// ✅ GOOD - Correct retention period logic
const gdprRetentionDays = 2555 // 7 years

expect(retentionPolicy.retentionPeriod).toBeGreaterThanOrEqual(gdprRetentionDays)

// Or for strict comparison
expect(retentionPolicy.retentionPeriod).toBeGreaterThan(gdprRetentionDays - 1)
```

---

### Issue 5: Property Name Mismatches

**Problem:** Tests fail due to incorrect property names

```bash
Property 'natures' does not exist, did you mean 'nature'?
```

**Solution:** Correct property names in test data

```typescript
// ❌ BAD - Wrong property name
breachNotification: {
  content: {
    breachDetails: {
      natures: 'unauthorized_access' // Property doesn't exist
    }
  }
}

// ✅ GOOD - Correct property name
breachNotification: {
  content: {
    breachDetails: {
      nature: 'unauthorized_access' // Correct property
    }
  }
}
```

---

### Issue 6: Coverage Report Not Generating

**Problem:** HTML coverage reports are not created

```bash
Coverage reports generated at: ./coverage/gdpr (empty or missing HTML files)
```

**Solution:** Configure coverage correctly

```typescript
// vitest.gdpr.config.mts
{
  coverage: {
    provider: 'v8',
    reporter: ['text', 'json', 'html', 'lcov'], // Include 'html'
    reportsDirectory: './coverage/gdpr',
    include: ['src/app/(payload)/**'], // Must include actual source files
    exclude: [...],
    reportOnFailure: true // Generate reports even if tests fail
  }
}
```

---

### Issue 7: Environment Variables Not Loaded

**Problem:** Tests fail due to missing environment configuration

```bash
Error: Database connection failed
Error: SMTP Error
```

**Solution:** Ensure environment variables are loaded

```bash
# ✅ In package.json scripts
"test:gdpr": "dotenv -e test.env -- vitest run tests/gdpr/"

# ✅ In vitest.config.mts
setupFiles: ['./vitest.setup.ts']

# ✅ In vitest.setup.ts
import 'dotenv/config'
```

---

## Configuration Examples

### Complete GDPR Coverage Configuration

```typescript
// vitest.gdpr.config.mts
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  plugins: [tsconfigPaths({ root: '.' }), react()],
  test: {
    name: 'gdpr-tests',
    environment: 'node',
    setupFiles: ['./vitest.setup.ts'],
    include: ['tests/gdpr/**/*.test.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      include: [
        'src/app/(payload)/api/users/**/*.ts',
        'src/utilities/**/*.ts'
      ],
      exclude: [
        'src/payload-types.ts',
        '**/*.config.*',
        '**/*.setup.*'
      ],
      reportsDirectory: './coverage/gdpr',
      thresholds: {
        global: {
          statements: 95,
          branches: 90,
          functions: 95,
          lines: 95
        }
      },
      reportOnFailure: true
    },
    testTimeout: 10000,
    hookTimeout: 5000
  }
})
```

### Package.json Test Scripts

```json
{
  "scripts": {
    "test:gdpr": "TEST_ENV=gdpr dotenv -e test.env -- vitest run tests/gdpr/",
    "test:gdpr:coverage": "TEST_ENV=gdpr dotenv -e test.env -- vitest run tests/gdpr/ --coverage",
    "test:gdpr:html": "TEST_ENV=gdpr dotenv -e test.env -- vitest run tests/gdpr/ --coverage --reporter=html",
    "test:gdpr:simple": "dotenv -e test.env -- vitest run tests/gdpr/gdpr-compliance-simple.test.ts --coverage --coverage.reportsDirectory='./coverage/gdpr-simple'",
    "test:coverage:all": "pnpm run test:gdpr:simple && pnpm run test:accessibility:coverage && pnpm run test:i18n:coverage"
  }
}
```

---

## Payload CMS Integration

### Using Payload CMS in Tests

```typescript
// ✅ Proper Payload initialization
import { getPayload } from 'payload'
import configPromise from '@/payload.config'

describe('GDPR Tests with Payload', () => {
  let payload: Payload

  beforeAll(async () => {
    const config = await configPromise
    payload = await getPayload({ config })
  })

  it('should create user with valid data', async () => {
    const userData = {
      name: { en: 'Test User' },
      classification: 'member',
      joiningDate: '2023-01-01',
      rotaryDistrict: '1234',
      email: '<EMAIL>',
      memberType: 'member' as const
    }

    const user = await payload.create({
      collection: 'users',
      data: userData
    })

    expect(user.id).toBeDefined()
    expect(user.email).toBe(userData.email)
  })
})
```

### Handling Required Fields

```typescript
// GDPR Compliance User Creation Helper
const createGDPRCompliantUser = async (payload: Payload, overrides = {}) => {
  const defaultUserData = {
    name: { en: 'GDPR Test User', fr: 'Utilisateur Test GDPR', ar: 'مستخدم اختبار GDPR' },
    classification: 'member',
    joiningDate: '2023-01-01',
    rotaryDistrict: '9999',
    email: `gdpr-${Date.now()}@example.com`,
    memberType: 'member' as const,
    privacySettings: {
      isPublicProfile: false,
      dataProcessingConsent: true,
      shareContactDetails: false
    }
  }

  return await payload.create({
    collection: 'users',
    data: { ...defaultUserData, ...overrides }
  })
}
```

---

## Performance Optimization

### Speeds Up Coverage Generation

```typescript
// Optimize for GDPR test performance
{
  coverage: {
    all: false, // Only track tested files
    include: ['src/app/(payload)/**'], // Specific paths
    exclude: ['src/app/(frontend)/**'], // Exclude frontend
    reportOnFailure: false // Skip if not needed
  },
  pool: 'threads', // Use thread pool
  poolOptions: {
    threads: {
      singleThread: false,
      isolate: true
    }
  }
}
```

### Faster Test Execution

```bash
# Use specific config for faster runs
vitest run tests/gdpr/ --config=./vitest.gdpr.config.mts

# Use Node environment for server-side tests
{
  test: { environment: 'node' }
}
```

---

## Advanced Troubleshooting

### Debugging Coverage Issues

1. **Check coverage output paths**

```bash
ls -la coverage/gdpr/
# Should see index.html, coverage-final.json, etc.
```

2. **Verify test isolation**

```typescript
// Add console.log to verify test data
it('should debug payload creation', async () => {
  console.log('Test data:', testData)
  const result = await payload.create({...})
  console.log('Payload result:', result)
})
```

3. **Run with verbose output**

```bash
VITEST_MODE=true vitest run tests/gdpr/ --coverage --reporter=verbose
```

### Common Log Analysis

```bash
# View coverage summary
cat coverage/gdpr/coverage-summary.json | jq '.total'

# Check HTML report generation
ls -la coverage/gdpr/*.html

# Analyze test isolation
grep -r "Body has already been read" coverage/
```

### Environment-Specific Issues

**Windows Path Resolution**

```bash
# Use forward slashes in paths
reportsDirectory: './coverage/gdpr'

# Avoid backslashes in config
{ reportersDirectory: path.resolve('./coverage/gdpr') }
```

**Mac/Linux Path Issues**

```bash
# Ensure correct permissions
chmod 755 coverage/
chmod 644 coverage/gdpr/index.html
```

**Docker Environments**

```bash
# Use absolute paths in Docker
reportsDirectory: '/app/coverage/gdpr'
```

---

## Test Data Management

### GDPR Test Data Guidelines

```typescript
// Sample GDPR-compliant test data
export const gdprTestData = {
  users: {
    compliant: {
      required: {
        name: { en: 'Test User' },
        classification: 'member',
        joiningDate: '2023-01-01',
        rotaryDistrict: '1234',
        email: '<EMAIL>',
        memberType: 'member'
      },
      privacy: {
        privacySettings: {
          isPublicProfile: false,
          dataProcessingConsent: true,
          shareContactDetails: false,
          allowAnalytics: false
        },
        communicationPreferences: {
          emailNotifications: true,
          smsNotifications: false
        }
      }
    }
  },
  retention: {
    gdprMinimumDays: 2555, // 7 years
    auditRetentionYears: 7,
    consentRetentionYears: 5
  }
}
```

---

## Validation Checklist

### Before Running GDPR Coverage Tests

- ✅ `vitest.gdpr.config.mts` exists with correct coverage settings
- ✅ `vitest.setup.ts` loads environment variables
- ✅ `package.json` contains GDPR-specific scripts
- ✅ Test files include required Payload fields
- ✅ Environment variables are properly configured
- ✅ Coverage directory exists with write permissions

### After Coverage Generation

- ✅ HTML report generated: `coverage/gdpr/index.html`
- ✅ JSON summary available: `coverage/gdpr/coverage-summary.json`
- ✅ Coverage thresholds met for GDPR compliance
- ✅ No TypeScript errors in configuration
- ✅ Test isolation working correctly

---

## Support & Resources

### Documentation Links

- [Vitest Coverage Documentation](https://vitest.dev/guide/coverage.html)
- [Payload CMS Testing Documentation](https://payloadcms.com/docs/beta/testing)
- [GDPR Article 5 Requirements](https://gdpr-info.eu/art-5-gdpr/)

### Quick Commands

```bash
# Clean and regenerate coverage
rm -rf coverage/ && pnpm test:gdpr:coverage

# View coverage report
open coverage/gdpr/index.html

# Check coverage thresholds
cat coverage/gdpr/coverage-summary.json
```

### Need Help?

- Check browser console for HTML report issues
- Verify Node.js version compatibility
- Ensure all paths are correct for your OS
- Check Payload CMS configuration for database connection issues
