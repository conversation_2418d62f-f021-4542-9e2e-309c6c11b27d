# Internationalization for Rotary Club Tunis Doyen CMS

## Overview

This document outlines the internationalization (i18n) strategy for the Rotary Club Tunis Doyen CMS. Given the international nature of Rotary and the diverse membership of the Tunis Doyen chapter, the CMS must support multiple languages to effectively serve its community.

## Supported Languages

The CMS will initially support the following languages:

1. **English** - Primary language for international communication
2. **French** - Widely used in Tunisia and within the club
3. **Arabic** - Official language of Tunisia

Additional languages may be added in the future based on club needs.

## Implementation Strategy

### Field-Level Localization

Payload CMS supports field-level localization, which will be implemented for all user-facing content. The following fields will be localized across collections:

- Titles
- Descriptions
- Rich text content
- Custom field labels
- SEO metadata

```typescript
// Example of localized field configuration
const localizedField = {
  name: 'title',
  type: 'text',
  required: true,
  localized: true, // Enable localization for this field
};
```

### Locale Configuration

The CMS will be configured with the following locale settings:

```typescript
// Payload config for localization
export default buildConfig({
  // ... other config
  i18n: {
    defaultLocale: 'en',
    locales: [
      {
        code: 'en',
        label: 'English',
      },
      {
        code: 'fr',
        label: 'Français',
      },
      {
        code: 'ar',
        label: 'العربية',
        direction: 'rtl', // Right-to-left support for Arabic
      },
    ],
  },
});
```

### RTL Support

Arabic requires right-to-left (RTL) text direction support. The CMS will implement:

1. RTL layout switching based on locale
2. Bidirectional text handling
3. Appropriate CSS for RTL interfaces
4. RTL-compatible components and layouts

### Translation Workflow

The CMS will support an efficient translation workflow:

1. **Content Creation** - Content is initially created in the default language (English)
2. **Translation Flagging** - New or updated content is automatically flagged for translation
3. **Translation Interface** - Dedicated UI for translators to efficiently translate flagged content
4. **Translation Status** - Visual indicators showing translation status for each language
5. **Approval Process** - Optional review and approval workflow for translations

### URL Structure

The CMS will implement locale-specific URL structures:

```
/en/about-us
/fr/a-propos-de-nous
/ar/من-نحن
```

URL handling will include:

1. Locale detection and redirection
2. Language switcher component
3. Preservation of language preference across sessions
4. SEO optimizations for multilingual content

## Frontend Implementation

### Next.js Integration

The frontend will leverage Next.js internationalization features:

```typescript
// next.config.js example
module.exports = {
  i18n: {
    locales: ['en', 'fr', 'ar'],
    defaultLocale: 'en',
    localeDetection: true,
  },
};
```

### Language Switcher Component

A language switcher component will be implemented to allow users to change their preferred language:

```typescript
// Example Language Switcher component
const LanguageSwitcher = () => {
  const router = useRouter();
  const { locales, locale: activeLocale } = router;

  const handleLocaleChange = (locale) => {
    router.push(router.pathname, router.asPath, { locale });
  };

  return (
    <div className="language-switcher">
      {locales.map((locale) => (
        <button
          key={locale}
          onClick={() => handleLocaleChange(locale)}
          className={locale === activeLocale ? 'active' : ''}
        >
          {locale.toUpperCase()}
        </button>
      ))}
    </div>
  );
};
```

## Content Fallbacks

When content is not available in the requested language, the system will:

1. Display content in the default language (English)
2. Show a visual indicator that the content is not available in the requested language
3. Provide an option to view available languages for that content

## Date, Time, and Number Formatting

The CMS will implement locale-specific formatting for:

1. Dates and times using the Intl.DateTimeFormat API
2. Numbers and currencies using the Intl.NumberFormat API
3. Relative time expressions (e.g., "2 days ago")

## Translation Management

### Translation Memory

To improve efficiency, the CMS will implement a translation memory system that:

1. Stores previously translated segments
2. Suggests translations for similar content
3. Maintains consistency across the site

### Glossary and Style Guide

A multilingual glossary will be maintained for:

1. Rotary-specific terminology
2. Club-specific terms and phrases
3. Consistent translation of common terms

## Testing and Quality Assurance

The internationalization implementation will be tested for:

1. Correct display of RTL content
2. Proper handling of mixed-direction text
3. UI layout in all supported languages
4. Date, time, and number formatting
5. Translation completeness and accuracy

## Next Steps

1. Configure Payload CMS with the i18n settings
2. Identify all fields requiring localization
3. Implement the frontend language switcher
4. Set up the translation workflow
5. Create the translation memory system
6. Develop testing procedures for i18n features