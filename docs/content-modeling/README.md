# **Content Model & Architecture Guide**
## **Rotary Club Tunis Doyen Website CMS**

**Version:** 1.1
**Author:** Monem
**Date:** August 27, 2025
**Related PRD Version:** 1.2
**Change Log:** `docs/audit-trail/ChangeLog.md`

---

### **Table of Contents**

1. Collection Schemas (Revised for Flexibility & Data Integrity)
2. Relationships (Revised for Real-World Scenarios)
3. i18n Strategy (Revised for User-Centric Fallbacks)
4. Hooks & Endpoints (Enhanced for Production Safety)

---

### **1. Collection Schemas (Revised for Flexibility & Data Integrity)**

---

**Collection: home_page**
*Purpose: Manages high-level homepage structure and featured content.*

- **hero_title**
  • Label: Hero Title
  • Type: text
  • Validation: required, max 100 characters
  • i18n: localized
  • Example: "We are People of Action"

- **hero_subtitle**
  • Label: Hero Subtitle
  • Type: text
  • Validation: required, max 150 characters
  • i18n: localized

- **hero_image**
  • Label: Hero Image
  • Type: image
  • Validation: required, max 5MB, JPG/PNG
  • i18n: shared

- **impact_stats**
  • Label: Impact Statistics
  • Type: group (repeater)
  • Fields:
    - label (text, localized, required, max 30 chars) → e.g., "Projects Completed"
    - value (number, shared, required, min 0) → e.g., 24
    - icon (select, shared, options: earth, shovel, vaccine) → maps to Rotary.org icons
  • Validation: min 3 items
  • i18n: label=localized, value/icon=shared

- **featured_projects**
  • Label: Featured Projects
  • Type: relationship
  • Target: projects
  • Cardinality: many-to-many
  • Filter: status = "Active" or "Completed"
  • i18n: shared
  • **Note:** Projects are featured via a `is_featured` boolean in the `projects` collection (see below).

- **cta_text**
  • Label: Call to Action Text
  • Type: text
  • Validation: required, max 60 characters
  • i18n: localized

- **cta_link**
  • Label: Call to Action Link
  • Type: relationship or url
  • Options:
    - Internal Page (relationship to `pages`)
    - External URL (text, URL validation)
  • Validation: required (one must be selected)
  • i18n: shared

---

**Collection: projects** *(Enhanced)*
*Purpose: Tracks service initiatives and their impact.*

- **title**
  • Label: Project Title
  • Type: text
  • Validation: required
  • i18n: localized

- **description**
  • Label: Description
  • Type: richText
  • Validation: required
  • i18n: localized

- **category**
  • Label: Cause Area
  • Type: select
  • Options: Peace, Health, Education, Environment, Community Development
  • i18n: localized

- **status**
  • Label: Status
  • Type: select
  • Options: Planning, Active, Completed
  • i18n: shared

- **start_date**
  • Label: Start Date
  • Type: date
  • Validation: required
  • i18n: shared

- **end_date**
  • Label: End Date
  • Type: date
  • Validation: optional, must be after `start_date`
  • i18n: shared

- **image**
  • Label: Project Image
  • Type: image
  • Validation: required
  • i18n: shared

- **is_featured**
  • Label: Feature on Homepage
  • Type: boolean
  • Default: false
  • i18n: shared
  • **Note:** Editors can toggle this without editing the homepage. The `home_page` collection dynamically pulls projects where `is_featured = true`.

---

**Collection: contact_form_submissions** *(Enhanced)*
*Purpose: Logs incoming contact form messages (not publicly editable).*

- **name**
  • Label: Name
  • Type: text
  • Validation: required
  • i18n: shared

- **email**
  • Label: Email
  • Type: text
  • Validation: required, email format
  • i18n: shared

- **subject**
  • Label: Subject
  • Type: text
  • Validation: required
  • i18n: localized

- **message**
  • Label: Message
  • Type: text
  • Widget: textarea
  • Validation: required, max 2000 characters
  • i18n: localized

- **submitted_at**
  • Label: Submission Date
  • Type: date (datetime)
  • i18n: shared

- **status**
  • Label: Status
  • Type: select
  • Options: Unread, Read, Responded, Archived
  • Default: Unread
  • i18n: shared

---

*(All other collection schemas remain as previously defined, with the understanding that the `impact_stats.value` field is now a `number` type.)*

---

### **2. Relationships (Revised for Real-World Scenarios)**

- **Event → Organizer (members)**
  • Cardinality: **many-to-many**
  • Rationale: A single event (e.g., "Annual Gala") may have multiple organizers (e.g., Events Chair, Sponsorship Lead).
  • Implementation: A dedicated `event_organizers` join table or direct many-to-many field.
  • Example: The "Clean Beach Day" event has 3 organizers from the membership.

- **Event Registration (join table)**
  • Cardinality:
    - `event_registrations` → `events`: **many-to-one**
    - `event_registrations` → `members`: **many-to-one**
  • Rationale: The `event_registrations` collection acts as a proper **join table** with its own attributes (status, registered_at). This ensures data integrity and allows for future extensions (e.g., custom registration fields).
  • Example: One `event_registrations` entry links Karim (member) to the "Tech Workshop" (event).

- **News → Projects**
  • Cardinality: **many-to-many**
  • Rationale: A single news article (e.g., "Our Annual Report") may cover multiple projects. A project may be covered in multiple articles.
  • Implementation: A `news_projects` relationship field in the `news` collection.
  • Example: A feature article on "Our Year of Service" links to the "Books for All", "Clean Beach", and "Health Fair" projects.

- **Member → Event Registration**
  • Cardinality: **one-to-many** (via the join table)
  • Rationale: A member can have many registration entries. Each entry is a unique record in the `event_registrations` table.

---

### **3. i18n Strategy (Revised for User-Centric Fallbacks)**

- **Fallback Logic (Improved):**
  • The system will respect the **user's browser language preference** (from `Accept-Language` header).
  • If the requested language is not available, it will fall back in this order:
    1.  **User's preferred language** (if set in profile)
    2.  **Site default language** (set in `site_settings`, default: `fr`)
    3.  **English (`en`)** as the ultimate fallback.
  • This is more user-centric than a hardcoded `en` -> `fr` chain.

- **Editorial Enforcement:**
  • The `before_publish` hook (see below) will **prevent publishing** if any `required` field in the primary language (e.g., French) is missing.
  • For non-primary languages, missing translations will trigger a **warning**, but not block publishing, to allow for staged content rollout.

---

### **4. Hooks & Endpoints (Enhanced for Production Safety)**

- **Webhook: after_create event_registration**
  • Trigger: On successful creation of an `event_registrations` entry.
  • Functionality: Sends a confirmation email to the registrant and a notification to all event organizers.
  • Security: Executed asynchronously; uses a message queue to prevent blocking. Rate-limited to 100 emails/hour per event to deter abuse.

- **Server Hook: before_publish news**
  • Trigger: Before a `news` entry is published.
  • Functionality:
    - **Blocks publishing** if the `title`, `excerpt`, or `content` fields are missing in the **primary language** (set in `site_settings`).
    - Logs a **warning** if these fields are missing in secondary languages.
    - Optionally, calls a translation API (e.g., DeepL) to pre-fill missing fields with a draft (marked as "Needs Review").
  • Security: Runs in a sandboxed environment; translation API key is encrypted.

- **Custom Endpoint: POST /api/anonymize-inactive-members**
  • Purpose: Automates GDPR-compliant data retention.
  • Functionality: Finds members with no login or registration activity for 2+ years, removes PII (name, email, phone), retains role and join date for historical records.
  • Authentication: Requires admin JWT token with `data:manage` scope.
  • Rate Limiting: One execution per month; idempotent.

- **Custom Endpoint: GET /api/impact-stats**
  • Purpose: Exposes key metrics for dynamic display (e.g., "47M volunteer hours").
  • Functionality: Aggregates data from `projects` (status=Completed) and `event_registrations` (last year). Returns JSON with `total_projects`, `total_volunteer_hours`, `total_funding`.
  • Caching: Response cached for 24 hours via CDN.
  • Access: Public.