# **Dark Mode Integration**

Here’s a refined Tailwind v4 config that wires in a **semantic token layer** and gives you **automatic light/dark mappings**:

```js
// tailwind.config.js (Tailwind v4 + Rotary dark mode defaults)
import { defineConfig } from "tailwindcss";

export default defineConfig({
  darkMode: "class", // toggle with <html class="dark">
  theme: {
    extend: {
      colors: {
        // --- Base brand ---
        brand: {
          royalBlue: "#17458F",
          royalBlueDark: "#123F7A",
          gold: "#F7A81B",
        },
        status: {
          success: "#009739",
          error: "#D41367",
          warning: "#FF7600",
          info: "#00A2E0",
        },

        // --- Neutral base tokens ---
        neutral: {
          white: "#FFFFFF",
          nearBlack: "#111111",
          trueBlack: "#000000",
          charcoal: "#54565A",
          slate: "#657F99",
          smoke: "#B1B1B1",
          powderBlue: "#B9D9EB",
        },

        surface: {
          overlay: "rgba(23,69,143,0.6)",
        },

        // --- Semantic light/dark mappings ---
        surfaceBase: {
          DEFAULT: "#FFFFFF", // light mode bg
          dark: "#111111", // dark mode bg
        },
        surfaceCard: {
          DEFAULT: "#FFFFFF",
          dark: "#1C1C1C",
        },
        surfaceOverlay: {
          DEFAULT: "rgba(23,69,143,0.6)",
          dark: "rgba(17,17,17,0.85)",
        },
        textPrimary: {
          DEFAULT: "#111111",
          dark: "#FFFFFF",
        },
        textSecondary: {
          DEFAULT: "#54565A",
          dark: "#B1B1B1",
        },
        textLink: {
          DEFAULT: "#17458F",
          dark: "#F7A81B",
        },
      },

      fontFamily: {
        sans: ["Open Sans", "Arial", "system-ui", "sans-serif"],
      },
      fontSize: {
        h1: ["56px", { lineHeight: "64px", fontWeight: "700" }],
        h2: ["40px", { lineHeight: "48px", fontWeight: "600" }],
        h3: ["28px", { lineHeight: "36px", fontWeight: "600" }],
        body: ["16px", { lineHeight: "24px", fontWeight: "400" }],
        small: ["13px", { lineHeight: "20px", fontWeight: "400" }],
      },
    },
  },
  plugins: [],
});
```

---

## ✅ Usage examples

```html
<!-- Background and text swap automatically in dark mode -->
<div class="bg-surfaceBase text-textPrimary dark:bg-surfaceBase-dark dark:text-textPrimary-dark">
  <h1 class="text-h1">Rotary Heading</h1>
  <p class="text-body">This text adjusts based on dark mode.</p>
  <a href="#" class="text-textLink dark:text-textLink-dark">Learn more</a>
</div>

<!-- Card with dark mode -->
<div class="bg-surfaceCard text-textSecondary dark:bg-surfaceCard-dark dark:text-textSecondary-dark p-6 rounded-2xl shadow">
  Accessible dark-mode aware card
</div>
```

---

### 🔑 Why this helps

* **Consistency** → your devs don’t need to decide “which blue” in dark mode.
* **Futureproofing** → if Rotary tweaks colors, you just update tokens.
* **Accessibility** → ensures contrast ratios hold across modes.
