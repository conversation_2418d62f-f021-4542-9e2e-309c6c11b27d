# **UI Kit & Style Guide**
## **Rotary Club Tunis Doyen Website CMS**

**Version:** 1.0
**Author:** Monem
**Date:** August 27, 2025
**Related PRD Version:** 1.2
**Related Content Model Version:** 1.1
**Change Log:** `docs/audit-trail/ChangeLog.md`

---

### **Table of Contents**

1. Brand Foundations
2. Color System (Official Rotary Palette)
3. Typography (Rotary-Approved)
4. Layout, Spacing, Grid
5. Components & Patterns (Specifications)
6. Developer Appendix — Implementation Notes
7. Why This Matters (Team Orientation)

---

### **1. Brand Foundations**

#### **Purpose**
One source of truth for designers and developers to ensure Rotary brand fidelity, accessibility, and technical consistency across CMS templates and components, with seamless integration to PRD v1.2, User Stories v1.0, and Architecture Guide v1.1.

#### **Brand Pillars**
Clarity, usefulness, dignity, local relevance, inclusivity, and action-oriented service.

#### **Identity Statement**
"We are People of Action." Use in hero messaging and campaign modules with disciplined hierarchy and ample white space. See People of Action guidance for tone and usage. ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/people-of-action))

#### **Naming**
Always "Rotary Club Tunis Doyen" (no district number in logo lockups; district can appear as text elsewhere). Follow official club logo format; retire legacy logos. ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/brand-elements/logos-and-graphics/logos-for-clubs-districts-and-zones))

#### **Logo Handling**
- Use official club lockup (wheel + wordmark). Protect clear space; never alter colors, stretch, or add effects.
- Never use the Rotary masterbrand signature alone for the club. ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/brand-elements/logos-and-graphics))

#### **Accessibility Baseline (WCAG 2.2 AA)**
- Text contrast ≥ 4.5:1 (normal) / 3:1 (≥ 24 px or 700 weight).
- Focus states visible and non-color dependent; keyboard navigable menus, modals, and forms.
- Motion reduced under prefers-reduced-motion; screen reader support with ARIA live regions for dynamic content; logical tab order and focus management per UX guidelines.

#### **Internationalization (TN-Specific)**
- Languages: French (default), Arabic (rtl), English (ltr).
- Numerals in Arabic locale: use Western Arabic numerals 0–9 for Tunisia (locale note).
- RTL mirroring: grids, icon chevrons, pagination arrows, breadcrumb separators; preserve number direction and URL LTR.

#### **Governance & Versioning**
- Store tokens (colors, typography, spacing) in code as variables; expose JSON for design tools.
- Maintain a CHANGELOG.md; every change to tokens or components bumps a minor version and triggers regression checks; cross-reference with Document-Cross-Reference.md for alignment status.

---

### **2. Color System (Official Rotary Palette)**

#### **Notes**
Use pure brand formulas only; do not screen or alter. Primary brand colors are Rotary Royal Blue and Rotary Gold. Values below follow Brand Center. ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/brand-elements/colors))

#### **A) Primary**
- **Rotary Royal Blue** — PMS 286C; CMYK C100 M84 Y12 K3; RGB 23 69 143; HEX #17458F
  - Usage: Headings, primary actions, links, nav, key UI chrome. ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/brand-elements/colors))
- **Rotary Gold** — PMS 130C; CMYK C0 M41 Y100 K0; RGB 247 168 27; HEX #F7A81B
  - Usage: Accents, highlights, badges, focus rings, active indicators. ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/brand-elements/colors))

#### **B) Secondary (Support Accents)**
Use sparingly, never to compete with Primary:
- Azure #0067C8 (PMS 2175C)
- Sky Blue #00A2E0 (PMS 2202C)
- Cranberry #D41367 (PMS 214C)
- Cardinal #E02927 (PMS 485C)
- Turquoise #00ADBB (PMS 7466C)
- Orange #FF7600 (PMS 2018C)
- Violet #901F93 (PMS 2070C)
- Grass #009739 (PMS 355C)

#### **C) Neutrals (UI Surfaces, Text)**
- White #FFFFFF; Black #000000
- Charcoal #54565A; Slate #657F99; Stone #9BA4B4; Pewter #898A8D; Smoke #B1B1B1; Silver #D0CFCD; Powder Blue #B9D9EB; Moss #A7ACA2; Taupe #D9C89E; Storm #7A6E66; Ash #968B83; Platinum #BFB7B0; Cloud #D6D1CA

#### **D) Status**
- Success: Grass #009739
- Error: Cranberry #D41367
- Warning: Orange #FF7600
- Info: Sky Blue #00A2E0

#### **E) Contrast & Combinations**
Examples, AA-compliant:
- Text on White: Royal Blue, Charcoal, Black — pass AA for body text.
- Text on Royal Blue: White — pass AA for ≥ 16 px semibold or 18.5 px regular; prefer bold for small UI labels.
- Gold on White: decorative only; not for body text or fine UI text.
- Focus: 3 px outer ring in Rotary Gold with 1 px inner white gap on dark surfaces.

#### **F) Implementation Tokens**
CSS custom properties (excerpt):
```css
--ri-blue: #17458F;
--ri-gold: #F7A81B;
--ri-charcoal: #54565A;
--ri-slate: #657F99;
--ri-success: #009739;
--ri-error: #D41367;
--ri-warning: #FF7600;
--ri-info: #00A2E0;
--ri-surface: #FFFFFF;
--ri-surface-alt: #F8FAFC;
```

Theming: Light default; optional dark elevates Royal Blue 10% (mix with black) and shifts neutrals; Gold remains unchanged. Do not theme the logo colors.

---

### **3. Typography (Rotary-Approved)**

#### **A) Primary Families**
- Frutiger (licensed) — preferred for headlines and navigation when available.
- Open Sans (free) — approved primary alternative.
- Arial (free) — fallback. ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/brand-elements/typography))

#### **B) System for the Site**
Web-safe, multilingual, cost-aware:
- Headings and navigation: Open Sans (preferred in web stack), weight 700/600.
- Body: Open Sans 400/500 for consistency across FR/EN/AR, excellent Arabic support in widely available subsets.
- Numeric tables and stats: Open Sans 600; tabular-nums for alignment.
- Fallback stack: "Open Sans", "Arial", system-ui, sans-serif.

#### **C) Type Scale (Desktop / Mobile)**
- H1: 40 / 32, 1.15 lh, -0.5 letter-spacing; color Royal Blue or Black depending on background.
- H2: 32 / 26, 1.2 lh, -0.25 ls; color Royal Blue.
- H3: 24 / 22, 1.3 lh, 0 ls; color Charcoal.
- Body L: 18 / 18, 1.65 lh; color Charcoal.
- Body M (default): 16 / 16, 1.65 lh; color Charcoal.
- Caption/Meta: 13 / 13, 1.4 lh; color Slate.
- Button/Link label: 15 / 15, 1.2 lh; semibold; uppercase permitted for short CTAs only.

#### **D) Link & Emphasis Rules**
- Links: Royal Blue, underline on hover/focus; visited tone unchanged (brand consistency).
- Emphasis: use weight before italics; avoid long italic spans for AR legibility.
- Headlines avoid title case in FR/AR; use sentence case for clarity.

#### **E) RTL Typography (Arabic)**
- Direction: dir="rtl" on html when AR active; text-align: start; list markers mirrored.
- Numerals: Western Arabic 0–9 for Tunisia locale; set font-variant-numeric: tabular-nums for tables and stats.
- Alignment: keep numbers and URLs LTR; wrap with <bdi> as necessary.

---

### **4. Layout, Spacing, Grid**

#### **A) Grid**
- 12-column fluid grid, 72–84 px gutters at ≥ 1280; 24 px gutters tablet; 16 px mobile.
- Max content width 1200 px; wide media up to 1440 px for hero.
- RTL: grid gutters and offsets mirror automatically with logical properties (margin-inline, padding-inline).

#### **B) Spacing System**
- Base unit: 8 px.
- Spacing tokens: 0, 4, 8, 12, 16, 24, 32, 40, 48, 64, 80, 96.
- Section rhythm: 80–96 px top/bottom desktop, 48–64 px mobile.
- Card internals: 16–24 px; form fields 12–16 px padding.

#### **C) Elevation, Borders, Motion**
- Border radius: 8 px default; 12–16 px for callouts; 999 px for pills.
- Shadow tiers (subtle, medium, raised): use opacity-only shadows; avoid colorized glows except focus rings.
- Motion: 150–250 ms ease-out for hover; 200–300 ms ease-in-out for transitions; honor reduced motion.

#### **D) Responsive Breakpoints**
- Mobile: < 768 px
- Tablet: 768–1024 px
- Desktop: > 1024 px
- Extra wide: > 1440 px (progressively enhance only)

#### **E) Content Patterns**
- Hero: 60–70% viewport height on desktop; min 420 px; text on solid or 40–60% overlay over photography.
- Vertical rhythm: consistent baseline; avoid stacking multiple brand colors in one band — prefer white surfaces with brand accents.

---

### **5. Components & Patterns (Specifications)**

#### **General Rules for All Components**
- States: default, hover, focus-visible, active, disabled.
- Interaction: transition 200 ms ease-in-out; cursor pointers for interactive elements.
- Accessibility: roles/ARIA where applicable; touch targets ≥ 44 × 44 px; visible labels; error text programmatically associated; aligned with user-experience/README.md for error handling and validation.

#### **5.1 Buttons**
- **Primary (Brand)**: Background Royal Blue; text White; padding 12×20; radius 8; shadow subtle on hover.
  - Hover: darken Royal Blue by ~8%.
  - Focus: 3 px outer ring in Rotary Gold with 1 px inner white gap.
  - Disabled: 40% opacity; no hover.
- **Secondary (Outline)**: Border 2 px Royal Blue; text Royal Blue; background White.
  - Hover: fill Royal Blue; text White.
- **Tertiary (Ghost)**: Text Royal Blue on transparent; underline appears on hover; reserved for low-emphasis actions.
- **Destructive**: Background Cranberry; text White; confirm step for irreversible actions.

#### **5.2 Navigation Bar (Sticky)**
- Left: Club logo (official lockup).
- Center/Right: Menu items (max 6), Search, Language toggle (FR | AR | EN).
- Mobile: Hamburger; slide-in menu; focus trapped in drawer.
- RTL: item order mirrors; search icon remains at visual end.
- States: current page indicated by 2 px underline (Royal Blue) and aria-current="page".

#### **5.3 Hero — "People of Action"**
- Media: still image or short video; respectful, real community moments; avoid staged stock.
- Messaging: Headline 6–10 words; optional supporting line; one Primary CTA.
- Overlay: gradient or solid brand surface to maintain AA contrast.
- Use People of Action language guidance (short, inspiring, action-oriented). ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/people-of-action))

#### **5.4 Cards (News, Projects, Events)**
- Anatomy: Media 16:9 → Title → Meta (date, location) → Excerpt → CTA.
- Padding: 16–20; radius 12; shadow medium on hover; entire card clickable with overlay link (preserve internal link semantics).
- Event card badge: date chip uses Royal Blue background, White text; location icon Slate.

#### **5.5 Forms (Enhanced for Accessibility Integration)**
- Inputs: 1 px border Smoke; radius 8; padding 12×12.
- Focus: 2 px Royal Blue ring + 1 px white offset on dark.
- Error: border Cranberry; helper text Cranberry; aria-invalid="true"; describedby links to error id; inline validation on blur; summary at form top with anchor links.
- Success: border Grass momentarily; do not rely solely on color.
- Labels always visible; placeholders are not labels; all error messages announced with ARIA live regions; logical tab order and focus management.

#### **5.6 Modal / Dialog**
- Role="dialog" with aria-modal="true"; focus trap; ESC to close; dismiss "X" button labeled.
- Width: 560–720; padding 24; radius 12; backdrop rgba(0,0,0,.5).
- Buttons: Primary right, Secondary left; order mirrors in RTL.

#### **5.7 Impact Stats Block**
- Grid: 3-up desktop, 1–2-up mobile.
- Icon: 40 px line icon; color Royal Blue.
- Number: large (H2 size or 48 px), semibold; unit clear.
- Label: Body M; Slate.
- Data: fetch from CMS fields (numbers change; do not hardcode). Use examples like "members" or "volunteer hours," but keep values CMS-driven to stay current with Rotary reporting.

#### **5.8 Iconography**
- Style: simple line icons, 2 px stroke, 24 px default; fill on active.
- Color: Royal Blue default; Gold for highlights; Slate for secondary meta.
- Required set: Calendar, User, Email, Phone, Language, Menu, Search, Arrow (prev/next), Location, Social (Facebook/Instagram/X).
- Accessibility: aria-hidden="true" if decorative; aria-label on actionable buttons.

#### **5.9 Media & Photography**
- Real people, real service; diverse, respectful representation; avoid stereotypes.
- Composition: eye level, candid action; leave safe area for headlines/CTAs.
- Filters: none; maintain authentic color.
- Always pair with alt text describing the action (not just "image").
- Align with People of Action example assets/templates for social and print adaptation.

#### **5.10 Footer**
- Required links: About, Projects, Events, Join, Donate, Contact, Privacy.
- Club address and meeting info; social links with descriptive labels.
- Secondary nav uses Slate; background White or very light neutral; ensure contrast.

---

### **6. Developer Appendix — Implementation Notes**

#### **Design Tokens Structure**
Names only; store values from Sections 2–3:
- color.brand.primary, color.brand.accent, color.text.primary, color.text.muted, color.surface.base, color.status.success|warning|error|info
- font.family.primary, font.size.scale, font.weight.semibold|bold
- radius.sm|md|lg, shadow.sm|md|lg, spacing.0–96, grid.columns, grid.gutter

#### **CSS Examples**
- Use logical properties: padding-inline, margin-inline for instant RTL support.
- Prefer :focus-visible over :focus; don't remove outlines without replacement.

#### **Tailwind or Utility Layers**
Map tokens to theme.extend and include dir-based variants (rtl:).

#### **Performance**
Ship variable font if using Open Sans VF; preload key weights; set font-display: swap.

#### **Content Governance**
All metrics (members, volunteer hours, funds) are CMS fields with locale-aware number formatting; never render from hardcoded strings; integrate with CHANGELOG.md and Document-Cross-Reference.md for version tracking and alignment.

---

### **7. Implementation Examples**

#### **React Component & Token Usage**

Below is a complete React implementation example demonstrating how to use the Rotary design tokens and build components following the established patterns. This example includes multilingual support, accessibility features, and responsive design.

```javascript
/*
Rotary UI Kit - Drop-in tokens JSON + Example React component
How to use:
1. Copy the TOKENS object into your design system (JSON or SCSS/CSS variables).
2. Use the React component as an example reference for building components in your app.
3. To convert tokens to CSS custom properties, map TOKENS to :root in your build step.
*/

// ---- TOKENS (drop-in JSON-like object) ----
export const TOKENS = {
  "color": {
    "brand": {
      "royalBlue": "#17458F",
      "royalBlueDark": "#123F7A",
      "gold": "#F7A81B"
    },
    "status": {
      "success": "#009739",
      "error": "#D41367",
      "warning": "#FF7600",
      "info": "#00A2E0"
    },
    "neutral": {
      "white": "#FFFFFF",
      "nearBlack": "#111111",
      "trueBlack": "#000000",
      "charcoal": "#54565A",
      "slate": "#657F99",
      "smoke": "#B1B1B1",
      "powderBlue": "#B9D9EB"
    },
    "surface": {
      "overlay": "rgba(23,69,143,0.6)"
    }
  },
  "typography": {
    "family": {
      "primary": "\"Open Sans\", Arial, system-ui, sans-serif"
    },
    "link": {
      "hoverColor": "rgb(18,63,122)",
      "visitedStyle": "inherit"
    },
    "scale": {
      "h1": "56px",
      "h2": "40px",
      "h3": "28px",
      "body": "16px",
      "small": "13px"
    },
    "weights": {
      "regular": 400,
      "medium": 500,
      "semibold": 600,
      "bold": 700
    }
  },
  "spacing": {
    "base": 8,
    "scale": [0,4,8,12,16,24,32,40,48,64]
  },
  "radius": {
    "sm": "6px",
    "md": "8px",
    "lg": "12px",
    "pill": "999px"
  },
  "shadow": {
    "sm": "0 1px 2px rgba(16,24,40,0.05)",
    "md": "0 6px 18px rgba(16,24,40,0.08)"
  },
  "grid": {
    "columns": 12,
    "containerMax": "1200px"
  }
};

// ---- Example helper: map tokens to CSS variables ----
export function injectCssVariables(prefix = '--ri') {
  if (typeof document === 'undefined') return;
  const root = document.documentElement;
  const set = (k, v) => root.style.setProperty(`${prefix}-${k}`, v);

  // colors
  Object.entries(TOKENS.color.brand).forEach(([k,v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.status).forEach(([k,v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.neutral).forEach(([k,v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.surface).forEach(([k,v]) => set(`color-${k}`, v));
  // typography
  Object.entries(TOKENS.typography.link).forEach(([k,v]) => set(`typo-link-${k}`, v));
  // radius
  Object.entries(TOKENS.radius).forEach(([k,v]) => set(`radius-${k}`, v));
  // shadows
  Object.entries(TOKENS.shadow).forEach(([k,v]) => set(`shadow-${k}`, v.replace(/"/g,'\\"')));
}
```

#### **Complete Page Implementation Example**

```javascript
import React, { useEffect, useState } from 'react';
import { TOKENS, injectCssVariables } from './rotary-tokens';

export default function RotaryHomepageDemo() {
  useEffect(() => { injectCssVariables(); }, []);

  const [lang, setLang] = useState('fr');
  const [dir, setDir] = useState('ltr');

  useEffect(() => {
    setDir(lang === 'ar' ? 'rtl' : 'ltr');
  }, [lang]);

  const stats = [
    { number: '1.2 million', label: 'members worldwide' },
    { number: '47 million', label: 'volunteer hours' },
    { number: '$291 million', label: 'for sustainable projects' }
  ];

  const events = [
    { title: 'Community Cleanup', location: 'Tunis', date: 'Sept 14, 2025' },
    { title: 'Youth Leadership Camp', location: 'Sousse', date: 'Oct 20, 2025' },
    { title: 'Health & Wellness Fair', location: 'Sfax', date: 'Nov 5, 2025' }
  ];

  return (
    <div dir={dir} className="min-h-screen bg-surfaceBase text-textPrimary dark:bg-surfaceBase-dark dark:text-textPrimary-dark" style={{fontFamily: TOKENS.typography.family.primary}}>
      {/* Navigation Header */}
      <header className="w-full shadow-sm bg-surfaceBase fixed top-0 left-0 right-0 z-10 dark:bg-surfaceBase-dark" style={{boxShadow: TOKENS.shadow.sm}}>
        <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full flex items-center justify-center bg-(--ri-color-royalBlue) text-white font-bold text-sm">RD</div>
            <div className="hidden sm:block">
              <div className="text-sm font-semibold text-(--ri-color-royalBlue) leading-tight">Rotary Club</div>
              <div className="text-xs text-(--ri-color-slate) leading-tight">Tunis Doyen</div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <nav className="hidden md:flex gap-6 text-sm font-medium text-textSecondary dark:text-textSecondary-dark">
              <a className="hover:text-(--ri-color-royalBlue) dark:hover:text-(--ri-color-gold) transition-colors duration-200" href="#">About</a>
              <a className="hover:text-(--ri-color-royalBlue) dark:hover:text-(--ri-color-gold) transition-colors duration-200" href="#">Our Causes</a>
              <a className="hover:text-(--ri-color-royalBlue) dark:hover:text-(--ri-color-gold) transition-colors duration-200" href="#">News</a>
              <a className="hover:text-(--ri-color-royalBlue) dark:hover:text-(--ri-color-gold) transition-colors duration-200" href="#">Get Involved</a>
            </nav>

            {/* Dark Mode Toggle */}
            <button
              onClick={() => document.documentElement.classList.toggle('dark')}
              className="p-2 rounded-md bg-surfaceCard dark:bg-surfaceCard-dark text-textSecondary dark:text-textSecondary-dark hover:bg-opacity-80"
              aria-label="Toggle dark mode"
            >
              <span className="block dark:hidden">🌙</span>
              <span className="hidden dark:block">☀️</span>
            </button>

            <select aria-label="Language" value={lang} onChange={(e)=>setLang(e.target.value)} className="border rounded-md px-2 py-1 text-sm bg-surfaceCard border-gray-300 dark:bg-surfaceCard-dark dark:border-gray-600 text-textPrimary dark:text-textPrimary-dark focus:outline-none focus:ring-2 focus:ring-(--ri-color-royalBlue)">
              <option value="en">EN</option>
              <option value="fr">FR</option>
              <option value="ar">AR</option>
            </select>
            <a href="#" className="hidden sm:inline-flex items-center px-4 py-2 rounded-md text-white font-semibold transition-colors duration-200 hover:bg-(--ri-color-royalBlueDark)" style={{backgroundColor: TOKENS.color.brand.royalBlue}}>Donate</a>
          </div>
        </div>
      </header>
      <div className="h-16"></div>

      {/* Hero Section */}
      <main>
        <section className="relative w-full h-[50vh] md:h-[65vh] flex items-center justify-center text-center px-4" style={{backgroundImage: "url(https://images.unsplash.com/photo-1524504388940-b1c1722653e1?q=80&w=1600&auto=format&fit=crop)", backgroundSize: 'cover', backgroundPosition: 'center'}}>
          <div className="absolute inset-0 bg-surfaceOverlay dark:bg-surfaceOverlay-dark"></div>
          <div className="relative text-white max-w-2xl">
            <h1 className="text-[36px] sm:text-[48px] md:text-[56px] font-bold leading-tight drop-shadow-md">
              We are <span className="text-(--ri-color-gold)">People of Action</span>
            </h1>
            <p className="mt-4 text-sm sm:text-base font-medium drop-shadow-sm">A worldwide network of professional and community leaders helping to find solutions for the world's most pressing challenges.</p>
            <a href="#" className="inline-flex items-center mt-8 px-8 py-3 rounded-md text-white font-semibold transition-colors duration-200 hover:bg-(--ri-color-royalBlueDark)" style={{backgroundColor: TOKENS.color.brand.royalBlue}}>
              Take Action With Us
            </a>
          </div>
        </section>

        {/* Impact Stats */}
        <section className="bg-surfaceBase dark:bg-surfaceBase-dark py-12 px-4">
          <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-3 gap-8">
            {stats.map((s, i) => (
              <div key={i} className="text-center">
                <div className="text-[40px] font-bold text-(--ri-color-royalBlue)">{s.number}</div>
                <div className="text-lg font-medium mt-1 text-textPrimary dark:text-textPrimary-dark">{s.label}</div>
              </div>
            ))}
          </div>
        </section>

        {/* Service Areas */}
        <section className="py-16 px-4 bg-gray-50 dark:bg-gray-900">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-[40px] font-bold text-center text-(--ri-color-royalBlue)">What We Do</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
              <div className="bg-surfaceCard dark:bg-surfaceCard-dark rounded-lg shadow-sm p-6" style={{boxShadow: TOKENS.shadow.sm}}>
                <h3 className="text-[28px] font-bold text-textPrimary dark:text-textPrimary-dark">Community Projects</h3>
                <p className="mt-2 text-textSecondary dark:text-textSecondary-dark">We lead and participate in local service projects that have a lasting impact in our communities.</p>
              </div>
              <div className="bg-surfaceCard dark:bg-surfaceCard-dark rounded-lg shadow-sm p-6" style={{boxShadow: TOKENS.shadow.sm}}>
                <h3 className="text-[28px] font-bold text-textPrimary dark:text-textPrimary-dark">Global Initiatives</h3>
                <p className="mt-2 text-textSecondary dark:text-textSecondary-dark">Rotary is a global network of leaders and we are part of initiatives that tackle the world's biggest challenges.</p>
              </div>
              <div className="bg-surfaceCard dark:bg-surfaceCard-dark rounded-lg shadow-sm p-6" style={{boxShadow: TOKENS.shadow.sm}}>
                <h3 className="text-[28px] font-bold text-textPrimary dark:text-textPrimary-dark">Youth Programs</h3>
                <p className="mt-2 text-textSecondary dark:text-textSecondary-dark">We support future generations with youth leadership development and exchange programs.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Events Section */}
        <section className="py-16 px-4 bg-surfaceBase dark:bg-surfaceBase-dark">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-[40px] font-bold text-center text-(--ri-color-royalBlue)">Upcoming Events</h2>
            <div className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {events.map((event, i) => (
                <article key={i} className="flex flex-col rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-surfaceCard dark:bg-surfaceCard-dark">
                  <div className="p-6 grow">
                    <h3 className="text-xl font-semibold text-textPrimary dark:text-textPrimary-dark">{event.title}</h3>
                    <p className="mt-1 text-sm font-medium text-textSecondary dark:text-textSecondary-dark">{event.location}</p>
                    <time className="mt-1 text-sm font-medium block text-textSecondary dark:text-textSecondary-dark">{event.date}</time>
                  </div>
                  <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="#" className="inline-flex items-center px-6 py-3 rounded-md text-white font-semibold transition-colors duration-200 hover:bg-(--ri-color-royalBlueDark)" style={{backgroundColor: TOKENS.color.brand.royalBlue}}>
                      Register Now
                    </a>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t py-8 mt-16 bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="text-sm text-textSecondary dark:text-textSecondary-dark">© {new Date().getFullYear()} Rotary Club Tunis Doyen — All rights reserved</div>
          <div className="flex gap-4">
            <a href="#" className="text-sm hover:text-(--ri-color-royalBlue) dark:hover:text-(--ri-color-gold) transition-colors duration-200 text-textSecondary dark:text-textSecondary-dark">Privacy Policy</a>
            <a href="#" className="text-sm hover:text-(--ri-color-royalBlue) dark:hover:text-(--ri-color-gold) transition-colors duration-200 text-textSecondary dark:text-textSecondary-dark">Contact Us</a>
          </div>
        </div>
      </footer>
    </div>
  );
}
```

#### **Integration with Existing Systems**

- **CSS Custom Properties**: The `injectCssVariables()` function maps tokens to CSS variables for use with Tailwind or custom styles
- **Multilingual Support**: Language switching with RTL direction support
- **Accessibility**: ARIA labels, keyboard navigation, and focus management
- **Responsive Design**: Mobile-first approach with progressive enhancement
- **Performance**: Optimized images and efficient CSS usage

---

### **8. Tailwind CSS Integration & Dark Mode**

#### **Tailwind v4 Configuration**

The following Tailwind configuration provides semantic token mappings and automatic dark mode support:

```javascript
// tailwind.config.js (Tailwind v4 + Rotary dark mode defaults)
import { defineConfig } from "tailwindcss";

export default defineConfig({
  darkMode: "class", // toggle with <html class="dark">
  theme: {
    extend: {
      colors: {
        // --- Base brand ---
        brand: {
          royalBlue: "#17458F",
          royalBlueDark: "#123F7A",
          gold: "#F7A81B",
        },
        status: {
          success: "#009739",
          error: "#D41367",
          warning: "#FF7600",
          info: "#00A2E0",
        },

        // --- Neutral base tokens ---
        neutral: {
          white: "#FFFFFF",
          nearBlack: "#111111",
          trueBlack: "#000000",
          charcoal: "#54565A",
          slate: "#657F99",
          smoke: "#B1B1B1",
          powderBlue: "#B9D9EB",
        },

        surface: {
          overlay: "rgba(23,69,143,0.6)",
        },

        // --- Semantic light/dark mappings ---
        surfaceBase: {
          DEFAULT: "#FFFFFF", // light mode bg
          dark: "#111111", // dark mode bg
        },
        surfaceCard: {
          DEFAULT: "#FFFFFF",
          dark: "#1C1C1C",
        },
        surfaceOverlay: {
          DEFAULT: "rgba(23,69,143,0.6)",
          dark: "rgba(17,17,17,0.85)",
        },
        textPrimary: {
          DEFAULT: "#111111",
          dark: "#FFFFFF",
        },
        textSecondary: {
          DEFAULT: "#54565A",
          dark: "#B1B1B1",
        },
        textLink: {
          DEFAULT: "#17458F",
          dark: "#F7A81B",
        },
      },

      fontFamily: {
        sans: ["Open Sans", "Arial", "system-ui", "sans-serif"],
      },
      fontSize: {
        h1: ["56px", { lineHeight: "64px", fontWeight: "700" }],
        h2: ["40px", { lineHeight: "48px", fontWeight: "600" }],
        h3: ["28px", { lineHeight: "36px", fontWeight: "600" }],
        body: ["16px", { lineHeight: "24px", fontWeight: "400" }],
        small: ["13px", { lineHeight: "20px", fontWeight: "400" }],
      },
    },
  },
  plugins: [],
});
```

#### **Dark Mode Usage Examples**

```html
<!-- Background and text swap automatically in dark mode -->
<div class="bg-surfaceBase text-textPrimary dark:bg-surfaceBase-dark dark:text-textPrimary-dark">
  <h1 class="text-h1">Rotary Heading</h1>
  <p class="text-body">This text adjusts based on dark mode.</p>
  <a href="#" class="text-textLink dark:text-textLink-dark">Learn more</a>
</div>

<!-- Card with dark mode -->
<div class="bg-surfaceCard text-textSecondary dark:bg-surfaceCard-dark dark:text-textSecondary-dark p-6 rounded-2xl shadow">
  Accessible dark-mode aware card
</div>
```

#### **Dark Mode Implementation Benefits**

- **Consistency**: Developers use semantic classes like `bg-surfaceBase` instead of manual color values
- **Future-Proofing**: Token updates automatically apply across light and dark modes
- **Accessibility**: Contrast ratios maintained in both themes
- **Performance**: CSS custom properties enable efficient theme switching
- **Developer Velocity**: No need for extensive `dark:` overrides throughout the codebase

#### **Theme Switching Implementation**

```javascript
// Theme toggle utility
function toggleTheme() {
  const html = document.documentElement;
  const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
  const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

  html.classList.remove(currentTheme);
  html.classList.add(newTheme);
  localStorage.setItem('theme', newTheme);
}

// Initialize theme on page load
function initializeTheme() {
  const savedTheme = localStorage.getItem('theme');
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const theme = savedTheme || (systemPrefersDark ? 'dark' : 'light');

  document.documentElement.classList.add(theme);
}
```

---

### **9. Why This Matters (Team Orientation)**

#### **Fidelity**
Colors, type, and logo usage adhere to Rotary Brand Center — protecting global recognition. ([brandcenter.rotary.org](https://brandcenter.rotary.org/en-us/our-brand/brand-elements/colors))

#### **Inclusivity**
Tunisia-specific RTL and numeral rules reduce friction for Arabic readers; full WCAG 2.2 AA compliance with forms integration for accessible user experiences.

#### **Velocity**
Tokenized system lets designers and developers iterate without brand drift; cross-referenced with PRD, User Stories, and Architecture for seamless implementation.

#### **Consistency**
People of Action language and templates unify campaign work across channels; production-level readiness with updated UX and accessibility docs.

---

### **Cross-References to Technical Implementation**

| **Design Element** | **Related Collection** | **Related Hook/Endpoint** | **Related User Flow** | **Technical Implementation** |
|-------------------|----------------------|---------------------------|----------------------|-----------------------------|
| **Hero Section** | `home_page` | N/A | Homepage → Browse Events | Static generation with ISR |
| **Impact Stats** | Dynamic CMS fields | `/api/impact-stats` endpoint | Homepage display | Animated counters with icons |
| **Navigation** | N/A | N/A | All user flows | Sticky navigation with language toggle |
| **Forms** | `event_registrations`, `members` | `after_create` webhook | Registration flows | Real-time validation with ARIA |
| **Cards** | `news`, `projects`, `events` | N/A | Content browsing | Responsive grid with hover states |
| **Colors** | N/A | N/A | All visual elements | CSS custom properties with theming |
| **Typography** | All collections | N/A | All text content | Multilingual font stacks with RTL |

---

**Related Documentation:**
- PRD v1.2: `docs/rotary-requirements/Project Requirements Document.md`
- User Stories & Personas v1.0: `docs/rotary-requirements/User-Stories-Personas.md`
- Content Model & Architecture Guide v1.1: `docs/content-modeling/README.md`
- Architecture Guide (Updated): `docs/architecture/README.md`
- User Experience Documentation v1.0: `docs/user-experience/README.md`
- Accessibility Forms Guidelines v1.0: `docs/user-experience/accessibility-forms.md`
- Document Cross-Reference Matrix: `docs/Document-Cross-Reference.md`
- Change Log: `docs/audit-trail/ChangeLog.md`