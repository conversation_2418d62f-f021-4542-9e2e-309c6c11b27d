const fs = require('fs');

// Read the file
let content = fs.readFileSync('src/utilities/emailTemplates.ts', 'utf8');

// Find the allowedStyles section and replace it
const startMarker = 'allowedStyles: {';
const endMarker = '    }';

// Find the start position
const startPos = content.indexOf(startMarker);
if (startPos === -1) {
  console.error('Could not find allowedStyles section');
  process.exit(1);
}

// Find the end position (matching closing brace)
let braceCount = 1;
let pos = startPos + startMarker.length;
while (braceCount > 0 && pos < content.length) {
  if (content[pos] === '{') {
    braceCount++;
  } else if (content[pos] === '}') {
    braceCount--;
  }
  pos++;
}

if (braceCount !== 0) {
  console.error('Could not find matching closing brace for allowedStyles');
  process.exit(1);
}

// Extract the part before and after the section to replace
const before = content.substring(0, startPos);
const after = content.substring(pos);

// Create the new allowedStyles section
const newSection = `allowedStyles: {
      '*': {
        'font-family': [new RegExp('^[\\w\\s,-]+$')],
        'max-width': [new RegExp('^\\d+(px|em|%)$')],
        'margin': [new RegExp('^\\d+(px|em|%)(\\s\\d+(px|em|%)){0,3}$')],
        'padding': [new RegExp('^\\d+(px|em|%)(\\s\\d+(px|em|%)){0,3}$')],
        'text-align': ['left', 'right', 'center', 'justify'],
        'background': [new RegExp('^[\\w\\s\\(\\)\\#,.-]+$')],
        'color': [new RegExp('^[\\w\\s\\(\\)\\#,.-]+$')],
        'font-size': [new RegExp('^\\d+(px|em|%)$')],
        'border-radius': [new RegExp('^\\d+(px|em|%)$')],
        'border-left': [new RegExp('^(\\d+px\\s\\w+\\s[\\w\\#]+)$')],
        'border-right': [new RegExp('^(\\d+px\\s\\w+\\s[\\w\\#]+)$')],
        'direction': ['ltr', 'rtl'],
        'width': [new RegExp('^\\d+(px|em|%)$')],
      }`;

// Combine everything back together
content = before + newSection + after;

// Write the fixed content back to the file
fs.writeFileSync('src/utilities/emailTemplates.ts', content);

console.log('Fixed allowedStyles section in emailTemplates.ts');