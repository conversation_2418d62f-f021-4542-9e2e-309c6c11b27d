
import React from 'react';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Text,
} from '@react-email/components'

interface RegistrationEmailProps {
  userName: string
  eventTitle: string
  eventDate: string
  eventLocation: string
}

const bodyStyle = {
  backgroundColor: '#f6f6f6',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const containerStyle = {
  margin: '0 auto',
  padding: '20px 0 48px',
  width: '580px',
}

const headingStyle = {
  fontSize: '32px',
  lineHeight: '1.3',
  fontWeight: '700',
  color: '#484848',
}

const textStyle = {
  fontSize: '16px',
  lineHeight: '1.4',
  color: '#484848',
}

export const RegistrationEmail = ({
  userName,
  eventTitle,
  eventDate,
  eventLocation,
}: RegistrationEmailProps) => (
  <Html>
    <Head />
    <Body style={bodyStyle}>
      <Container style={containerStyle}>
        <Heading style={headingStyle}>Hello {userName},</Heading>
        <Text style={textStyle}>
          Thank you for registering for the event: <strong>{eventTitle}</strong>
          .
        </Text>
        <Text style={textStyle}>
          The event will take place on <strong>{eventDate}</strong> at{' '}
          <strong>{eventLocation}</strong>.
        </Text>
        <Text style={textStyle}>
          We are looking forward to seeing you there!
        </Text>
      </Container>
    </Body>
  </Html>
)
