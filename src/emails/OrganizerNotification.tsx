import React from 'react';
import { Html, Head, Preview, Body, Container, Heading, Text, Button } from '@react-email/components';

interface OrganizerNotificationEmailProps {
  eventTitle: string;
  eventDate: string;
  eventLocation: string;
  attendeeName: string;
  attendeeEmail: string;
  registrationDate: string;
}

export const OrganizerNotificationEmail: React.FC<Readonly<OrganizerNotificationEmailProps>> = ({ 
  eventTitle,
  eventDate,
  eventLocation,
  attendeeName,
  attendeeEmail,
  registrationDate,
}) => (
  <Html>
    <Head />
    <Preview>New Registration for {eventTitle}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>New Event Registration</Heading>
        <Text style={text}>A new registration has been received for your event:</Text>
        <div style={eventDetails}>
          <Heading as="h3" style={h3}>{eventTitle}</Heading>
          <Text style={detailText}><strong>Date:</strong> {eventDate}</Text>
          <Text style={detailText}><strong>Location:</strong> {eventLocation}</Text>
        </div>
        <div style={attendeeDetails}>
          <Heading as="h4" style={h4}>Attendee Information:</Heading>
          <Text style={detailText}><strong>Name:</strong> {attendeeName}</Text>
          <Text style={detailText}><strong>Email:</strong> {attendeeEmail}</Text>
          <Text style={detailText}><strong>Registration Date:</strong> {registrationDate}</Text>
        </div>
        <Button style={button} href="https://www.rotary-tunis-doyen.org/admin">
          View Registrations
        </Button>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
};

const container = {
  border: '1px solid #eaeaea',
  borderRadius: '5px',
  margin: '40px auto',
  padding: '20px',
  width: '465px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: 'normal',
  textAlign: 'center' as const,
  margin: '30px 0',
  padding: '0',
};

const text = {
  color: '#333',
  fontSize: '14px',
  lineHeight: '24px',
};

const eventDetails = {
  background: '#f8f9fa',
  padding: '20px',
  borderRadius: '8px',
  margin: '20px 0',
  borderLeft: '4px solid #004A87',
};

const attendeeDetails = {
  background: '#f8f9fa',
  padding: '20px',
  borderRadius: '8px',
  margin: '20px 0',
};

const h3 = {
  margin: '0 0 10px 0',
  color: '#004A87',
};

const h4 = {
  margin: '0 0 10px 0',
  color: '#004A87',
};

const detailText = {
  margin: '5px 0',
};

const button = {
  backgroundColor: '#007bff',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '100%',
  padding: '10px',
};