import { Html, Head, Body, Container, Heading, Text } from '@react-email/components'

interface OrganizerNotificationEmailProps {
  eventTitle: string
  eventDate: string
  eventLocation: string
  attendeeName: string
  attendeeEmail: string
  registrationDate: string
}

export const OrganizerNotificationEmail = ({ eventTitle, eventDate, eventLocation, attendeeName, attendeeEmail, registrationDate }: OrganizerNotificationEmailProps) => (
  <Html>
    <Head />
    <Body style={bodyStyle}>
      <Container style={containerStyle}>
        <Heading style={headingStyle}>New Registration for {eventTitle}</Heading>
        <Text>Event: {eventTitle}</Text>
        <Text>Date: {eventDate}</Text>
        <Text>Location: {eventLocation}</Text>
        <Text>Attendee Name: {attendeeName}</Text>
        <Text>Attendee Email: {attendeeEmail}</Text>
        <Text>Registration Date: {registrationDate}</Text>
      </Container>
    </Body>
  </Html>
)

const bodyStyle = {
  fontFamily: 'Arial, sans-serif',
  backgroundColor: '#f6f6f6',
  margin: '0',
  padding: '0',
}

const containerStyle = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px',
  borderRadius: '8px',
  maxWidth: '600px',
}

const headingStyle = {
  color: '#333333',
  fontSize: '24px',
  marginBottom: '10px',
}
