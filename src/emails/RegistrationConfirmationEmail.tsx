import { Html, Head, Body, Container, Heading, Text } from '@react-email/components'

interface RegistrationEmailProps {
  userName: string
  eventTitle: string
  eventDate: string
  eventLocation: string
}

export const RegistrationConfirmationEmail = ({ userName, eventTitle, eventDate, eventLocation }: RegistrationEmailProps) => (
  <Html>
    <Head />
    <Body style={bodyStyle}>
      <Container style={containerStyle}>
        <Heading style={headingStyle}>Hello {userName}</Heading>
        <Text>Event: {eventTitle}</Text>
        <Text>Date: {eventDate}</Text>
        <Text>Location: {eventLocation}</Text>
      </Container>
    </Body>
  </Html>
)

const bodyStyle = {
  fontFamily: 'Arial, sans-serif',
  backgroundColor: '#f6f6f6',
  margin: '0',
  padding: '0',
}

const containerStyle = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px',
  borderRadius: '8px',
  maxWidth: '600px',
}

const headingStyle = {
  color: '#333333',
  fontSize: '24px',
  marginBottom: '10px',
}
