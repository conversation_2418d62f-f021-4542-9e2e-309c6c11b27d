import type { AccessArgs } from 'payload'

import type { User } from '@/payload-types'

type isAuthenticated = (args: AccessArgs<User>) => boolean

export const authenticated: isAuthenticated = ({ req: { user } }) => {
  // Check if user exists and their status is 'published' (or equivalent for active)
  // Adjust 'published' based on your actual user status field and values
  return Boolean(user && user._status === 'published');
}
