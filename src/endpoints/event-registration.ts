import type { Endpoint } from 'payload'
import { sendEmail } from '../utilities/emailService'
import { getRegistrationConfirmationTemplate, getOrganizerNotificationTemplate } from '../utilities/emailService'

// Event Registration Handler Endpoint
export const eventRegistration: Endpoint = {
  path: '/event-registration',
  method: 'post',
  handler: async (req) => {
    // 1. Critical: Authentication & Authorization
    if (!req.user) {
      return new Response(JSON.stringify({ error: 'Unauthorized. Please log in to register.' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      })
    }
    // Optional: Check for specific roles
    // if (req.user.role !== 'member') { ... }

    let eventId = ''

    try {
      // 2. Get form data from request (Payload v3 handles this differently)
      eventId = req.data?.eventId as string
      const formDataString = req.data?.formData as string

      // 3. Validate Input Data
      if (!eventId || !formDataString) {
        return new Response(JSON.stringify({ error: 'Missing required fields.' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      // Parse form data
      const formData = JSON.parse(formDataString)

      // Get the event details
      const event = await req.payload.findByID({
        collection: 'events',
        id: eventId,
        // Only fetch required fields to improve performance
        depth: 0,
      })

      if (!event) {
        return new Response(JSON.stringify({ error: 'Event not found.' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      // Check if registration is required
      if (!event.registrationRequired) {
        return new Response(JSON.stringify({ error: 'Registration not required for this event.' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      // Check registration deadline
      const deadline = event.registrationForm?.registrationDeadline
      const now = new Date()
      if (deadline && now > new Date(deadline)) {
        return new Response(JSON.stringify({ error: 'Registration deadline has passed.' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      // Check capacity limits (This logic is sound)
      const maxRegistrations = event.registrationForm?.maxRegistrations
      const currentAttendees = event.attendees?.length || 0
      const isWaitlisted = maxRegistrations && currentAttendees >= maxRegistrations

      if (isWaitlisted && !event.registrationForm?.allowWaitlist) {
        return new Response(JSON.stringify({ error: 'Event is full.' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      // 4. Handle File Uploads
      interface UploadedFileInfo {
        fieldName: string
        file: string // Media document ID
      }

      const uploadedFiles: UploadedFileInfo[] = []
      if (req.file) {
        // Here you would add file validation (type, size, virus scan)
        const uploadedFile = await req.payload.create({
          collection: 'media',
          data: { alt: `Registration file for ${event.title}` },
          file: req.file,
        })
        uploadedFiles.push({
          fieldName: req.file.name,
          file: uploadedFile.id,
        })
      }

      const newAttendee = {
        // 5. Use the authenticated user's ID for security
        userId: req.user.id,
        userEmail: req.user.email,
        // The rest of the validated formData
        ...formData,
        registrationDate: new Date().toISOString(),
        status: isWaitlisted ? 'waitlisted' : 'registered',
        uploadedFiles: uploadedFiles,
      }

      // 6. Update event with new attendee (Payload handles concurrency)
      const updatedAttendees = [...(event.attendees || []), newAttendee]

      await req.payload.update({
        collection: 'events',
        id: eventId,
        data: {
          attendees: updatedAttendees,
        },
      })

      // 7. Send confirmation and organizer notification emails asynchronously
      try {
        // Get user's preferred language (default to 'en')
        // Note: Language detection can be enhanced based on user preferences in future
        const userLanguage = 'en' // Default to English for now

        // Format event date for email
        const eventDateFormatted = new Date(event.eventDate).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })

        // Get user name (fallback to email username if no name)
        const userName = req.user?.name || req.user?.email?.split('@')[0] || 'Valued Member'

        // Send confirmation email to registrant
        const confirmationTemplate = await getRegistrationConfirmationTemplate(
          req.payload,
          event.title || 'Event',
          eventDateFormatted,
          event.location || 'TBD',
          userName,
          userLanguage
        )

        await sendEmail({
          to: req.user.email,
          subject: confirmationTemplate.subject,
          html: confirmationTemplate.html,
          text: confirmationTemplate.text,
        })

        // Send notification email to organizer if configured
        if (event.organizer?.email && event.organizer?.sendNotifications !== false) {
          const organizerTemplate = await getOrganizerNotificationTemplate(
            req.payload,
            event.title || 'Event',
            eventDateFormatted,
            event.location || 'TBD',
            userName,
            req.user.email,
            new Date().toLocaleDateString('en-US'),
            userLanguage
          )

          await sendEmail({
            to: event.organizer.email,
            subject: organizerTemplate.subject,
            html: organizerTemplate.html,
            text: organizerTemplate.text,
          })
        }

        console.log('Registration emails sent successfully')
      } catch (emailError) {
        // Log email error but don't fail registration
        console.error('Error sending registration emails:', emailError)
        req.payload.logger.error(`Email sending failed for event ${eventId}: ${emailError}`)
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: isWaitlisted ? 'You have been added to the waitlist.' : 'Registration successful.',
          attendeeId: newAttendee.userId,
          status: newAttendee.status,
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    } catch (error) {
      // 8. Log the error for debugging
      req.payload.logger.error(`Registration error for event ${eventId}: ${error}`)
      return new Response(JSON.stringify({ error: 'Registration failed.' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      })
    }
  },
}