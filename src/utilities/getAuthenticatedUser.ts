import type { User } from '@/payload-types';

/**
 * Get authenticated user from request
 * Extracts JWT token from Authorization header and fetches user data
 */
export async function getAuthenticatedUser(request: Request, payload: any): Promise<User | null> {
  try {
    // Extract token from Authorization header
    const authHeader = request.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('JWT ')) {
      return null
    }

    const token = authHeader.substring(4) // Remove 'JWT ' prefix

    // Use Payload's built-in me endpoint to get user data
    const meUserReq = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'}/api/users/me`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
    })

    if (!meUserReq.ok) {
      return null
    }

    const { user }: { user: User } = await meUserReq.json()
    return user
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}