import type { EmailTemplateType, SupportedLanguage } from '../types/email';
import React, { type ComponentType } from 'react';
import { render } from '@react-email/render';

// Enhanced EmailTemplate interface
interface EmailTemplateConfig {
  id: string;
  name: string;
  type: EmailTemplateType;
  language: SupportedLanguage;
  version: string;
  component: ComponentType<any>;
  props: Record<string, any>;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    tags: string[];
  };
}

// Template registry with version control
class EmailTemplateRegistry {
  private templates = new Map<string, EmailTemplateConfig>();
  
  register(config: EmailTemplateConfig): void {
    const key = `${config.type}-${config.language}-${config.version}`;
    this.templates.set(key, config);
  }
  
  async render(type: EmailTemplateType, language: SupportedLanguage, props: Record<string, any>): Promise<string> {
    const template = this.getLatestTemplate(type, language);
    if (!template) throw new Error(`Template not found: ${type}-${language}`);
    
    const Component = template.component;
    return render(React.createElement(Component, props));
  }
  
  private getLatestTemplate(type: EmailTemplateType, language: SupportedLanguage): EmailTemplateConfig | undefined {
    // This is a simplified version. A real versioning system would involve fetching
    // all templates for a given type and language, then determining the latest version.
    // For now, we'll just return the exact match if available.
    let latestTemplate: EmailTemplateConfig | undefined;
    let latestVersion = '0.0.0';

    for (const [key, config] of this.templates.entries()) {
      if (config.type === type && config.language === language) {
        if (this.compareVersions(config.version, latestVersion) > 0) {
          latestVersion = config.version;
          latestTemplate = config;
        }
      }
    }
    return latestTemplate;
  }

  private compareVersions(v1: string, v2: string): number {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);

    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const p1 = parts1[i] || 0;
      const p2 = parts2[i] || 0;

      if (p1 > p2) return 1;
      if (p1 < p2) return -1;
    }
    return 0;
  }
}

export const emailTemplateRegistry = new EmailTemplateRegistry();
