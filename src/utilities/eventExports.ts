import { Event } from '@/payload-types'

/**
 * Generate CSV content from attendee data
 * @param event - The event object with attendees
 * @returns CSV string content
 */
export const generateAttendeesCSV = (event: Event): string => {
  const attendees = event.attendees || []

  if (attendees.length === 0) {
    return 'No attendees registered for this event.'
  }

  // CSV Header
  const headers = [
    'Attendee Name',
    'Email Address',
    'Registration Date',
    'Registration Status',
    'Custom Fields Data',
    'Uploaded Files Count'
  ]

  // CSV Rows
  const rows = attendees.map((attendee) => [
    attendee.userName || 'N/A',
    attendee.userEmail || 'N/A',
    attendee.registrationDate
      ? new Date(attendee.registrationDate).toLocaleString()
      : 'N/A',
    attendee.status || 'registered',
    // Format custom fields as JSON string if present
    attendee.customFields
      ? JSON.stringify(attendee.customFields, null, 2)
      : 'None',
    attendee.uploadedFiles?.length.toString() || '0'
  ])

  // Combine headers and rows
  const allRows = [headers, ...rows]

  // Convert to CSV format
  return allRows
    .map(row =>
      row.map(field => {
        // Escape fields containing commas or quotes
        if (field.includes(',') || field.includes('"') || field.includes('\n')) {
          return `"${field.replace(/"/g, '""')}"`
        }
        return field
      }).join(',')
    )
    .join('\n')
}

/**
 * Create a filename for the exported CSV
 * @param event - The event object
 * @returns Formatted filename
 */
export const createCSVFilename = (event: Event): string => {
  const eventTitle = event.title?.replace(/[^a-zA-Z0-9-_]/g, '-').substring(0, 50) || 'event'
  const date = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
  return `${eventTitle}-attendees-${date}.csv`
}

/**
 * Complete export pipeline
 * @param event - The event object with attendees
 * @returns Object with filename and CSV content
 */
export const exportEventAttendees = (event: Event) => {
  const csvContent = generateAttendeesCSV(event)
  const filename = createCSVFilename(event)

  return {
    filename,
    content: csvContent,
    mimeType: 'text/csv',
    size: new Blob([csvContent]).size
  }
}