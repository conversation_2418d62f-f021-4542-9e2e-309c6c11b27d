// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

/**
 * Simple object check.
 * @param item
 * @returns {boolean}
 */
export const isObject = (obj: any): obj is Record<string, any> => {
  return Object.prototype.toString.call(obj) === '[object Object]';
};

/**
 * Deep merge two objects.
 * @param target
 * @param ...sources
 */
export function deepMerge<T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T {
  const result: T = { ...target };

  for (const source of sources) {
    if (!source) continue;

    for (const key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        const targetValue = result[key];
        const sourceValue = source[key];

        if (isObject(targetValue) && isObject(sourceValue)) {
          result[key as keyof T] = deepMerge(targetValue, sourceValue) as T[keyof T];
        } else {
          result[key as keyof T] = sourceValue as T[keyof T];
        }
      }
    }
  }

  return result;
}
