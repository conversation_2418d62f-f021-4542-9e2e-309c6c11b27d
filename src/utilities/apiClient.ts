// src/utilities/apiClient.ts
interface ApiErrorResponse {
  error: string;
  code: string;
  details?: Record<string, unknown>;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
}

export async function post<T>(url: string, data: any): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    const responseData = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: responseData.error || 'Unknown error',
        code: responseData.code || 'API_ERROR',
        details: responseData.details,
      };
    }

    return {
      success: true,
      data: responseData.data as T,
      message: responseData.message,
    };
  } catch (error) {
    console.error(`API POST request to ${url} failed:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error',
      code: 'NETWORK_ERROR',
    };
  }
}

export async function get<T>(url: string): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const responseData = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: responseData.error || 'Unknown error',
        code: responseData.code || 'API_ERROR',
        details: responseData.details,
      };
    }

    return {
      success: true,
      data: responseData.data as T,
      message: responseData.message,
    };
  } catch (error) {
    console.error(`API GET request to ${url} failed:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error',
      code: 'NETWORK_ERROR',
    };
  }
}