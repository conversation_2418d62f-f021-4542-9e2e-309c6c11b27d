import { describe, it, expect, vi, beforeEach } from 'vitest'
import { formatDateTime } from '../formatDateTime'

describe('formatDateTime', () => {
  beforeEach(() => {
    // Mock Date to ensure consistent test results
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2024-01-15T12:00:00Z')) // Monday, January 15, 2024
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should format a valid timestamp correctly', () => {
    const timestamp = '2023-12-25T15:30:45Z' // Monday, December 25, 2023
    const result = formatDateTime(timestamp)

    expect(result).toBe('12/25/2023')
  })

  it('should format current date when no timestamp provided', () => {
    const result = formatDateTime('')

    // Should use current mocked date: January 15, 2024
    expect(result).toBe('01/15/2024')
  })

  it('should format null timestamp as current date', () => {
    const result = formatDateTime(null as any)

    // Should use current mocked date: January 15, 2024
    expect(result).toBe('01/15/2024')
  })

  it('should format undefined timestamp as current date', () => {
    const result = formatDateTime(undefined as any)

    // Should use current mocked date: January 15, 2024
    expect(result).toBe('01/15/2024')
  })

  it('should format single digit month with leading zero', () => {
    const timestamp = '2024-01-15T12:00:00Z' // January (month 0+1=1)
    const result = formatDateTime(timestamp)

    expect(result).toBe('01/15/2024')
  })

  it('should format double digit month without leading zero', () => {
    const timestamp = '2023-12-25T12:00:00Z' // December (month 11+1=12)
    const result = formatDateTime(timestamp)

    expect(result).toBe('12/25/2023')
  })

  it('should format single digit day with leading zero', () => {
    const timestamp = '2024-03-05T12:00:00Z' // 5th
    const result = formatDateTime(timestamp)

    expect(result).toBe('03/05/2024')
    expect(result).toMatch(/^03\/05\/\d{4}$/)
  })

  it('should format double digit day without leading zero', () => {
    const timestamp = '2024-03-25T12:00:00Z' // 25th
    const result = formatDateTime(timestamp)

    expect(result).toBe('03/25/2024')
    expect(result).toMatch(/^03\/25\/\d{4}$/)
  })

  it('should handle leap year dates correctly', () => {
    const timestamp = '2024-02-29T12:00:00Z' // Leap year date
    const result = formatDateTime(timestamp)

    expect(result).toBe('02/29/2024')
  })

  it('should handle end of month correctly', () => {
    const timestamp = '2024-01-31T12:00:00Z' // January 31st
    const result = formatDateTime(timestamp)

    expect(result).toBe('01/31/2024')
  })

  it('should format the year correctly for current system year', () => {
    const result = formatDateTime('')

    // Should use mocked current year 2024
    expect(result).toBe('01/15/2024')
    expect(result).toMatch(/01\/15\/2024$/)
  })

  it('should return MM/DD/YYYY format consistently', () => {
    const timestamps = [
      '2023-01-01T00:00:00Z',
      '2023-12-31T23:59:59Z',
      '2022-06-15T12:30:45Z',
      '2021-03-07T09:15:30Z'
    ]

    const expected = [
      '01/01/2023',
      '12/31/2023',
      '06/15/2022',
      '03/07/2021'
    ]

    timestamps.forEach((timestamp, index) => {
      const result = formatDateTime(timestamp)
      expect(result).toBe(expected[index])
      expect(result).toMatch(/^\d{2}\/\d{2}\/\d{4}$/)
    })
  })

  it('should handle timezone conversions correctly', () => {
    // Test with different timezone strings
    const timestamp = '2023-12-25T00:00:00+05:00' // UTC+5 hours
    const result = formatDateTime(timestamp)

    // The Date constructor should handle the timezone conversion
    expect(result).toBe('12/24/2023') // Should convert to December 24th at UTC
    expect(result).toMatch(/^\d{2}\/\d{2}\/\d{4}$/)
  })
})