import { describe, it, expect } from 'vitest'
import { deepMerge, isObject } from '../deepMerge';

describe('deepMerge utility', () => {
  describe('isObject helper', () => {
    it('should return true for plain objects', () => {
      expect(isObject({})).toBe(true)
      expect(isObject({ key: 'value' })).toBe(true)
      expect(isObject({ nested: { data: 123 } })).toBe(true)
    })

    it('should return false for non-objects', () => {
            expect(isObject(null)).toBe(false);
      expect(isObject(undefined)).toBe(false)
      expect(isObject('string')).toBe(false)
      expect(isObject(123)).toBe(false)
      expect(isObject(true)).toBe(false)
      expect(isObject(Symbol('test'))).toBe(false)
    })

    it('should return false for arrays', () => {
      expect(isObject([])).toBe(false)
      expect(isObject([1, 2, 3])).toBe(false)
      expect(isObject(['a', 'b', 'c'])).toBe(false)
    })

    it('should return false for functions', () => {
      expect(isObject(() => {})).toBe(false)
      expect(isObject(function test() {})).toBe(false)
    })

    it('should return false for dates', () => {
      expect(isObject(new Date())).toBe(false)
    })

    it('should return false for regex', () => {
      expect(isObject(/test/)).toBe(false)
      expect(isObject(new RegExp('test'))).toBe(false)
    })
  })

  describe('deepMerge function', () => {
    it('should merge simple objects', () => {
      const target = { a: 1, b: 2 }
      const source = { c: 3, d: 4 }
      const result = deepMerge(target, source)

      expect(result).toEqual({ a: 1, b: 2, c: 3, d: 4 })
    })

    it('should override target properties with source properties', () => {
      const target = { a: 1, b: 2 }
      const source = { a: 10, b: 20 }
      const result = deepMerge(target, source)

      expect(result).toEqual({ a: 10, b: 20 })
    })

    it('should perform deep merge on nested objects', () => {
      const target = {
        a: {
          b: 1,
          c: { d: 2 }
        }
      }
      const source = {
        a: {
          e: 3,
          c: { f: 4 }
        }
      }
      const expected = {
        a: {
          b: 1,
          c: { d: 2, f: 4 },
          e: 3
        }
      }

      const result = deepMerge(target, source)
      expect(result).toEqual(expected)
    })

    it('should handle multiple nested levels', () => {
      const target = {
        level1: {
          level2: {
            level3: {
              value: 'original'
            }
          }
        }
      }
      const source = {
        level1: {
          level2: {
            level3: {
              newValue: 'added'
            },
            newLevel: 'new'
          }
        }
      }
      const expected = {
        level1: {
          level2: {
            level3: {
              value: 'original',
              newValue: 'added'
            },
            newLevel: 'new'
          }
        }
      }

      const result = deepMerge(target, source)
      expect(result).toEqual(expected)
    })

    it('should handle empty objects', () => {
      const target = {}
      const source = { a: 1, b: 2 }
      const result = deepMerge(target, source)

      expect(result).toEqual({ a: 1, b: 2 })
    })

    it('should handle merging into empty target', () => {
      const target = { existing: 'value' }
      const source = {}
      const result = deepMerge(target, source)

      expect(result).toEqual({ existing: 'value' })
    })

    it('should handle arrays (override, not merge)', () => {
      const target = { arr: [1, 2, 3] }
      const source = { arr: [4, 5, 6] }
      const result = deepMerge(target, source)

      expect(result).toEqual({ arr: [4, 5, 6] })
    })

    it('should handle primitive types', () => {
      const target = { str: 'hello', num: 42, bool: true }
      const source = { str: 'world', num: 100, bool: false }
      const result = deepMerge(target, source)

      expect(result).toEqual({ str: 'world', num: 100, bool: false })
    })

    it('should handle null and undefined values', () => {
      const target = { a: 1, b: null }
      const source = { b: undefined, c: null }
      const result = deepMerge(target, source)

      expect(result).toEqual({ a: 1, b: undefined, c: null })
    })

    it('should handle functions (override them)', () => {
      const target = {
        func: function original() { return 'original' },
        data: 'test'
      }
      const source = {
        func: function newOne() { return 'new' },
        data2: 'test2'
      }
      const result = deepMerge(target, source)

      expect(typeof result.func).toBe('function')
      expect(result.data).toBe('test')
      expect((result as any).data2).toBe('test2')
    })

    it('should handle complex nested structures', () => {
      const target = {
        users: [
          { id: 1, name: 'John' }
        ],
        config: {
          database: {
            host: 'localhost',
            port: 5432,
            credentials: {
              username: 'admin',
              password: 'secret'
            }
          }
        }
      }

      const source = {
        users: [
          { id: 2, name: 'Jane' }
        ],
        config: {
          database: {
            port: 5433,
            credentials: {
              password: 'newsecret',
              ssl: true
            },
            pools: 10
          }
        },
        newFeature: {
          enabled: true,
          settings: {
            timeout: 5000
          }
        }
      }

      const result = deepMerge(target, source)

      expect(result.users).toEqual([{ id: 2, name: 'Jane' }]) // Array override
      expect(result.config.database.host).toBe('localhost') // Original value preserved
      expect(result.config.database.port).toBe(5433) // Updated value
      expect(result.config.database.credentials.username).toBe('admin') // Nested preserve
      expect(result.config.database.credentials.password).toBe('newsecret') // Nested update
      expect((result as any).config.database.credentials.ssl).toBe(true) // Nested add
      expect((result as any).config.database.pools).toBe(10) // New nested property
      expect((result as any).newFeature.enabled).toBe(true) // New top-level property
    })

    it('should preserve original target object reference', () => {
      const target = { a: 1 }
      const source = { b: 2 }
      const result = deepMerge(target, source)

      expect(result).not.toBe(target) // Should create a new object
      expect(result).not.toBe(source) // Should not be the source
      expect(result).toEqual({ a: 1, b: 2 })
    })

    it('should handle edge cases with mixed data types', () => {
      const target = {
        value: 'string',
        array: [1, 2],
        object: { nested: 'nested' },
        func: function() { return true }
      }

      const source = {
        value: 123,
        array: { replaced: true },
        object: { newNested: 'newNested', nested: 'updated' },
        func: 'replaced by string'
      }

      const result = deepMerge(target, source)

      expect(result.value).toBe(123)
      expect(result.array).toEqual({ replaced: true })
      expect(result.object.nested).toBe('updated')
      expect((result as any).object.newNested).toBe('newNested')
      expect(result.func).toBe('replaced by string')
    })
  })
})