import { describe, it, expect } from 'vitest'
import { toKebabCase } from '../toKebabCase'

describe('toKebabCase', () => {
  describe('basic functionality', () => {
    it('should convert camelCase to kebab-case', () => {
      expect(toKebabCase('camelCase')).toBe('camel-case')
    })

    it('should convert PascalCase to kebab-case', () => {
      expect(toKebabCase('PascalCase')).toBe('pascal-case')
    })

    it('should handle consecutive uppercase letters (conservative splitting)', () => {
      expect(toKebabCase('XMLHttpRequest')).toBe('xmlhttp-request')
    })
  })

  describe('whitespace handling', () => {
    it('should replace single spaces with hyphens', () => {
      expect(toKebabCase('hello world')).toBe('hello-world')
    })

    it('should replace multiple spaces with single hyphen', () => {
      expect(toKebabCase('hello   world')).toBe('hello-world')
    })

    it('should handle mixed spaces and tabs', () => {
      expect(toKebabCase('hello\t  world')).toBe('hello-world')
    })

    it('should handle newlines and carriage returns (creates trailing hyphen)', () => {
      expect(toKebabCase('hello\nworld\r')).toBe('hello-world-')
    })

    it('should handle combination of spaces and camelCase', () => {
      expect(toKebabCase('helloWorld testCase')).toBe('hello-world-test-case')
    })
  })

  describe('case conversion', () => {
    it('should convert uppercase strings to lowercase', () => {
      expect(toKebabCase('HELLO WORLD')).toBe('hello-world')
    })

    it('should convert mixed case to lowercase', () => {
      expect(toKebabCase('Hello World Test')).toBe('hello-world-test')
    })

    it('should handle already lowercase strings', () => {
      expect(toKebabCase('hello world')).toBe('hello-world')
    })
  })

  describe('special characters and edge cases', () => {
    it('should handle strings with hyphens already present', () => {
      expect(toKebabCase('already-kebab')).toBe('already-kebab')
    })

    it('should handle strings with underscores', () => {
      expect(toKebabCase('snake_case')).toBe('snake_case')
    })

    it('should handle strings with numbers', () => {
      expect(toKebabCase('test123case')).toBe('test123case')
    })

    it('should handle strings with special characters', () => {
      expect(toKebabCase('test@#$%^&*()')).toBe('test@#$%^&*()')
    })
  })

  describe('complex combinations', () => {
    it('should handle camelCase with spaces', () => {
      expect(toKebabCase('userName field')).toBe('user-name-field')
    })

    it('should handle multi-word camelCase', () => {
      expect(toKebabCase('getUserEmailAddress')).toBe('get-user-email-address')
    })

    it('should handle PascalCase with spaces', () => {
      expect(toKebabCase('UserModel Fields')).toBe('user-model-fields')
    })

    it('should handle complex mixed case (conservative consecutive uppercase)', () => {
      expect(toKebabCase('HTTPStatusCode ErrorHandler')).toBe('httpstatus-code-error-handler')
    })
  })

  describe('input validation', () => {
    it('should handle single character strings', () => {
      expect(toKebabCase('a')).toBe('a')
      expect(toKebabCase('A')).toBe('a')
    })

    it('should handle empty string', () => {
      expect(toKebabCase('')).toBe('')
    })

    it('should handle string with only spaces', () => {
      expect(toKebabCase('   ')).toBe('-')
      expect(toKebabCase(' \t \n ')).toBe('-')
    })
  })

  describe('method chaining and optional chaining', () => {
    it('should handle null input gracefully', () => {
      expect(toKebabCase(null as any)).toBeUndefined()
    })

    it('should handle undefined input gracefully', () => {
      expect(toKebabCase(undefined as any)).toBe(undefined)
    })

    it('should use optional chaining correctly', () => {
      const input: string | null = null
      expect(toKebabCase(input!)).toBeUndefined()
    })

    it('should work with different string methods', () => {
      expect(toKebabCase('TEST_STRING'.toLowerCase())).toBe('test_string')
    })
  })

  describe('edge cases and performance', () => {
    it('should handle long strings efficiently', () => {
      const longString = 'A'.repeat(1000)
      const result = toKebabCase(longString)
      expect(result).toBe('a'.repeat(1000))
    })

    it('should handle strings with no modifications needed', () => {
      expect(toKebabCase('already-kebab-case')).toBe('already-kebab-case')
    })

    it('should handle strings needing all transformations', () => {
      expect(toKebabCase('XMLHttpRequest API Method')).toBe('xmlhttp-request-api-method')
    })
  })

  describe('idempotent behavior', () => {
    it('should be idempotent - applying multiple times gives same result', () => {
      const input = 'helloWorld TestCase'
      const firstPass = toKebabCase(input)
      const secondPass = toKebabCase(firstPass)

      expect(firstPass).toBe('hello-world-test-case')
      expect(secondPass).toBe('hello-world-test-case')
    })

    it('should produce consistent results', () => {
      const input = 'UserID Test String'
      const results = Array(10).fill(null).map(() => toKebabCase(input))

      expect(new Set(results).size).toBe(1) // All results should be identical
      expect(results[0]).toBe('user-id-test-string')
    })
  })

  describe('comprehensive test cases', () => {
    const testCases = [
      { input: 'simple', expected: 'simple' },
      { input: 'SimpleTest', expected: 'simple-test' },
      { input: 'HTMLParser', expected: 'htmlparser' },
      { input: 'test space', expected: 'test-space' },
      { input: 'UPPERCASE', expected: 'uppercase' },
      { input: 'kebab-case', expected: 'kebab-case' },
      { input: 'mixed_case Example', expected: 'mixed_case-example' },
      { input: 'APIEndpointController', expected: 'apiendpoint-controller' },
      { input: 'Test\tSpace\tAnd\nNewline', expected: 'test-space-and-newline' },
      { input: 'a b c d', expected: 'a-b-c-d' },
      { input: '  trim spaces  ', expected: '-trim-spaces-' }
    ]

    testCases.forEach(({ input, expected }) => {
      it(`should transform "${input}" to "${expected}"`, () => {
        expect(toKebabCase(input)).toBe(expected)
      })
    })
  })
})