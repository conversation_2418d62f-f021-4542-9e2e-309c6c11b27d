import { describe, it, expect, vi } from 'vitest'
import canUseDOM from '../canUseDOM'

describe('canUseDOM utility', () => {
  it('should test the actual canUseDOM export (evaluates at import time)', () => {
    // Since canUseDOM is evaluated at module import time based on globals:
    // export default !!(typeof window !== 'undefined' && window.document && window.document.createElement)

    // In Vitest with jsdom, window should be available, so canUseDOM should be true
    expect(canUseDOM).toBe(true)

    // Also verify the underlying logic that canUseDOM represents
    expect(typeof window).toBe('object')
    expect(window.document).toBeDefined()
    expect(typeof window.document.createElement).toBe('function')

    // The DOM availability logic should return true
    const DOMAvailable = !!(typeof window !== 'undefined' && window.document && window.document.createElement)
    expect(DOMAvailable).toBe(true)

    // canUseDOM should match this runtime evaluation
    expect(canUseDOM).toBe(DOMAvailable)
  })

})