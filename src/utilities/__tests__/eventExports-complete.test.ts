import { describe, it, expect } from 'vitest'
import { generateAttendeesCSV, createCSVFilename } from '../eventExports'
import { createMockEvent } from '../testing/mockFactory'

describe('CSV Export Comprehensive Tests', () => {
  describe('generateAttendeesCSV', () => {
    it('should handle empty attendees gracefully', () => {
      const event = createMockEvent({ attendeeCount: 0 })
      const result = generateAttendeesCSV(event)
      
      expect(result).toContain('No attendees registered')
    })
  })
  
  describe('createCSVFilename', () => {
    it('should create safe filename', () => {
      const event = createMockEvent({
        title: 'Event with Special: Characters?! <Test>',
        id: 'test'
      });
      
      const filename = createCSVFilename(event)
      expect(filename).toMatch(/^[a-zA-Z0-9\-_\.]+\.csv$/)
      expect(filename).not.toContain('<')
      expect(filename).not.toContain('>')
      expect(filename).not.toContain(':')
    })
  })
})
