// Basic CSV export test
import { describe, it, expect } from 'vitest'
import { exportEventAttendees } from '../eventExports'
import { createMockEvent } from '../testing/mockFactory'

describe('CSV Export Basic Functionality', () => {
  it('should export basic attendee data', () => {
    const mockEvent = createMockEvent({
      id: 'test-event',
      title: 'Test Event',
      attendeeCount: 1,
    })
    
    const result = exportEventAttendees(mockEvent)
    
    expect(result.filename.toLowerCase()).toContain('test-event')
    expect(result.content).toContain('Attendee 1')
    expect(result.content).toContain('<EMAIL>')
    expect(result.mimeType).toBe('text/csv')
  })
})