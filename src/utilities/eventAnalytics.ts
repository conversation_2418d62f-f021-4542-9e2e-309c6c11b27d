import { getPayload } from 'payload'
import payloadConfig from '@payload-config'
import { EventAnalyticsData } from '../types/eventAnalytics'

/**
 * Event Analytics Service - Core analytics engine for event performance metrics
 * Calculates key metrics, trends, and aggregations from event registration data
 */

export class EventAnalyticsService {
  /**
   * Get dashboard overview metrics for all events
   */
  static async getDashboardMetrics(
    dateRange?: { start: Date; end: Date },
    eventTypes?: string[]
  ): Promise<EventAnalyticsData['overview']> {
    const payload = await getPayload({ config: payloadConfig })

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const eventQuery: any = {
      status: { equals: 'completed' }
    }

    // Add date range filter if provided
    if (dateRange) {
      eventQuery.eventDate = {
        greater_than_equal: dateRange.start,
        less_than_equal: dateRange.end
      }
    }

    // Add event type filter if provided
    if (eventTypes && eventTypes.length > 0) {
      eventQuery.eventType = {
        in: eventTypes
      }
    }

    // Fetch completed events for accurate metrics
    const events = await payload.find({
      collection: 'events',
      where: eventQuery,
      limit: 1000,
      sort: '-eventDate'
    })

    // Calculate aggregated metrics
    const totalEvents = events.totalDocs
    const totalRegistrations = events.docs.reduce((sum, event) => {
      return sum + (event.attendees?.length || 0)
    }, 0)

    // Calculate capacity utilization
    const capacityEvents = events.docs.filter(event => event.capacity && event.capacity > 0)
    const totalCapacityUtilization = capacityEvents.reduce((sum, event) => {
      const attendeeCount = event.attendees?.length || 0
      const utilization = (attendeeCount / event.capacity!) * 100
      return sum + utilization
    }, 0)
    const averageCapacityUtilization = capacityEvents.length > 0
      ? totalCapacityUtilization / capacityEvents.length
      : 0

    // Find most popular event type
    const eventTypeCounts: Record<string, number> = {}
    events.docs.forEach(event => {
      const type = event.eventType || 'other'
      eventTypeCounts[type] = (eventTypeCounts[type] || 0) + (event.attendees?.length || 0)
    })

    const topEventType = Object.entries(eventTypeCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none'

    return {
      totalEvents,
      totalRegistrations,
      averageCapacityUtilization: Math.round(averageCapacityUtilization),
      topEventType
    }
  }

  /**
   * Get daily registration trends for analytics visualization
   */
  static async getDailyRegistrationTrends(
    dateRange: { start: Date; end: Date },
    eventTypes?: string[]
  ): Promise<EventAnalyticsData['trends']['dailyRegistrations']> {
    const payload = await getPayload({ config: payloadConfig })

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const eventQuery: any = {
      status: { equals: 'completed' }
    }

    // Date range filter
    eventQuery.eventDate = {
      greater_than_equal: dateRange.start,
      less_than_equal: dateRange.end
    }

    if (eventTypes && eventTypes.length > 0) {
      eventQuery.eventType = {
        in: eventTypes
      }
    }

    const events = await payload.find({
      collection: 'events',
      where: eventQuery,
      limit: 1000
    })

    // Aggregate registrations by date
    const dailyData: Record<string, number> = {}

    events.docs.forEach(event => {
      const eventDate = new Date(event.eventDate).toISOString().split('T')[0] // YYYY-MM-DD format

      if (!dailyData[eventDate]) {
        dailyData[eventDate] = 0
      }

      dailyData[eventDate] += event.attendees?.length || 0
    })

    // Convert to sorted array
    return Object.entries(dailyData)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }

  /**
   * Get event type breakdown with percentages
   */
  static async getEventTypeBreakdown(
    dateRange: { start: Date; end: Date }
  ): Promise<EventAnalyticsData['trends']['eventTypeBreakdown']> {
    const payload = await getPayload({ config: payloadConfig })

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const eventQuery: any = {
      status: { equals: 'completed' },
      eventDate: {
        greater_than_equal: dateRange.start,
        less_than_equal: dateRange.end
      }
    }

    const events = await payload.find({
      collection: 'events',
      where: eventQuery,
      limit: 1000
    })

    // Calculate total registrations for percentage calculation
    const totalRegistrations = events.docs.reduce((sum, event) => {
      return sum + (event.attendees?.length || 0)
    }, 0)

    if (totalRegistrations === 0) {
      return []
    }

    // Aggregate by event type
    const typeData: Record<string, number> = {}
    events.docs.forEach(event => {
      const type = event.eventType || 'other'
      typeData[type] = (typeData[type] || 0) + (event.attendees?.length || 0)
    })

    // Convert to array with percentages
    return Object.entries(typeData)
      .map(([type, count]) => ({
        type: type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' '),
        count,
        percentage: Math.round((count / totalRegistrations) * 100)
      }))
      .sort((a, b) => b.count - a.count)
  }

  /**
   * Get specific event analytics (for event detail view)
   */
  static async getEventAnalytics(eventId: string): Promise<{
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
     eventDetails: any,
    registrationStats: {
      total: number,
      registered: number,
      waitlisted: number,
      cancelled: number,
      attended: number
    },
    capacityUtilization: number,
    recentRegistrations: Array<{
      date: string,
      count: number
    }>
  }> {
    const payload = await getPayload({ config: payloadConfig })

    const event = await payload.findByID({
      collection: 'events',
      id: eventId
    })

    if (!event) {
      throw new Error('Event not found')
    }

    // Calculate registration status breakdown
    const statusCounts = {
      total: event.attendees?.length || 0,
      registered: 0,
      waitlisted: 0,
      cancelled: 0,
      attended: 0
    }

    event.attendees?.forEach((attendee: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
      const status = attendee.status || 'registered'
      statusCounts[status as keyof typeof statusCounts]++
    })

    // Calculate capacity utilization
    const capacityUtilization = event.capacity && event.capacity > 0
      ? Math.round((statusCounts.total / event.capacity) * 100)
      : 0

    // Get recent registration activity (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentActivity: Record<string, number> = {}
    event.attendees?.forEach((attendee: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
      if (attendee.registrationDate) {
        const regDate = new Date(attendee.registrationDate)
        if (regDate >= thirtyDaysAgo) {
          const dateKey = regDate.toISOString().split('T')[0]
          recentActivity[dateKey] = (recentActivity[dateKey] || 0) + 1
        }
      }
    })

    const recentRegistrations = Object.entries(recentActivity)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date))

    return {
      eventDetails: event,
      registrationStats: statusCounts,
      capacityUtilization,
      recentRegistrations
    }
  }
}

/**
 * Cache implementation for analytics data
 * Simple in-memory cache with TTL for development/production flexibility
 */
class AnalyticsCache {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private static cache: Map<string, { data: any, timestamp: number }> = new Map()
  private static readonly TTL = 5 * 60 * 1000 // 5 minutes

  static get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    const now = Date.now()
    if (now - entry.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  static set(key: string, data: any): void { // eslint-disable-line @typescript-eslint/no-explicit-any
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  static invalidate(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key)
      }
    }
  }

  static invalidateEventAnalytics(eventId?: string): void {
    if (eventId) {
      // Invalidate specific event analytics
      this.invalidate(`/${eventId}`)
      this.invalidate(`${eventId}`)
    } else {
      // Invalidate all analytics cache
      this.invalidate('analytics')
    }
  }

  static invalidateAll(): void {
    this.cache.clear()
  }
}

/**
 * Analytics hooks for automatic cache invalidation
 * Should be integrated into Payload CMS collection hooks
 */
export class AnalyticsHooks {
  /**
   * Hook to call after event creation/update
   */
  static afterChange = async ({ doc, operation }: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      // Invalidate all analytics cache when events change
      AnalyticsCache.invalidateAll()

      // Log the invalidation for debugging
      console.log(`Analytics cache invalidated after event ${operation}:`, doc?.title)
    } catch (error) {
      console.error('Failed to invalidate analytics cache:', error)
      // Don't throw - this shouldn't break the main operation
    }
  }

  /**
   * Hook to call after event deletion
   */
  static afterDelete = async ({ _doc, id }: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      // Invalidate all analytics cache when events are deleted
      AnalyticsCache.invalidateAll()

      // Log cache invalidation for debugging
      console.log(`Analytics cache invalidated after delete:`, id)
    } catch (error) {
      console.error('Failed to invalidate analytics cache after delete:', error)
    }
  }

  /**
   * Hook to call after registration data changes
   */
  static afterRegistration = (eventId: string) => {
    try {
      // Invalidate event-specific analytics and overview dashboard
      AnalyticsCache.invalidateEventAnalytics(eventId)
      console.log(`Analytics cache invalidated after registration change for event: ${eventId}`)
    } catch (error) {
      console.error('Failed to invalidate analytics cache after registration:', error)
    }
  }
}

// Export cache for use in API endpoints
export { AnalyticsCache }
