import sanitizeHtml, { IOptions } from 'sanitize-html'

// Properly typed sanitization configuration
const sanitizeOptions: IOptions = {
  allowedTags: [
    'div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'strong', 'em', 'ul', 'ol', 'li', 'a', 'br', 'hr',
    'table', 'thead', 'tbody', 'tr', 'th', 'td', 'img'
  ],
  allowedAttributes: {
    '*': ['style', 'class'],
    'a': ['href', 'name', 'target'],
    'img': ['src', 'alt', 'width', 'height'],
    'table': ['width', 'cellpadding', 'cellspacing', 'border']
  },
  allowedStyles: {
    '*': {
      'color': [/^#[0-9a-fA-F]{3,6}$/, /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/],
      'font-size': [/^\d+(?:px|em|rem|%)$/],
      'font-weight': [/^(?:normal|bold|bolder|lighter|\d{3})$/],
      'text-align': [/^(?:left|right|center|justify)$/],
      'margin': [/^\d+(?:px|em|rem|%)(?:\s+\d+(?:px|em|rem|%)){0,3}$/],
      'padding': [/^\d+(?:px|em|rem|%)(?:\s+\d+(?:px|em|rem|%)){0,3}$/],
      'background-color': [/^#[0-9a-fA-F]{3,6}$/, /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/]
    }
  },
  allowedSchemes: ['http', 'https', 'mailto'],
  allowedSchemesByTag: {
    img: ['http', 'https', 'data']
  }
}

// Type-safe sanitization function
export const sanitizeEmailContent = (html: string): string => {
  return sanitizeHtml(html, sanitizeOptions)
}
