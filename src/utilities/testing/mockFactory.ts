// Copy this complete content into mockFactory.ts
import { Event } from '@/payload-types' // Assuming Event type is available here

interface MockEventOptions {
  id?: string;
  title?: string;
  slug?: string;
  eventType?: Event['eventType']; // Use Event type for eventType
  status?: Event['status']; // Use Event type for status
  attendeeCount?: number; // Make optional, default to 0
  customScenarios?: 'basic' | 'edge-cases' | 'performance' | 'empty';
  // Add options for other required fields if needed for specific tests
  eventDate?: string;
  description?: string;
  location?: string;
  capacity?: number;
  registrationRequired?: boolean;
  organizerEmail?: string;
}

export const createMockEvent = (options: MockEventOptions = {}): Event => { // Explicitly type return as Event
  const {
    attendeeCount = 0, // Default to 0
    customScenarios,
    id,
    title,
    slug,
    eventType,
    status,
    eventDate,
    description,
    location,
    capacity,
    registrationRequired,
    organizerEmail,
  } = options;

  const mockEvent: Event = {
    id: id || 'mock-event-' + Math.random().toString(36).substring(7),
    title: title || 'Mock Rotary Event',
    slug: slug || 'mock-rotary-event-' + Math.random().toString(36).substring(7),
    eventType: eventType || 'meeting',
    status: status || 'published',
    eventDate: eventDate || new Date().toISOString(), // Required field
    description: description || 'A mock event for testing purposes.', // Required field
    location: location || 'Virtual', // Required field
    capacity: capacity, // Optional
    registrationRequired: registrationRequired || false, // Required field
    registrationForm: { // Required field if registrationRequired is true, provide a default
      formFields: [],
      maxRegistrations: undefined,
      registrationDeadline: undefined,
      allowWaitlist: false,
      confirmationMessage: 'Thank you for registering!',
    },
    organizer: { // Required field
      name: 'Mock Organizer',
      email: organizerEmail || '<EMAIL>',
      phone: '************',
      sendNotifications: true,
    },
    attendees: [], // Initialize as empty array
    createdAt: new Date().toISOString(), // Required by Payload
    updatedAt: new Date().toISOString(), // Required by Payload
  };

  if (attendeeCount > 0) {
    mockEvent.attendees = Array.from({ length: attendeeCount }, (_, i) => ({
      userId: `user-${i + 1}`,
      userEmail: `attendee${i + 1}@example.com`,
      userName: `Attendee ${i + 1}`,
      registrationDate: new Date().toISOString(), // Ensure date is ISO string
      status: 'registered',
      customFields: {}, // Use an empty object for custom fields
      uploadedFiles: [],
    }));
  }

  // Handle custom scenarios if needed
  if (customScenarios === 'empty') {
    mockEvent.attendees = [];
    mockEvent.title = 'Empty Event';
  }
  // Add other custom scenario logic here

  return mockEvent;
};