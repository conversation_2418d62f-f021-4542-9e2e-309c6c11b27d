import { vi, describe, it, expect, beforeEach } from 'vitest'
import { POST } from './route'
import { NextRequest } from 'next/server'
import { getPayload } from 'payload'

// Mock Next.js cookies with configurable token
const mockCookiesGet = vi.fn()
vi.mock('next/headers', () => ({
  cookies: vi.fn(async () => Promise.resolve({
    get: mockCookiesGet
  }))
}))
// Mock Payload configuration
vi.mock('@payload-config', () => ({
  default: {}
}))

// Mock NextRequest
const createMockRequest = (body: any, headers: Record<string, string> = {}) => {
  return {
    json: vi.fn().mockResolvedValue(body),
    headers: {
      get: (key: string) => headers[key] || null
    }
  } as unknown as NextRequest
}

// Mock Payload instance with proper auth structure
const mockPayload = {
  auth: vi.fn(),
  findByID: vi.fn(),
  update: vi.fn()
}

// Mock auth results with proper user structure
const createMockAuthResult = (userId: string) => ({
  user: {
    id: userId,
    name: 'Mock User',
    email: '<EMAIL>',
    rotaryId: 'TEST001'
  }
})

// Helper function to setup authenticated scenario
const setupAuthenticatedScenario = () => {
  mockCookiesGet.mockReturnValue({ value: 'mock-session-id' })
}

// Helper function to setup unauthenticated scenario
const setupUnauthenticatedScenario = () => {
  mockCookiesGet.mockReturnValue(null)
}

const mockAuthResult = createMockAuthResult('mock-user-id')

// Mock getPayload correctly
vi.mock('payload', () => ({
  getPayload: vi.fn()
}))

describe('Event Registration API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Re-setup mocked cookies each time
    mockCookiesGet.mockReturnValue({ value: 'mock-session-id' }) // Default authenticated
    // @ts-ignore
    getPayload.mockResolvedValue(mockPayload)
  })

  it('returns 401 for unauthenticated requests', async () => {
    // Setup unauthenticated state
    setupUnauthenticatedScenario()
    mockPayload.auth.mockResolvedValue({ user: null })

    const request = createMockRequest({
      eventId: 'event-1',
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        classification: 'Engineer',
        consentDataProcessing: true
      }
    }, {
      'x-forwarded-for': '127.0.0.1'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(401)
    expect(data.error).toBe('Authentication required')
  })

  it('returns 400 for invalid registration data', async () => {
    setupAuthenticatedScenario()
    mockPayload.auth.mockResolvedValue(mockAuthResult)

    const request = createMockRequest({
      // Missing eventId
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>'
      }
    }, {
      'x-forwarded-for': '127.0.0.2'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.message).toBeDefined()
  })

  it('returns 404 for non-existent events', async () => {
    setupAuthenticatedScenario()
    mockPayload.auth.mockResolvedValue(createMockAuthResult('user-not-found'))
    mockPayload.findByID.mockResolvedValue(null)

    const request = createMockRequest({
      eventId: 'non-existent-event',
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        classification: 'Engineer',
        consentDataProcessing: true
      }
    }, {
      'x-forwarded-for': '127.0.0.3'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(404)
    expect(data.error).toBe('Event not found')
  })

  it('returns 400 for events that do not require registration', async () => {
    setupAuthenticatedScenario()
    mockPayload.auth.mockResolvedValue(createMockAuthResult('user-no-reg'))
    mockPayload.findByID.mockResolvedValue({
      id: 'event-1',
      registrationRequired: false,
      status: 'published'
    })

    const request = createMockRequest({
      eventId: 'event-1',
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        classification: 'Engineer',
        consentDataProcessing: true
      }
    }, {
      'x-forwarded-for': '127.0.0.4'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Registration is not required for this event')
  })

  it('returns 400 for duplicate registrations', async () => {
    setupAuthenticatedScenario()
    mockPayload.auth.mockResolvedValue(createMockAuthResult('user-duplicate'))
    mockPayload.findByID.mockResolvedValue({
      id: 'event-1',
      registrationRequired: true,
      status: 'published',
      attendees: [
        {
          userEmail: '<EMAIL>'
        }
      ]
    })

    const request = createMockRequest({
      eventId: 'event-1',
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        classification: 'Engineer',
        consentDataProcessing: true
      }
    }, {
      'x-forwarded-for': '127.0.0.5'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('You are already registered for this event')
  })

  it('returns 400 for events at capacity', async () => {
    setupAuthenticatedScenario()
    mockPayload.auth.mockResolvedValue(createMockAuthResult('user-capacity'))
    mockPayload.findByID
      .mockResolvedValueOnce({
        id: 'event-1',
        registrationRequired: true,
        status: 'published',
        capacity: 10,
        attendees: Array(10).fill({}).map((_, i) => ({ id: `attendee-${i}` }))
      })
      .mockResolvedValueOnce({
        id: 'event-1',
        registrationRequired: true,
        status: 'published',
        capacity: 10,
        attendees: Array(10).fill({}).map((_, i) => ({ id: `attendee-${i}` }))
      })

    const request = createMockRequest({
      eventId: 'event-1',
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        classification: 'Engineer',
        consentDataProcessing: true
      }
    }, {
      'x-forwarded-for': '127.0.0.6'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Event is at capacity. Another registration was just processed.')
  })

  it('successfully registers a new attendee', async () => {
    setupAuthenticatedScenario()
    mockPayload.auth.mockResolvedValue(createMockAuthResult('user-success'))
    mockPayload.findByID
      .mockResolvedValueOnce({
        id: 'event-1',
        registrationRequired: true,
        status: 'published',
        capacity: 50,
        attendees: []
      })
      .mockResolvedValueOnce({
        id: 'event-1',
        registrationRequired: true,
        status: 'published',
        capacity: 50,
        attendees: []
      })
    mockPayload.update.mockResolvedValue({
      id: 'event-1',
      attendees: [
        {
          userId: 'user-success',
          userName: 'John Doe',
          userEmail: '<EMAIL>',
          registrationDate: new Date().toISOString(),
          status: 'registered',
          attendeeInfo: {
            name: 'John Doe',
            phone: '+1234567890',
            classification: 'Engineer',
            consents: {
              dataProcessing: true,
              marketing: false
            }
          }
        }
      ]
    })

    const request = createMockRequest({
      eventId: 'event-1',
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        classification: 'Engineer',
        consentDataProcessing: true
      }
    }, {
      'x-forwarded-for': '127.0.0.7'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.message).toBe('Registration successful')
    expect(mockPayload.update).toHaveBeenCalled()
  })

  it('returns 400 for past events', async () => {
    setupAuthenticatedScenario()
    mockPayload.auth.mockResolvedValue(createMockAuthResult('user-past'))
    mockPayload.findByID.mockResolvedValue({
      id: 'event-1',
      registrationRequired: true,
      status: 'published',
      eventDate: new Date(Date.now() - 86400000).toISOString() // Yesterday
    })

    const request = createMockRequest({
      eventId: 'event-1',
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        classification: 'Engineer',
        consentDataProcessing: true
      }
    }, {
      'x-forwarded-for': '127.0.0.8'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Cannot register for past events')
  })
})