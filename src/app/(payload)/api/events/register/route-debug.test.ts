import { vi, describe, it, expect } from 'vitest'
import { POST } from './route'
import { NextRequest } from 'next/server'

// Mock Next.js cookies
vi.mock('next/headers', () => ({
  cookies: vi.fn(async () => Promise.resolve({
    get: vi.fn().mockReturnValue({ value: 'mock-session-id' })
  }))
}))

// Mock Payload configuration
vi.mock('@payload-config', () => ({
  default: {}
}))

// Mock Payload instance with proper auth structure
const mockPayload = {
  auth: vi.fn(),
  findByID: vi.fn(),
  update: vi.fn()
}

// Mock getPayload
vi.mock('payload', () => ({
  getPayload: vi.fn().mockImplementation(async () => Promise.resolve(mockPayload))
}))

// Create mock request
const createMockRequest = (body: any, headers: Record<string, string> = {}) => {
  return {
    json: vi.fn().mockResolvedValue(body),
    headers: {
      get: (key: string) => headers[key] || null
    }
  } as unknown as NextRequest
}

describe('Event Registration API - DEBUG', () => {
  it('Minimal authentication test', async () => {
    // Setup mock auth to resolve successfully
    const mockAuthResult = {
      user: {
        id: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
        rotaryId: 'TEST001'
      }
    }

    mockPayload.auth.mockResolvedValue(mockAuthResult)
    mockPayload.findByID.mockResolvedValue(null) // Cause a 404

    const request = createMockRequest({
      eventId: 'non-existent-event',
      attendee: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        classification: 'Engineer',
        consentDataProcessing: true
      }
    })

    const response = await POST(request)
    const data = await response.json()

    console.log('Response status:', response.status)
    console.log('Response data:', data)

    // Just check that we get some response (authentication should pass)
    expect(response.status).toBeDefined()
    expect(mockPayload.auth).toHaveBeenCalled()
  })
})