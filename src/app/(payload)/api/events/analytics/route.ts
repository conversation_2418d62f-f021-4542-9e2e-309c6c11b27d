import { NextRequest } from 'next/server'
import { EventAnalyticsService, AnalyticsCache } from '@/utilities/eventAnalytics'

/**
 * Event Analytics API Endpoint
 * Provides analytics data for event performance metrics
 * Admin-only access controlled by Payload CMS routing
 */

// Cache key prefix for analytics
const CACHE_PREFIX = 'analytics'

export async function GET(request: NextRequest) {
  try {

    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const startDateParam = searchParams.get('startDate')
    const endDateParam = searchParams.get('endDate')
    const eventTypesParam = searchParams.get('eventTypes')
    const metricParam = searchParams.get('metric') || 'overview'

    // Parse date range
    let dateRange: { start: Date; end: Date } | undefined
    if (startDateParam && endDateParam) {
      dateRange = {
        start: new Date(startDateParam),
        end: new Date(endDateParam)
      }
    } else {
      // Default to last 30 days
      const end = new Date()
      const start = new Date()
      start.setDate(end.getDate() - 30)
      dateRange = { start, end }
    }

    // Parse event types filter
    let eventTypes: string[] | undefined
    if (eventTypesParam) {
      eventTypes = eventTypesParam.split(',').map(type => type.trim())
    }

    // Create cache key based on parameters
    const cacheKey = `${CACHE_PREFIX}:${metricParam}:${dateRange.start.toISOString()}:${dateRange.end.toISOString()}:${eventTypes?.join(',') || 'all'}`

    // Check cache first
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const cachedResult = AnalyticsCache.get<any>(cacheKey)
    if (cachedResult) {
      return new Response(JSON.stringify(cachedResult), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=300' // 5 minutes
        }
      })
    }

    // Generate analytics data based on requested metric
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let result: any

    switch (metricParam) {
      case 'overview':
        result = await EventAnalyticsService.getDashboardMetrics(dateRange, eventTypes)
        break

      case 'trends':
        result = {
          dailyRegistrations: await EventAnalyticsService.getDailyRegistrationTrends(dateRange, eventTypes),
          eventTypeBreakdown: await EventAnalyticsService.getEventTypeBreakdown(dateRange)
        }
        break

      case 'daily':
        result = {
          dailyRegistrations: await EventAnalyticsService.getDailyRegistrationTrends(dateRange, eventTypes)
        }
        break

      case 'types':
        result = {
          eventTypeBreakdown: await EventAnalyticsService.getEventTypeBreakdown(dateRange)
        }
        break

      default:
        result = await EventAnalyticsService.getDashboardMetrics(dateRange, eventTypes)
    }

    // Cache the result
    AnalyticsCache.set(cacheKey, result)

    return new Response(JSON.stringify(result), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300' // 5 minutes
      }
    })

  } catch (error) {
    console.error('Analytics API Error:', error)

    return new Response(JSON.stringify({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

/**
 * POST endpoint for specific event analytics
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { eventId } = body

    if (!eventId) {
      return new Response(JSON.stringify({ error: 'Event ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Get specific event analytics
    const analytics = await EventAnalyticsService.getEventAnalytics(eventId)

    return new Response(JSON.stringify(analytics), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Event Analytics API Error:', error)

    let statusCode = 500
    let message = 'Internal server error'

    if (error instanceof Error) {
      if (error.message === 'Event not found') {
        statusCode = 404
        message = error.message
      } else {
        message = error.message
      }
    }

    return new Response(JSON.stringify({ error: message }), {
      status: statusCode,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
