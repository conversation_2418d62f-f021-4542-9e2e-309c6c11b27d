import { getPayload } from 'payload'
import payloadConfig from '@payload-config'
import { getAuthenticatedUser } from '@/utilities/getAuthenticatedUser'

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100

// In-memory store for rate limiting (replace with Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(clientId: string): boolean {
  const now = Date.now()
  const client = rateLimitStore.get(clientId)

  if (!client || now > client.resetTime) {
    rateLimitStore.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (client.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  client.count++
  rateLimitStore.set(clientId, client)
  return true
}

function getClientId(request: Request): string {
  // Use a combination of IP and user agent for identification
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwardedFor || realIp || 'unknown'
  const userAgent = request.headers.get('user-agent') || ''
  return `${ip}:${userAgent.slice(0, 100)}` // Limit user agent length
}

// Input sanitization function to prevent XSS
function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return ''
  // Remove HTML tags and potentially dangerous characters
  return input.replace(/[<>'\"&]/g, '').trim()
}

// Request validation
interface ProfileUpdateRequest {
  name?: {
    en?: string
    fr?: string
    ar?: string
  }
  phonePersonal?: string
  phoneWork?: string
  classification?: {
    en?: string
    fr?: string
    ar?: string
  }
}

function validateProfileUpdate(body: ProfileUpdateRequest): boolean {
  // Validate name fields
  if (body.name) {
    if (body.name.en && body.name.en.length > 100) return false
    if (body.name.fr && body.name.fr.length > 100) return false
    if (body.name.ar && body.name.ar.length > 100) return false
  }

  // Validate phone numbers
  if (body.phonePersonal && body.phonePersonal.length > 20) return false
  if (body.phoneWork && body.phoneWork.length > 20) return false

  // Validate classification
  if (body.classification) {
    if (body.classification.en && body.classification.en.length > 100) return false
    if (body.classification.fr && body.classification.fr.length > 100) return false
    if (body.classification.ar && body.classification.ar.length > 100) return false
  }

  return true
}

export async function PUT(request: Request) {
  try {
    // Initialize Payload CMS
    const payload = await getPayload({ config: payloadConfig })

    // Rate limiting check
    const clientId = getClientId(request)
    if (!checkRateLimit(clientId)) {
      return new Response(
        JSON.stringify({
          error: 'Too many requests, please try again later.',
          code: 'RATE_LIMIT_EXCEEDED'
        }),
        {
          status: 429,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Get authenticated user
    const user = await getAuthenticatedUser(request, payload)

    if (!user) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized access. Please log in.',
          code: 'UNAUTHORIZED'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body
    const body: ProfileUpdateRequest = await request.json()

    // Validate request
    if (!validateProfileUpdate(body)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid request data.',
          code: 'INVALID_DATA'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Sanitize input data
    const sanitizedData: ProfileUpdateRequest = {}
    
    if (body.name) {
      sanitizedData.name = {}
      if (body.name.en) sanitizedData.name.en = sanitizeInput(body.name.en)
      if (body.name.fr) sanitizedData.name.fr = sanitizeInput(body.name.fr)
      if (body.name.ar) sanitizedData.name.ar = sanitizeInput(body.name.ar)
    }
    
    if (body.phonePersonal) sanitizedData.phonePersonal = sanitizeInput(body.phonePersonal)
    if (body.phoneWork) sanitizedData.phoneWork = sanitizeInput(body.phoneWork)
    
    if (body.classification) {
      sanitizedData.classification = {}
      if (body.classification.en) sanitizedData.classification.en = sanitizeInput(body.classification.en)
      if (body.classification.fr) sanitizedData.classification.fr = sanitizeInput(body.classification.fr)
      if (body.classification.ar) sanitizedData.classification.ar = sanitizeInput(body.classification.ar)
    }

    // Update user profile
    const updatedUser = await payload.update({
      collection: 'users',
      id: user.id,
      data: sanitizedData,
      depth: 2
    })

    // Remove sensitive fields
    const { password: _, salt: __, ...safeProfile } = updatedUser

    // Return updated profile
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Profile updated successfully',
        data: {
          user: safeProfile
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Rate-Limit-Remaining': (RATE_LIMIT_MAX_REQUESTS - (rateLimitStore.get(clientId)?.count || 0)).toString(),
        }
      }
    )

  } catch (error) {
    console.error('Profile update error:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error occurred while updating profile.',
        code: 'INTERNAL_ERROR'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}