import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { PUT } from './route'
import { getPayload } from 'payload'

// Use vi.hoisted to ensure the mock is available when vi.mock is hoisted
const { mockGetAuthenticatedUser } = vi.hoisted(() => {
  return { mockGetAuthenticatedUser: vi.fn() }
})

// Mock the getAuthenticatedUser utility
vi.mock('@/utilities/getAuthenticatedUser', () => ({
  getAuthenticatedUser: mockGetAuthenticatedUser,
}))

// Mock the payload module
vi.mock('payload')

describe('PUT /api/users/profile/update', () => {
  const mockPayload = {
    update: vi.fn(),
  }

  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
  }

  beforeEach(() => {
    vi.resetAllMocks()
    vi.mocked(getPayload).mockResolvedValue(mockPayload as any)
  })

  it('should return 401 Unauthorized if user is not authenticated', async () => {
    // Arrange
    mockGetAuthenticatedUser.mockResolvedValue(null)
    const request = new Request('http://localhost/api/users/profile/update', {
      method: 'PUT',
      body: JSON.stringify({}),
    })

    // Act
    const response = await PUT(request)
    const body = await response.json()

    // Assert
    expect(response.status).toBe(401)
    expect(body.error).toBe('Unauthorized access. Please log in.')
  })

  it('should return 400 Bad Request for invalid data', async () => {
    // Arrange
    mockGetAuthenticatedUser.mockResolvedValue(mockUser)
    const invalidData = { name: { en: 'a'.repeat(101) } } // Name too long
    const request = new Request('http://localhost/api/users/profile/update', {
      method: 'PUT',
      body: JSON.stringify(invalidData),
    })

    // Act
    const response = await PUT(request)
    const body = await response.json()

    // Assert
    expect(response.status).toBe(400)
    expect(body.error).toBe('Invalid request data.')
  })

  it('should return 200 OK and updated user on successful update', async () => {
    // Arrange
    mockGetAuthenticatedUser.mockResolvedValue(mockUser)
    const validData = { name: { en: 'New Name' } }
    const updatedUser = { ...mockUser, name: { en: 'New Name' } }
    mockPayload.update.mockResolvedValue(updatedUser)

    const request = new Request('http://localhost/api/users/profile/update', {
      method: 'PUT',
      body: JSON.stringify(validData),
    })

    // Act
    const response = await PUT(request)
    const body = await response.json()

    // Assert
    expect(response.status).toBe(200)
    expect(body.success).toBe(true)
    expect(body.data.user.name.en).toBe('New Name')
    expect(mockPayload.update).toHaveBeenCalledWith({
      collection: 'users',
      id: mockUser.id,
      data: validData,
      depth: 2,
    })
  })

  it('should sanitize input before updating', async () => {
    // Arrange
    mockGetAuthenticatedUser.mockResolvedValue(mockUser)
    const maliciousData = { name: { en: '<script>alert("xss")</script>New Name' } }
    const sanitizedData = { name: { en: 'scriptalert(xss)/scriptNew Name' } }
    const updatedUser = { ...mockUser, ...sanitizedData }
    mockPayload.update.mockResolvedValue(updatedUser)

    const request = new Request('http://localhost/api/users/profile/update', {
      method: 'PUT',
      body: JSON.stringify(maliciousData),
    })

    // Act
    await PUT(request)

    // Assert
    expect(mockPayload.update).toHaveBeenCalledWith({
      collection: 'users',
      id: mockUser.id,
      data: sanitizedData,
      depth: 2,
    })
  })

  describe('Rate Limiting', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('should return 429 Too Many Requests when rate limit is exceeded', async () => {
      // Arrange
      mockGetAuthenticatedUser.mockResolvedValue(mockUser)
      const RATE_LIMIT_MAX_REQUESTS = 100 // As defined in the route

      // Act: Simulate exceeding the rate limit
      for (let i = 0; i < RATE_LIMIT_MAX_REQUESTS; i++) {
        const request = new Request('http://localhost/api/users/profile/update', {
          method: 'PUT',
          body: JSON.stringify({ name: { en: 'Test' } }),
        })
        await PUT(request)
      }

      // This request should be blocked due to rate limiting
      const finalRequest = new Request('http://localhost/api/users/profile/update', {
        method: 'PUT',
        body: JSON.stringify({ name: { en: 'Test' } }),
      })
      const response = await PUT(finalRequest)
      const body = await response.json()

      // Assert
      expect(response.status).toBe(429)
      expect(body.error).toBe('Too many requests, please try again later.')
    })

    it('should allow requests again after the rate limit window resets', async () => {
      // Arrange
      mockGetAuthenticatedUser.mockResolvedValue(mockUser)
      const RATE_LIMIT_MAX_REQUESTS = 100
      const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes

      // Create request function to generate fresh request objects
      const createRequest = () => new Request('http://localhost/api/users/profile/update', {
        method: 'PUT',
        body: JSON.stringify({ name: { en: 'Test' } }),
      })

      // Act: Exceed the rate limit
      for (let i = 0; i < RATE_LIMIT_MAX_REQUESTS; i++) {
        await PUT(createRequest())
      }
      const blockedResponse = await PUT(createRequest())
      expect(blockedResponse.status).toBe(429) // Confirm it's blocked

      // Advance time past the window
      vi.advanceTimersByTime(RATE_LIMIT_WINDOW + 1000)

      // This request should now be allowed
      mockPayload.update.mockResolvedValue({ ...mockUser, name: { en: 'Test' } })
      const successResponse = await PUT(createRequest())

      // Assert
      expect(successResponse.status).toBe(200)
    })
  })
})
