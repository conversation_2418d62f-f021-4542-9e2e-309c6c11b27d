import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { GET } from './route' // Assuming GET is the exported function from your route file
import { getAuthenticatedUser } from '../../../../../utilities/getAuthenticatedUser'
import { getPayload } from 'payload'

// Mock dependencies
vi.mock('../../../../../utilities/getAuthenticatedUser')
vi.mock('payload')

describe('GET /api/users/profile', () => {
  const mockPayload = {
    findByID: vi.fn(),
  }

  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedpassword',
    salt: 'randomsalt',
  }

  beforeEach(() => {
    vi.resetModules() // Reset modules to isolate state between tests
    vi.resetAllMocks()
    // Mock getPayload to return our mock payload instance
    vi.mocked(getPayload).mockResolvedValue(mockPayload as any)
  })

  it('should return 401 Unauthorized if user is not authenticated', async () => {
    // Arrange
    vi.mocked(getAuthenticatedUser).mockResolvedValue(null)
    const request = new Request('http://localhost/api/users/profile')

    // Act
    const response = await GET(request)
    const body = await response.json()

    // Assert
    expect(response.status).toBe(401)
    expect(body.error).toBe('Unauthorized access. Please log in.')
    expect(body.code).toBe('UNAUTHORIZED')
  })

  it('should return 200 OK and user profile for an authenticated user', async () => {
    // Arrange
    vi.mocked(getAuthenticatedUser).mockResolvedValue(mockUser)
    mockPayload.findByID.mockResolvedValue(mockUser)
    const request = new Request('http://localhost/api/users/profile')

    // Act
    const response = await GET(request)
    const body = await response.json()

    // Assert
    expect(response.status).toBe(200)
    expect(body.success).toBe(true)
    expect(body.data.user.id).toBe(mockUser.id)
    expect(body.data.user.email).toBe(mockUser.email)
    expect(mockPayload.findByID).toHaveBeenCalledWith({
      collection: 'users',
      id: mockUser.id,
      depth: 2,
    })
  })

  it('should not include password or salt in the response', async () => {
    // Arrange
    vi.mocked(getAuthenticatedUser).mockResolvedValue(mockUser)
    mockPayload.findByID.mockResolvedValue(mockUser)
    const request = new Request('http://localhost/api/users/profile')

    // Act
    const response = await GET(request)
    const body = await response.json()

    // Assert
    expect(response.status).toBe(200)
    expect(body.data.user).not.toHaveProperty('password')
    expect(body.data.user).not.toHaveProperty('salt')
  })

  it('should return 500 if there is a server error', async () => {
    // Arrange
    const testError = new Error('Database connection failed')
    vi.mocked(getAuthenticatedUser).mockRejectedValue(testError)
    const request = new Request('http://localhost/api/users/profile')

    // Act
    const response = await GET(request)
    const body = await response.json()

    // Assert
    expect(response.status).toBe(500)
    expect(body.error).toBe('Internal server error occurred while fetching profile.')
    expect(body.code).toBe('INTERNAL_ERROR')
  })

  // New describe block for rate limiting tests
  describe('Rate Limiting', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('should return 429 Too Many Requests when rate limit is exceeded', async () => {
      const localMockUser = { ...mockUser, id: 'rate-limit-user-1' };
      vi.mocked(getAuthenticatedUser).mockResolvedValue(localMockUser)
      mockPayload.findByID.mockResolvedValue(localMockUser)
      const request = new Request('http://localhost/api/users/profile')
      const RATE_LIMIT_MAX_REQUESTS = 100

      for (let i = 0; i < RATE_LIMIT_MAX_REQUESTS; i++) {
        await GET(request)
      }
      const response = await GET(request)
      const body = await response.json()

      expect(response.status).toBe(429)
      expect(body.error).toBe('Too many requests, please try again later.')
      expect(body.code).toBe('RATE_LIMIT_EXCEEDED')
      expect(response.headers.get('Retry-After')).toBeDefined()
    })

    it('should allow requests again after the rate limit window resets', async () => {
      const localMockUser = { ...mockUser, id: 'rate-limit-user-2' };
      vi.mocked(getAuthenticatedUser).mockResolvedValue(localMockUser)
      mockPayload.findByID.mockResolvedValue(localMockUser)
      const request = new Request('http://localhost/api/users/profile')
      const RATE_LIMIT_MAX_REQUESTS = 100
      const RATE_LIMIT_WINDOW = 15 * 60 * 1000

      for (let i = 0; i < RATE_LIMIT_MAX_REQUESTS; i++) {
        await GET(request)
      }
      const blockedResponse = await GET(request)
      expect(blockedResponse.status).toBe(429)

      vi.advanceTimersByTime(RATE_LIMIT_WINDOW + 1000)

      const successResponse = await GET(request)
      expect(successResponse.status).toBe(200)
    })

    it('should include X-Rate-Limit-Remaining header on successful requests', async () => {
      const localMockUser = { ...mockUser, id: 'rate-limit-user-3' };
      vi.mocked(getAuthenticatedUser).mockResolvedValue(localMockUser)
      mockPayload.findByID.mockResolvedValue(localMockUser)
      const request = new Request('http://localhost/api/users/profile')
      const RATE_LIMIT_MAX_REQUESTS = 100

      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(response.headers.get('X-Rate-Limit-Remaining')).toBe((RATE_LIMIT_MAX_REQUESTS - 1).toString())
    })
  })
})