import { getPayload } from 'payload'
import payloadConfig from '@payload-config'
import { getAuthenticatedUser } from '../../../../../utilities/getAuthenticatedUser'
import crypto from 'crypto';

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100

// In-memory store for rate limiting (replace with Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()
const CLEANUP_INTERVAL_MS = 60 * 60 * 1000; // Clean up every hour

function cleanupRateLimitMap() {
  const now = Date.now();
  for (const [clientId, client] of rateLimitStore.entries()) {
    if (now > client.resetTime) {
      rateLimitStore.delete(clientId);
    }
  }
  console.log(`[CLEANUP] User profile rate limit map cleaned. Current size: ${rateLimitStore.size}`);
}

// Start cleanup interval on server startup (or when this module is first loaded)
setInterval(cleanupRateLimitMap, CLEANUP_INTERVAL_MS);

function checkRateLimit(clientId: string): { allowed: boolean; retryAfter?: number } {
  const now = Date.now()
  const client = rateLimitStore.get(clientId)

  if (!client || now > client.resetTime) {
    rateLimitStore.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return { allowed: true }
  }

  if (client.count >= RATE_LIMIT_MAX_REQUESTS) {
    return { allowed: false, retryAfter: Math.ceil((client.resetTime - now) / 1000) } // seconds
  }

  client.count++
  rateLimitStore.set(clientId, client)
  return { allowed: true }
}

function getClientId(request: Request, userId?: string): string {
  if (userId) {
    return `user_${userId}`;
  }

  // Fallback for unauthenticated requests: hash IP and user agent
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const ip = forwardedFor || realIp || 'unknown';
  const userAgent = request.headers.get('user-agent') || '';
  const identifier = `${ip}:${userAgent.slice(0, 100)}`; // Limit user agent length

  // Hash the identifier for better security and privacy
  return `anon_${crypto.createHash('sha256').update(identifier).digest('hex')}`;
}

export async function GET(request: Request) {
  try {
    // Initialize Payload CMS
    const payload = await getPayload({ config: payloadConfig })

    // Get authenticated user first
    const user = await getAuthenticatedUser(request, payload)

    // Rate limiting check - use user ID if authenticated, otherwise generate anonymous ID
    const clientId = getClientId(request, user?.id)
    const rateLimitStatus = checkRateLimit(clientId)
    if (!rateLimitStatus.allowed) {
      return new Response(
        JSON.stringify({
          error: 'Too many requests, please try again later.',
          code: 'RATE_LIMIT_EXCEEDED'
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': rateLimitStatus.retryAfter?.toString() || '60' // Default to 60 seconds
          }
        }
      )
    }

    if (!user) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized access. Please log in.',
          code: 'UNAUTHORIZED'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    if (!user) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized access. Please log in.',
          code: 'UNAUTHORIZED'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Fetch full user profile (excluding sensitive fields in the future)
    const userProfile = await payload.findByID({
      collection: 'users',
      id: user.id,
      depth: 2 // Include nested field data
    })

    // Remove sensitive fields
    const { password: _, salt: __, ...safeProfile } = userProfile

    // Return user profile
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          user: safeProfile
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'private, no-cache', // No caching for user data
          'X-Rate-Limit-Remaining': (RATE_LIMIT_MAX_REQUESTS - (rateLimitStore.get(clientId)?.count || 0)).toString(),
        }
      }
    )

  } catch (error) {
    console.error('Profile fetch error:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error occurred while fetching profile.',
        code: 'INTERNAL_ERROR'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}