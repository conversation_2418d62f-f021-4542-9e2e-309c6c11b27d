import type { <PERSON>ada<PERSON> } from 'next'

import payloadConfig from '@payload-config'
import { getPayload } from 'payload'
import { draftMode } from 'next/headers'
import React, { cache } from 'react'
import Link from 'next/link'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import { EventHero } from '@/heros/EventHero'
import { EventRegistrationForm } from '@/components/EventRegistrationForm'

import { generateMeta } from '@/utilities/generateMeta'

export async function generateStaticParams() {
  const payload = await getPayload({ config: payloadConfig })
  const events = await payload.find({
    collection: 'events',
    draft: false,
    limit: 1000,
    overrideAccess: false,
    pagination: false,
    select: {
      slug: true,
    },
  })

  const params = events.docs.map(({ slug }) => ({
    slug,
  }))

  return params
}

type Args = {
  params: Promise<{
    slug?: string
  }>
}

export default async function EventPage({ params: paramsPromise }: Args) {
  const { isEnabled: draft } = await draftMode()
  const { slug = '' } = await paramsPromise
  const url = '/events/' + slug

  const event = await queryEventBySlug({ slug, draft })

  if (!event) {
    return <PayloadRedirects url={url} />
  }

  // Handle draft mode for unpublished events
  if (event.status !== 'published' && !draft) {
    return <PayloadRedirects url={url} />
  }

  const description = event.description || ''
  const capacity = event.capacity
  const attendees = event.attendees || []
  const isRegistrationRequired = event.registrationRequired

  return (
    <article className="pt-16 pb-16">
      {/* Event Details Redirect Handling */}
      <PayloadRedirects disableNotFound url={url} />

      {/* Event Hero Section */}
      <EventHero event={event} />

      {/* Event Registration Section */}
      {isRegistrationRequired && (
        <section id="registration" className="mt-16">
          <div className="container max-w-3xl">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-4">Register for This Event</h2>
              <p className="text-muted-foreground">
                {capacity ? (
                  <>
                    <span className="font-medium">
                      {attendees.length} of {capacity} spots filled
                    </span>
                    {attendees.length >= capacity && (
                      <span className="text-red-600 font-medium">
                        {event.registrationForm?.allowWaitlist
                          ? ' - Waitlist is available'
                          : ' - Event is full'}
                      </span>
                    )}
                  </>
                ) : (
                  'Join us for this event!'
                )}
              </p>
            </div>

            <div className="mt-4">
              <EventRegistrationForm
                event={event}
                onRegistrationSuccess={(data) => {
                  console.log('Registration success:', data)
                  // Could show a success message or redirect
                }}
                onRegistrationError={(error) => {
                  console.error('Registration error:', error)
                  // Could show an error message
                }}
              />
            </div>
          </div>
        </section>
      )}

      {/* Event Description */}
      {description && description.trim() && (
        <section className="mt-16">
          <div className="container max-w-4xl">
            <div className="prose dark:prose-invert mx-auto">
              <h2>About This Event</h2>
              <div
                className="max-w-none prose prose-sm dark:prose-invert"
                style={{ whiteSpace: 'pre-line' }}
              >
                {event.description}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Registration Deadline Warning */}
      {event.registrationForm?.registrationDeadline && isRegistrationRequired && (
        <section className="mt-8">
          <div className="container max-w-3xl">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  <strong>Registration Deadline:</strong> {
                    new Date(event.registrationForm.registrationDeadline).toLocaleDateString('en-US', {
                      weekday: 'short',
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })
                  }
                </p>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Back to Events Link */}
      <section className="mt-16">
        <div className="container text-center">
          <Link
            href="/events"
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to All Events
          </Link>
        </div>
      </section>
    </article>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { slug = '' } = await paramsPromise
  const event = await queryEventBySlug({ slug })

  if (!event) {
    return {
      title: 'Event Not Found | Rotary Club Tunis Doyen',
    }
  }

  return generateMeta({
    doc: { ...event, title: `${event.title} | Events` },
  })
}

const queryEventBySlug = cache(async ({ slug, draft = false }: { slug: string; draft?: boolean }) => {
  const payload = await getPayload({ config: payloadConfig })

  const result = await payload.find({
    collection: 'events',
    draft,
    limit: 1,
    overrideAccess: draft,
    pagination: false,
    where: {
      slug: {
        equals: slug,
      },
    },
  })

  return result.docs?.[0] || null
})