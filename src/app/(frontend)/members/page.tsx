import React from 'react'
import { Metadata } from 'next'
import { MemberDirectoryClient } from './page.client'

type MemberDirectoryData = {
  members: {
    id: string
    name: { [key: string]: string }
    classification: { [key: string]: string }
    joiningDate: string
    rotaryId: string
    rotaryDistrict: string
    rotaryClub?: string
    phonePersonal?: string
    phoneWork?: string
    leadershipRoles?: Array<{
      position: { [key: string]: string }
      scope: string
      startYear: number
      isCurrent: boolean
    }>
    committees?: Array<{
      committee: string
      startDate: string
      isActive: boolean
    }>
    serviceProjectCount?: number
    publicServiceProjects?: Array<{
      projectName: string
      projectType: string
      year: number
    }>
    showContact: boolean
    showPhotos: boolean
  }[]
  total: number
  limit: number
  page: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// SEO metadata for the member directory
export const metadata: Metadata = {
  title: 'Rotary Club Tunis Doyen - Member Directory',
  description:
    'Connect with fellow Rotary members in Tunis Doyen. Search our professional directory to find colleagues, collaborators, and network contacts within our club.',
  keywords: 'Rotary Club Tunis Doyen, members, directory, networking, professionals, Tunisia',
  openGraph: {
    title: 'Rotary Club Tunis Doyen - Member Directory',
    description:
      'Connect with fellow professionals and expand your network within Rotary Club Tunis Doyen.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Rotary Club Tunis Doyen - Member Directory',
    description:
      'Connect with fellow professionals and expand your network within Rotary Club Tunis Doyen.',
  },
}

interface MemberDirectoryPageProps {
  searchParams: Promise<{
    search?: string
    district?: string[]
    committee?: string[]
    page?: string
    limit?: string
  }>
}

import { ErrorDisplay } from './ErrorDisplay' // New import for the error component

export default async function MemberDirectoryPage({ searchParams }: MemberDirectoryPageProps) {
  // Await search parameters in Next.js 15 App Router
  const awaitedSearchParams = await searchParams

  // Parse search parameters with defaults
  const search = awaitedSearchParams.search || ''
  const district = Array.isArray(awaitedSearchParams.district)
    ? awaitedSearchParams.district
    : awaitedSearchParams.district
      ? [awaitedSearchParams.district]
      : []
  const committee = Array.isArray(awaitedSearchParams.committee)
    ? awaitedSearchParams.committee
    : awaitedSearchParams.committee
      ? [awaitedSearchParams.committee]
      : []
  const page = parseInt(awaitedSearchParams.page || '1')
  const limit = parseInt(awaitedSearchParams.limit || '12')

  // Server-side data fetching for optimal performance
  let initialData: MemberDirectoryData | null = null
  let error: string | null = null

  try {
    // Build API URL with parameters
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    })

    if (search) {
      params.set('search', search)
    }

    if (district.length > 0) {
      params.set('district', district.join(','))
    }

    if (committee.length > 0) {
      params.set('committee', committee.join(','))
    }

    const apiUrl = `${process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'}/api/members?${params.toString()}`

    // Fetch data with timeout for optimal UX
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

    try {
      const response = await fetch(apiUrl, {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control':
            process.env.NODE_ENV === 'production'
              ? 'public, s-maxage=300, stale-while-revalidate=600'
              : 'no-cache',
        },
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        error = errorData?.error || `HTTP ${response.status}: Failed to load member directory`
        console.error('Member directory fetch error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData,
        })
      } else {
        const apiResponse = await response.json()
        initialData = apiResponse.data as MemberDirectoryData
      }
     } catch (fetchError: unknown) {
       clearTimeout(timeoutId)
       if (fetchError instanceof Error && fetchError.name === 'AbortError') {
         error = 'Request timed out. Please try again.'
       } else {
         error = 'Network error occurred. Please check your connection.'
       }
       console.error('Member directory fetch exception:', fetchError)
     }
  } catch (urlError) {
    error = 'Invalid search parameters provided.'
    console.error('URL construction error:', urlError)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header Section */}
      <div className="bg-linear-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Club Member Directory</h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Connect with fellow professionals and expand your network within Rotary Club Tunis
              Doyen. Discover colleagues, mentors, and collaborators dedicated to service and
              community impact.
            </p>

            {/* Privacy Assurance */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 max-w-2xl mx-auto">
              <div className="flex items-center justify-center space-x-2 text-sm text-blue-100">
                <svg className="w-5 h-5 shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Privacy protected - member data shown only with consent</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Section */}
      <div className="container mx-auto px-4 py-12 max-w-7xl">
        {/* Error State */}
        {error && <ErrorDisplay error={error} />}

        {/* Member Directory Client Component */}
        <MemberDirectoryClient
          initialData={initialData}
          error={error}
          initialSearch={search}
          initialFilters={{ district, committee }}
          initialPagination={{ page, limit }}
        />
      </div>

      {/* Footer */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          <div className="text-center text-sm text-gray-600 dark:text-gray-400">
            <p>
              Data protection is paramount. Only members who have chosen to share their information
              publicly are displayed in this directory. Contact the club administration for privacy
              concerns.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
