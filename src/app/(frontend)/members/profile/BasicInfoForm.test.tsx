import React from 'react'
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { BasicInfoForm } from './BasicInfoForm'

// Mock the global fetch function
global.fetch = vi.fn()

const mockUser = {
  id: 'user-1',
  name: { en: '<PERSON>', fr: '<PERSON>', ar: '<PERSON>' },
  email: '<EMAIL>',
  phonePersonal: '123456789',
  phoneWork: '987654321',
  classification: { en: 'Engineer', fr: 'Ingénieur', ar: 'Engineer' },
  privacySettings: { isPublicProfile: true, shareContactDetails: true, sharePhotos: true, marketingConsent: true },
  communicationPreferences: { emailNotifications: true, newsletterSubscription: true, meetingReminders: true, committeeUpdates: true },
}

describe('BasicInfoForm', () => {
  let setUserMock: ReturnType<typeof vi.fn>

  beforeEach(() => {
    cleanup(); // Ensure a clean DOM before each test
    setUserMock = vi.fn()
    vi.mocked(global.fetch).mockClear()
  })

  afterEach(() => {
    cleanup(); // Ensure a clean DOM after each test
  });

  it('should render the form with initial user data', () => {
    render(<BasicInfoForm user={mockUser} setUser={setUserMock} />)

    // Check if some key fields are populated using getByLabelText
    expect((screen.getByLabelText('Full name in English') as HTMLInputElement).value).toBe(mockUser.name.en)
    expect((screen.getByLabelText('Nom complet en Français') as HTMLInputElement).value).toBe(mockUser.name.fr)
    expect((screen.getByLabelText('Personal Phone') as HTMLInputElement).value).toBe(mockUser.phonePersonal)
    expect((screen.getByLabelText('Your profession in English') as HTMLInputElement).value).toBe(mockUser.classification.en)
  })

  it('should update form state on user input', () => {
    render(<BasicInfoForm user={mockUser} setUser={setUserMock} />)
    const englishNameInput = screen.getByLabelText('Full name in English') as HTMLInputElement

    fireEvent.change(englishNameInput, { target: { value: 'Jane Doe' } })

    expect(englishNameInput.value).toBe('Jane Doe')
  })

  it('should call fetch with updated data on successful submission', async () => {
    // Mock a successful API response
    vi.mocked(global.fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: { user: { ...mockUser, name: { en: 'Jane Doe' } } } }),
    } as Response)

    render(<BasicInfoForm user={mockUser} setUser={setUserMock} />)

    const englishNameInput = screen.getByLabelText('Full name in English') as HTMLInputElement
    fireEvent.change(englishNameInput, { target: { value: 'Jane Doe' } })

    const saveButton = screen.getByRole('button', { name: /Save Basic Information/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/users/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: { en: 'Jane Doe', fr: mockUser.name.fr, ar: mockUser.name.ar },
          phonePersonal: mockUser.phonePersonal,
          phoneWork: mockUser.phoneWork,
          classification: mockUser.classification,
        }),
      })
    })
  })

  it('should display a success message on successful submission', async () => {
    vi.mocked(global.fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: { user: mockUser } }),
    } as Response)

    render(<BasicInfoForm user={mockUser} setUser={setUserMock} />)

    const saveButton = screen.getByRole('button', { name: /Save Basic Information/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.queryByText('Profile information saved successfully!')).not.toBeNull()
    })
  })

  it('should display an error message on failed submission', async () => {
    vi.mocked(global.fetch).mockResolvedValue({
      ok: false,
      json: () => Promise.resolve({ error: 'Invalid data provided' }),
    } as Response)

    render(<BasicInfoForm user={mockUser} setUser={setUserMock} />)

    const saveButton = screen.getByRole('button', { name: /Save Basic Information/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.queryByText('Invalid data provided')).not.toBeNull()
    })
  })
})