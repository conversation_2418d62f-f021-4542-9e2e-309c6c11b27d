'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { BasicInfoForm } from './BasicInfoForm'
import { PrivacySettingsForm } from './PrivacySettingsForm'
import { CommunicationPreferencesForm } from './CommunicationPreferencesForm'
import { ChangePasswordForm } from './ChangePasswordForm'

interface User {
  id: string
  name: {
    en?: string
    fr?: string
    ar?: string
  }
  email: string
  phonePersonal?: string
  phoneWork?: string
  classification: {
    en?: string
    fr?: string
    ar?: string
  }
  privacySettings: {
    isPublicProfile: boolean
    shareContactDetails: boolean
    sharePhotos: boolean
    marketingConsent: boolean
    dataSharingConsent?: boolean
  }
  communicationPreferences: {
    emailNotifications: boolean
    newsletterSubscription: boolean
    meetingReminders: boolean
    committeeUpdates: boolean
  }
  profileCompletion?: number
}

export default function ProfilePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('basic')
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch user profile on component mount
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch('/api/users/profile')
        const data = await response.json()

        if (response.ok && data.success) {
          setUser(data.data.user)
        } else {
          setError(data.error || 'Failed to load profile')
          // Redirect to login if unauthorized
          if (response.status === 401) {
            router.push('/login')
          }
        }
      } catch (err) {
        setError('Network error occurred while loading profile')
        console.error('Profile fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchProfile()
  }, [router])

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-8"></div>
              <div className="flex space-x-4 mb-8">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                ))}
              </div>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20">
                <svg className="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">Error Loading Profile</h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">{error}</p>
              <div className="mt-6">
                <button
                  onClick={() => router.push('/login')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Go to Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Profile Not Found</h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">No profile data available.</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Profile Settings</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage your personal information and privacy preferences
          </p>
        </div>

        {/* Profile Completion Indicator */}
        {user && (
          <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Profile Completion</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Complete your profile to get the most out of your Rotary Club experience
                </p>
              </div>
              <div className="flex items-center">
                <span className="text-lg font-medium text-gray-900 dark:text-white">
                  {user.profileCompletion || 0}%
                </span>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                <div 
                  className="bg-blue-600 h-2.5 rounded-full" 
                  style={{ width: `${user.profileCompletion || 0}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-8 border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8 overflow-x-auto">
            <button
              onClick={() => handleTabChange('basic')}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'basic'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Basic Information
            </button>
            <button
              onClick={() => handleTabChange('privacy')}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'privacy'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Privacy
            </button>
            <button
              onClick={() => handleTabChange('communications')}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'communications'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Communications
            </button>
            <button
              onClick={() => handleTabChange('password')}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'password'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Password
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          {activeTab === 'basic' && <BasicInfoForm user={user} setUser={setUser} />}
          {activeTab === 'privacy' && <PrivacySettingsForm user={user} setUser={setUser} />}
          {activeTab === 'communications' && <CommunicationPreferencesForm user={user} setUser={setUser} />}
          {activeTab === 'password' && <ChangePasswordForm />}
        </div>
      </div>
    </div>
  )
}