import React from 'react'
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ChangePasswordForm } from './ChangePasswordForm'

// Mock the global fetch function
global.fetch = vi.fn()

describe('ChangePasswordForm', () => {
  beforeEach(() => {
    cleanup(); // Ensure a clean DOM before each test
    vi.mocked(global.fetch).mockClear()
  })

  afterEach(() => {
    cleanup(); // Ensure a clean DOM after each test
  });

  it('should render the password change form', () => {
    render(<ChangePasswordForm />)

    expect(screen.getByPlaceholderText('Current Password')).not.toBeNull()
    expect(screen.getByPlaceholderText('New Password')).not.toBeNull()
    expect(screen.getByPlaceholderText('Confirm New Password')).not.toBeNull()
    expect(screen.getByRole('button', { name: 'Change Password' })).not.toBeNull()
  })

  it('should display validation errors for short password', async () => {
    render(<ChangePasswordForm />)

    const currentPasswordInput = screen.getByPlaceholderText('Current Password')
    const newPasswordInput = screen.getByPlaceholderText('New Password')
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm New Password')
    const changePasswordButton = screen.getByRole('button', { name: 'Change Password' })

    // Fill current password to pass first validation check
    fireEvent.change(currentPasswordInput, { target: { value: 'currentPass123!' } })
    // Fill short password to trigger length validation
    fireEvent.change(newPasswordInput, { target: { value: 'short' } })
    fireEvent.change(confirmPasswordInput, { target: { value: 'short' } })
    fireEvent.click(changePasswordButton)

    await screen.findByText((content, element) => content.includes('Password must be at least 8 characters long'))
    expect(global.fetch).not.toHaveBeenCalled()
  })

  it('should display validation errors for non-matching passwords', async () => {
    render(<ChangePasswordForm />)

    const currentPasswordInput = screen.getByPlaceholderText('Current Password')
    const newPasswordInput = screen.getByPlaceholderText('New Password')
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm New Password')
    const changePasswordButton = screen.getByRole('button', { name: 'Change Password' })

    // Fill current password and valid new password but non-matching confirm password
    fireEvent.change(currentPasswordInput, { target: { value: 'currentPass123!' } })
    fireEvent.change(newPasswordInput, { target: { value: 'SecurePass123!' } })
    fireEvent.change(confirmPasswordInput, { target: { value: 'SecurePass123' } })
    fireEvent.click(changePasswordButton)

    await waitFor(() => {
      expect(screen.getByText((content, element) => content.includes('New passwords do not match'))).not.toBeNull()
    })
    expect(global.fetch).not.toHaveBeenCalled()
  })

  it('should call fetch with updated data on successful password change', async () => {
    // Mock a successful API response
    vi.mocked(global.fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, message: 'Password changed successfully!' }),
    } as Response)

    render(<ChangePasswordForm />)

    const currentPasswordInput = screen.getByPlaceholderText('Current Password')
    const newPasswordInput = screen.getByPlaceholderText('New Password')
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm New Password')
    const changePasswordButton = screen.getByRole('button', { name: 'Change Password' })

    fireEvent.change(currentPasswordInput, { target: { value: 'OldSecurePass123!' } })
    fireEvent.change(newPasswordInput, { target: { value: 'NewSecurePass123!' } })
    fireEvent.change(confirmPasswordInput, { target: { value: 'NewSecurePass123!' } })
    fireEvent.click(changePasswordButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/users/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: 'OldSecurePass123!',
          newPassword: 'NewSecurePass123!',
        }),
      })
    })
  })

  it('should display a success message on successful password change', async () => {
    vi.mocked(global.fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, message: 'Password changed successfully!' }),
    } as Response)

    render(<ChangePasswordForm />)

    const currentPasswordInput = screen.getByPlaceholderText('Current Password')
    const newPasswordInput = screen.getByPlaceholderText('New Password')
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm New Password')
    const changePasswordButton = screen.getByRole('button', { name: 'Change Password' })

    fireEvent.change(currentPasswordInput, { target: { value: 'OldSecurePass123!' } })
    fireEvent.change(newPasswordInput, { target: { value: 'NewSecurePass123!' } })
    fireEvent.change(confirmPasswordInput, { target: { value: 'NewSecurePass123!' } })
    fireEvent.click(changePasswordButton)

    await waitFor(() => {
      expect(screen.getByText((content, element) => content.includes('Password changed successfully!'))).not.toBeNull()
    })
  })

  it('should display an error message on failed password change', async () => {
    vi.mocked(global.fetch).mockResolvedValue({
      ok: false,
      json: () => Promise.resolve({ error: 'Incorrect current password' }),
    } as Response)

    render(<ChangePasswordForm />)

    const currentPasswordInput = screen.getByPlaceholderText('Current Password')
    const newPasswordInput = screen.getByPlaceholderText('New Password')
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm New Password')
    const changePasswordButton = screen.getByRole('button', { name: 'Change Password' })

    fireEvent.change(currentPasswordInput, { target: { value: 'WrongPass' } })
    fireEvent.change(newPasswordInput, { target: { value: 'NewSecurePass123!' } })
    fireEvent.change(confirmPasswordInput, { target: { value: 'NewSecurePass123!' } })
    fireEvent.click(changePasswordButton)

    await screen.findByText((content, element) => content.includes('Incorrect current password'))
  })
})
