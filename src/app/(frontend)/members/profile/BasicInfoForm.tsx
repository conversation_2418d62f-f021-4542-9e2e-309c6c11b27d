'use client'

import React, { useState } from 'react'

interface User {
  id: string
  name: {
    en?: string
    fr?: string
    ar?: string
  }
  email: string
  phonePersonal?: string
  phoneWork?: string
  classification: {
    en?: string
    fr?: string
    ar?: string
  }
  privacySettings: {
    isPublicProfile: boolean
    shareContactDetails: boolean
    sharePhotos: boolean
    marketingConsent: boolean
    dataSharingConsent?: boolean
  }
  communicationPreferences: {
    emailNotifications: boolean
    newsletterSubscription: boolean
    meetingReminders: boolean
    committeeUpdates: boolean
  }
}

interface BasicInfoFormProps {
  user: User
  setUser: (user: User) => void
}

export function BasicInfoForm({ user, setUser }: BasicInfoFormProps) {
  const [formData, setFormData] = useState({
    name: {
      en: user.name?.en || '',
      fr: user.name?.fr || '',
      ar: user.name?.ar || ''
    },
    phonePersonal: user.phonePersonal || '',
    phoneWork: user.phoneWork || '',
    classification: {
      en: user.classification?.en || '',
      fr: user.classification?.fr || '',
      ar: user.classification?.ar || ''
    }
  })
  
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleChange = (field: string, value: string, lang?: string) => {
    if (lang) {
      setFormData(prev => ({
        ...prev,
        [field]: {
          ...prev[field as keyof typeof prev],
          [lang]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setSuccess(null)
    setError(null)

    try {
      const response = await fetch('/api/users/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setSuccess('Profile information saved successfully!')
        // Update user state with new data
        setUser({
          ...user,
          ...data.data.user
        })
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(data.error || 'Failed to save profile information')
      }
    } catch (err) {
      setError('Network error occurred while saving profile')
      console.error('Profile save error:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Basic Information</h2>
      
      {success && (
        <div className="mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4">
          <div className="flex">
            <div className="shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800 dark:text-green-200">{success}</p>
            </div>
          </div>
        </div>
      )}
      
      {error && (
        <div className="mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4">
          <div className="flex">
            <div className="shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Fields */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Name</h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label htmlFor="name-en" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Full name in English
              </label>
              <input
                type="text"
                id="name-en"
                value={formData.name.en}
                onChange={(e) => handleChange('name', e.target.value, 'en')}
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm text-gray-900 dark:text-white"
                placeholder="Full name in English"
              />
            </div>
            <div>
              <label htmlFor="name-fr" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nom complet en Français
              </label>
              <input
                type="text"
                id="name-fr"
                value={formData.name.fr}
                onChange={(e) => handleChange('name', e.target.value, 'fr')}
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm text-gray-900 dark:text-white"
                placeholder="Nom complet en Français"
              />
            </div>
            <div>
              <label htmlFor="name-ar" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                الاسم الكامل بالعربية
              </label>
              <input
                type="text"
                id="name-ar"
                value={formData.name.ar}
                onChange={(e) => handleChange('name', e.target.value, 'ar')}
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm text-gray-900 dark:text-white"
                placeholder="الاسم الكامل بالعربية"
              />
            </div>
          </div>
        </div>
        
        {/* Phone Number Fields */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact Information</h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label htmlFor="phonePersonal" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Personal Phone
              </label>
              <input
                type="tel"
                id="phonePersonal"
                value={formData.phonePersonal}
                onChange={(e) => handleChange('phonePersonal', e.target.value)}
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm text-gray-900 dark:text-white"
                placeholder="+216 123 456 789"
              />
            </div>
            <div>
              <label htmlFor="phoneWork" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Work Phone
              </label>
              <input
                type="tel"
                id="phoneWork"
                value={formData.phoneWork}
                onChange={(e) => handleChange('phoneWork', e.target.value)}
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm text-gray-900 dark:text-white"
                placeholder="+216 987 654 321"
              />
            </div>
          </div>
        </div>
        
        {/* Classification Fields */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Professional Classification</h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label htmlFor="classification-en" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Your profession in English
              </label>
              <input
                type="text"
                id="classification-en"
                value={formData.classification.en}
                onChange={(e) => handleChange('classification', e.target.value, 'en')}
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm text-gray-900 dark:text-white"
                placeholder="Your profession in English"
              />
            </div>
            <div>
              <label htmlFor="classification-fr" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Votre profession en Français
              </label>
              <input
                type="text"
                id="classification-fr"
                value={formData.classification.fr}
                onChange={(e) => handleChange('classification', e.target.value, 'fr')}
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm text-gray-900 dark:text-white"
                placeholder="Votre profession en Français"
              />
            </div>
            <div>
              <label htmlFor="classification-ar" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                مهنتك بالعربية
              </label>
              <input
                type="text"
                id="classification-ar"
                value={formData.classification.ar}
                onChange={(e) => handleChange('classification', e.target.value, 'ar')}
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm text-gray-900 dark:text-white"
                placeholder="مهنتك بالعربية"
              />
            </div>
          </div>
        </div>
        
        {/* Rotary Information (Read-only) */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Rotary Information</h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Rotary Club
              </label>
              <input
                type="text"
                value={user.rotaryClub || 'Rotary Club Tunis Doyen'}
                disabled
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 px-3 py-2 shadow-sm sm:text-sm text-gray-900 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Rotary District
              </label>
              <input
                type="text"
                value={user.rotaryDistrict || '1930'}
                disabled
                className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 px-3 py-2 shadow-sm sm:text-sm text-gray-900 dark:text-white"
              />
            </div>
          </div>
        </div>
        
        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              'Save Basic Information'
            )}
          </button>
        </div>
      </form>
    </div>
  )
}