import React from 'react'
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { PrivacySettingsForm } from './PrivacySettingsForm'

// Mock the global fetch function
global.fetch = vi.fn()

const mockUser = {
  id: 'user-1',
  name: { en: '<PERSON>' },
  email: '<EMAIL>',
  privacySettings: {
    isPublicProfile: false,
    shareContactDetails: true,
    sharePhotos: false,
    marketingConsent: true,
    dataSharingConsent: false,
  },
  communicationPreferences: { emailNotifications: true, newsletterSubscription: true, meetingReminders: true, committeeUpdates: true },
}

describe('PrivacySettingsForm', () => {
  let setUserMock: ReturnType<typeof vi.fn>

  beforeEach(() => {
    cleanup(); // Ensure a clean DOM before each test
    setUserMock = vi.fn()
    vi.mocked(global.fetch).mockClear()
  })

  afterEach(() => {
    cleanup(); // Ensure a clean DOM after each test
  });

  it('should render the form with initial privacy settings', () => {
    render(<PrivacySettingsForm user={mockUser} setUser={setUserMock} />)

    expect((screen.getByLabelText('Public Profile') as HTMLInputElement).checked).toBe(false)
    expect((screen.getByLabelText('Share Contact Details') as HTMLInputElement).checked).toBe(true)
    expect((screen.getByLabelText('Share Photos') as HTMLInputElement).checked).toBe(false)
    expect((screen.getByLabelText('Marketing Emails') as HTMLInputElement).checked).toBe(true)
    expect((screen.getByLabelText('Share with Partners') as HTMLInputElement).checked).toBe(false)
  })

  it('should update form state on checkbox toggle', () => {
    render(<PrivacySettingsForm user={mockUser} setUser={setUserMock} />)
    const publicProfileCheckbox = screen.getByLabelText('Public Profile') as HTMLInputElement

    fireEvent.click(publicProfileCheckbox)

    expect(publicProfileCheckbox.checked).toBe(true)
  })

  it('should call fetch with updated data on successful submission', async () => {
    // Mock a successful API response
    vi.mocked(global.fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: { user: { ...mockUser, privacySettings: { ...mockUser.privacySettings, isPublicProfile: true } } } }),
    } as Response)

    render(<PrivacySettingsForm user={mockUser} setUser={setUserMock} />)

    const publicProfileCheckbox = screen.getByLabelText('Public Profile') as HTMLInputElement
    fireEvent.click(publicProfileCheckbox)

    const saveButton = screen.getByRole('button', { name: /Save Privacy Settings/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/users/privacy/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          privacySettings: {
            isPublicProfile: true,
            shareContactDetails: mockUser.privacySettings.shareContactDetails,
            sharePhotos: mockUser.privacySettings.sharePhotos,
            marketingConsent: mockUser.privacySettings.marketingConsent,
            dataSharingConsent: mockUser.privacySettings.dataSharingConsent,
          },
        }),
      })
    })
  })

  it('should display a success message on successful submission', async () => {
    vi.mocked(global.fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: { user: mockUser } }),
    } as Response)

    render(<PrivacySettingsForm user={mockUser} setUser={setUserMock} />)

    const saveButton = screen.getByRole('button', { name: /Save Privacy Settings/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.queryByText('Privacy settings saved successfully!')).not.toBeNull()
    })
  })

  it('should display an error message on failed submission', async () => {
    vi.mocked(global.fetch).mockResolvedValue({
      ok: false,
      json: () => Promise.resolve({ error: 'Failed to save privacy settings' }),
    } as Response)

    render(<PrivacySettingsForm user={mockUser} setUser={setUserMock} />)

    const saveButton = screen.getByRole('button', { name: /Save Privacy Settings/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.queryByText('Failed to save privacy settings')).not.toBeNull()
    })
  })
})