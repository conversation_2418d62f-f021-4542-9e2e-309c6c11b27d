import React from 'react'
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { CommunicationPreferencesForm } from './CommunicationPreferencesForm'

// Mock the global fetch function
global.fetch = vi.fn()

const mockUser = {
  id: 'user-1',
  name: { en: '<PERSON>' },
  email: '<EMAIL>',
  privacySettings: {
    isPublicProfile: false,
    shareContactDetails: true,
    sharePhotos: false,
    marketingConsent: true,
    dataSharingConsent: false,
  },
  communicationPreferences: {
    emailNotifications: true,
    newsletterSubscription: false,
    meetingReminders: true,
    committeeUpdates: false,
  },
}

describe('CommunicationPreferencesForm', () => {
  let setUserMock: ReturnType<typeof vi.fn>

  beforeEach(() => {
    cleanup(); // Ensure a clean DOM before each test
    setUserMock = vi.fn()
    vi.mocked(global.fetch).mockClear()
  })

  afterEach(() => {
    cleanup(); // Ensure a clean DOM after each test
  });

  it('should render the form with initial communication preferences', () => {
    render(<CommunicationPreferencesForm user={mockUser} setUser={setUserMock} />)

    expect((screen.getByLabelText('Event Updates and Announcements') as HTMLInputElement).checked).toBe(true)
    expect((screen.getByLabelText('Monthly Club Newsletter') as HTMLInputElement).checked).toBe(false)
    expect((screen.getByLabelText('Meeting and Event Reminders') as HTMLInputElement).checked).toBe(true)
    expect((screen.getByLabelText('Committee Activity Updates') as HTMLInputElement).checked).toBe(false)
  })

  it('should update form state on checkbox toggle', () => {
    render(<CommunicationPreferencesForm user={mockUser} setUser={setUserMock} />)
    const newsletterCheckbox = screen.getByLabelText('Monthly Club Newsletter') as HTMLInputElement

    fireEvent.click(newsletterCheckbox)

    expect(newsletterCheckbox.checked).toBe(true)
  })

  it('should call fetch with updated data on successful submission', async () => {
    // Mock a successful API response
    vi.mocked(global.fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: { user: { ...mockUser, communicationPreferences: { ...mockUser.communicationPreferences, newsletterSubscription: true } } } }),
    } as Response)

    render(<CommunicationPreferencesForm user={mockUser} setUser={setUserMock} />)

    const newsletterCheckbox = screen.getByLabelText('Monthly Club Newsletter') as HTMLInputElement
    fireEvent.click(newsletterCheckbox)

    const saveButton = screen.getByRole('button', { name: /Save Communication Preferences/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/users/communications/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          communicationPreferences: {
            emailNotifications: mockUser.communicationPreferences.emailNotifications,
            newsletterSubscription: true,
            meetingReminders: mockUser.communicationPreferences.meetingReminders,
            committeeUpdates: mockUser.communicationPreferences.committeeUpdates,
          },
        }),
      })
    })
  })

  it('should display a success message on successful submission', async () => {
    vi.mocked(global.fetch).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: { user: mockUser } }),
    } as Response)

    render(<CommunicationPreferencesForm user={mockUser} setUser={setUserMock} />)

    const saveButton = screen.getByRole('button', { name: /Save Communication Preferences/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.queryByText('Communication preferences saved successfully!')).not.toBeNull()
    })
  })

  it('should display an error message on failed submission', async () => {
    vi.mocked(global.fetch).mockResolvedValue({
      ok: false,
      json: () => Promise.resolve({ error: 'Failed to save communication preferences' }),
    } as Response)

    render(<CommunicationPreferencesForm user={mockUser} setUser={setUserMock} />)

    const saveButton = screen.getByRole('button', { name: /Save Communication Preferences/i })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.queryByText('Failed to save communication preferences')).not.toBeNull()
    })
  })
})