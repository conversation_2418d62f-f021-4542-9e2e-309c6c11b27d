// Load environment variables properly
// import { config } from 'dotenv'
// import { readFileSync, existsSync } from 'fs'
// import { join } from 'path'
// import { fileURLToPath } from 'url'

// Override environment loading for standalone execution
// const loadEnv = () => {
//   const __filename = fileURLToPath(import.meta.url)
//   const __dirname = path.dirname(__filename)
//   const testEnvPath = join(__dirname, '../test.env')
//   const envPath = join(__dirname, '../.env')

//   // Try test.env first, then .env
//   if (existsSync(testEnvPath)) {
//     const envContent = readFileSync(testEnvPath, 'utf8')
//     envContent.split('\n').forEach(line => {
//       const [key, ...valueParts] = line.split('=')
//       if (key && valueParts.length > 0 && !key.startsWith('NODE_OPTIONS')) {
//         // Properly strip quotes and whitespace from values
//         const rawValue = valueParts.join('=').trim()
//         const cleanedValue = rawValue.replace(/^["']|["']$/g, '')
//         process.env[key.trim()] = cleanedValue
//       }
//     })
//     console.log('✅ Environment variables loaded from test.env')
//   } else if (existsSync(envPath)) {
//     config({ path: envPath })
//     console.log('✅ Environment variables loaded from .env')
//   } else {
//     console.log('⚠️  No environment file found')
//   }
// }

// Ensure environment variables are loaded before any other operations
// loadEnv()

// storage-adapter-import-placeholder
import { mongooseAdapter } from '@payloadcms/db-mongodb'

import sharp from 'sharp' // sharp-import
import path, { dirname } from 'path'
import { fileURLToPath } from 'url'
import { buildConfig, PayloadRequest } from 'payload'
import { resendAdapter } from '@payloadcms/email-resend'
import { en } from '@payloadcms/translations/languages/en'
import { ar } from '@payloadcms/translations/languages/ar'
import { fr } from '@payloadcms/translations/languages/fr'

import { Categories } from './collections/Categories'
import { Events } from './collections/Events'
import { Media } from './collections/Media'
import { Pages } from './collections/Pages'
import { Posts } from './collections/Posts'
import { Users } from './collections/Users'
import { Footer } from './Footer/config'
import { Header } from './Header/config'
import { ImpactStats } from './globals/ImpactStats'
import { eventRegistration } from './endpoints/event-registration'
import { plugins } from './plugins'
import { defaultLexical } from '@/fields/defaultLexical'
import { getServerSideURL } from './utilities/getURL'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below.
      beforeLogin: ['@/components/BeforeLogin'],
      // The `BeforeDashboard` component renders the 'welcome' block that you see after logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below.
      beforeDashboard: ['@/components/BeforeDashboard'],
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    ...(process.env.NODE_ENV === 'development' ? {
      livePreview: {
        breakpoints: [
          {
            label: 'Mobile',
            name: 'mobile',
            width: 375,
            height: 667,
          },
          {
            label: 'Tablet',
            name: 'tablet',
            width: 768,
            height: 1024,
          },
          {
            label: 'Desktop',
            name: 'desktop',
            width: 1440,
            height: 900,
          },
        ],
      },
    } : {}),
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  db: mongooseAdapter({
    url: process.env.DATABASE_URI as string,
  }),
  collections: [Pages, Posts, Media, Categories, Users, Events],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [Header, Footer, ImpactStats],
  endpoints: [eventRegistration],
  plugins: [
    ...plugins,
    // storage-adapter-placeholder
  ],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  // i18n configuration for admin interface translation
  i18n: {
    supportedLanguages: { en, ar, fr },
    fallbackLanguage: 'en',
    translations: {
      en: {
        // Custom translations can be added here
        custom: {
          // Add custom keys as needed
        },
      },
      ar: {
        // Arabic custom translations
        custom: {
          // Add Arabic custom keys as needed
        },
      },
      fr: {
        // French custom translations
        custom: {
          // Add French custom keys as needed
        },
      },
    },
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow only authenticated users with admin role or cron jobs with valid secret
        if (req.user && req.user.collection === 'users') return true

        // For cron jobs, check for valid CRON_SECRET
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      },
    },
    tasks: [],
  },
  localization: {
    locales: [
      {
        label: 'English',
        code: 'en',
      },
      {
        label: 'Français',
        code: 'fr',
      },
      {
        label: 'العربية',
        code: 'ar',
        rtl: true,
      },
    ],
    defaultLocale: 'en',
    fallback: true,
  },
  email: resendAdapter({
    defaultFromAddress: 'no-reply @rotary-tunis-doyen.org',
    defaultFromName: 'Rotary Tunis Doyen',
    apiKey: process.env.RESEND_API_KEY as string,
  }),
})
