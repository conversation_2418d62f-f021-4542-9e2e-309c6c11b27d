import type { CollectionConfig } from 'payload'

export const EmailTemplates: CollectionConfig = {
  slug: 'email-templates',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'type', 'language', 'subject'], // Added subject to default columns
    group: 'Configuration',
  },
  access: {
    read: () => true, // Public read access for fetching templates
    create: ({ req: { user } }) => Boolean(user?.roles?.includes('admin')),
    update: ({ req: { user } }) => Boolean(user?.roles?.includes('admin')),
    delete: ({ req: { user } }) => Boolean(user?.roles?.includes('admin')),
  },
  fields: [
    {
      name: 'name',
      label: 'Template Name',
      type: 'text',
      required: true,
      localized: false,
      admin: {
        description: 'A descriptive name for this template (e.g., "Event Registration Confirmation")',
      },
    },
    {
      name: 'type',
      label: 'Template Type',
      type: 'select',
      required: true,
      localized: false,
      options: [
        {
          label: 'Registration Confirmation',
          value: 'registration_confirmation',
        },
        {
          label: 'Organizer Notification',
          value: 'organizer_notification',
        },
        {
          label: 'Password Reset',
          value: 'password_reset',
        },
        {
          label: 'Welcome Email',
          value: 'welcome_email',
        },
        // Add more template types as needed
      ],
      admin: {
        description: 'The type of email this template is used for',
      },
    },
    {
      name: 'language',
      label: 'Language',
      type: 'select',
      required: true,
      localized: false,
      options: [
        {
          label: 'English',
          value: 'en',
        },
        {
          label: 'French',
          value: 'fr',
        },
        {
          label: 'Arabic',
          value: 'ar',
        },
      ],
      admin: {
        description: 'The language of the email template',
      },
    },
    {
      name: 'subject',
      label: 'Subject',
      type: 'text',
      required: true,
      localized: true, // Subject should be localized
      admin: {
        description: 'The subject line of the email',
      },
    },
    {
      name: 'html',
      label: 'HTML Content',
      type: 'code', // Use 'code' type for HTML content
      required: true,
      localized: true, // HTML content should be localized
      admin: {
        language: 'html',
        description: 'The HTML content of the email template',
      },
    },
    {
      name: 'text',
      label: 'Text Content',
      type: 'textarea', // Use 'textarea' for plain text content
      required: true,
      localized: true, // Text content should be localized
      admin: {
        description: 'The plain text content of the email template (for email clients that do not support HTML)',
      },
    },
    // GDPR compliance fields
    {
      name: 'isArchived',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        readOnly: true, // Should not be directly editable by admins
        position: 'sidebar',
        description: 'Indicates if this template has been archived for GDPR compliance.',
      },
    },
    {
      name: 'archivedAt',
      type: 'date',
      admin: {
        readOnly: true, // Should not be directly editable by admins
        position: 'sidebar',
        description: 'Timestamp when this template was archived for GDPR compliance.',
      },
    },
  ],
}