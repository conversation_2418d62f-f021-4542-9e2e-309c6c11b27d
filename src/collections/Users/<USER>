import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
// Removed unused import

// Note: Advanced field-level access controls temporarily simplified
// In production, implement granular privacy controls using Payload's access pattern
// const restrictedAccess = {
//   read: async ({ req }) => authenticated logic with user ownership checks,
//   update: async ({ req }) => authenticated logic with user ownership checks
// }

export const Users: CollectionConfig = {
  slug: 'users',

  // Access controls - granular privacy protection
  access: {
    admin: authenticated,
    create: authenticated,
    delete: authenticated,
    read: authenticated,
    update: authenticated,
  },

  admin: {
    defaultColumns: ['name', 'classification', 'rotaryDistrict', 'joiningDate'],
    useAsTitle: 'name',
    description: 'Enhanced Rotary member profiles with privacy controls',
  },

  auth: true,

  fields: [
    // === BASIC MEMBERSHIP INFORMATION (6 fields) ===

    // Core Name Field - Localized Support
    {
      name: 'name',
      type: 'text',
      localized: true,
      required: true,
      admin: {
        description: 'Member full name - supported in EN/FR/AR',
      },
    },

    // Contact Information Fields
    {
      name: 'phonePersonal',
      type: 'text',
      required: false,
      admin: {
        description: 'Personal phone number for club communications',
      },
    },
    {
      name: 'phoneWork',
      type: 'text',
      required: false,
      admin: {
        description: 'Work phone number (optional for business communications)',
      },
    },

    // Professional Information
    {
      name: 'classification',
      type: 'text',
      localized: true,
      required: true,
      admin: {
        description:
          'Professional classification (Doctor, Engineer, Architect, etc.) - supports EN/FR/AR',
      },
    },
    {
      name: 'joiningDate',
      type: 'date',
      required: true,
      admin: {
        description: 'Date member joined Rotary Club',
        date: { pickerAppearance: 'dayOnly' },
      },
    },

    // === ROTARY MEMBERSHIP DETAILS (3 fields) ===

    {
      name: 'rotaryId',
      type: 'text',
      required: true,
      admin: {
        description: 'Rotary International Member ID',
      },
      validate: (value: string | null | undefined) => {
        // Basic Rotary ID validation - can be customized
        if (typeof value !== 'string') return true
        if (!value || value.length < 3) {
          return 'Rotary ID must be at least 3 characters'
        }
        return true
      },
    },
    {
      name: 'rotaryDistrict',
      type: 'select',
      required: true,
      options: [
        { label: 'District 1930', value: '1930' },
        { label: 'District 1890', value: '1890' },
        { label: 'Other District', value: 'other' },
      ],
      admin: {
        description: 'Rotary District affiliation',
      },
    },
    {
      name: 'rotaryClub',
      type: 'text',
      defaultValue: 'Rotary Club Tunis Doyen',
      admin: {
        description: 'Local club name (default: Tunis Doyen)',
      },
    },

    // === SERVICE & LEADERSHIP INFORMATION ===

    // Committee Memberships Array
    {
      name: 'committees',
      type: 'array',
      label: 'Committee Memberships',
      admin: {
        description: 'Current and past committee memberships',
      },
      fields: [
        {
          name: 'committee',
          type: 'select',
          required: true,
          options: [
            { label: 'President Elect Training', value: 'president-elect' },
            { label: 'Director Nominating', value: 'director-nominating' },
            { label: 'Community Service', value: 'community-service' },
            { label: 'Vocational Service', value: 'vocational-service' },
            { label: 'International Service', value: 'international-service' },
            { label: 'Youth Service', value: 'youth-service' },
            { label: 'Membership', value: 'membership' },
            { label: 'Club Service', value: 'club-service' },
          ],
          admin: {
            description: 'Specific committee area',
          },
        },
        {
          name: 'startDate',
          type: 'date',
          required: true,
          admin: {
            date: { pickerAppearance: 'dayOnly' },
            description: 'Committee membership start date',
          },
        },
        {
          name: 'endDate',
          type: 'date',
          required: false,
          admin: {
            date: { pickerAppearance: 'dayOnly' },
            description: 'Committee membership end date (leave empty if current)',
          },
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description: 'Is this an active committee membership?',
          },
        },
      ],
    },

    // Leadership Positions Array
    {
      name: 'leadershipRoles',
      type: 'array',
      label: 'Leadership Positions',
      admin: {
        description: 'Leadership roles held in Rotary at club, district, or international level',
      },
      fields: [
        {
          name: 'position',
          type: 'text',
          localized: true,
          required: true,
          admin: {
            description: 'Position title (e.g., President, Secretary) - supports EN/FR/AR',
          },
        },
        {
          name: 'scope',
          type: 'select',
          required: true,
          options: [
            { label: 'Club Level', value: 'club' },
            { label: 'District Level', value: 'district' },
            { label: 'International Level', value: 'international' },
          ],
          admin: {
            description: 'Scope of leadership role',
          },
        },
        {
          name: 'startYear',
          type: 'number',
          required: true,
          min: 1905, // Year Rotary was founded
          admin: {
            description: 'Year the leadership role began (Rotary International founded 1905)',
          },
        },
        {
          name: 'endYear',
          type: 'number',
          required: false,
          admin: {
            description: 'Year the leadership role ended (leave empty if current)',
          },
        },
        {
          name: 'isCurrent',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Is this a current leadership position?',
          },
        },
      ],
    },

    // === SERVICE PROJECTS & AWARDS ===

    // Service Projects Array
    {
      name: 'serviceProjects',
      type: 'array',
      label: 'Service Project Contributions',
      admin: {
        description: 'Community service and international projects participated in',
      },
      fields: [
        {
          name: 'projectName',
          type: 'text',
          required: true,
          admin: {
            description: 'Name or title of the service project',
          },
        },
        {
          name: 'projectType',
          type: 'select',
          required: true,
          options: [
            { label: 'Community Service', value: 'community' },
            { label: 'International Service', value: 'international' },
            { label: 'Vocational Service', value: 'vocational' },
            { label: 'Youth Service', value: 'youth' },
          ],
        },
        {
          name: 'year',
          type: 'number',
          required: true,
          admin: {
            description: 'Year the project was completed',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          required: false,
          admin: {
            description: 'Brief description of project contribution and outcomes',
          },
        },
        {
          name: 'hours',
          type: 'number',
          required: false,
          min: 0,
          admin: {
            description: 'Approximate hours contributed to project',
          },
        },
        {
          name: 'certificates',
          type: 'array',
          fields: [
            {
              name: 'certificate',
              type: 'upload',
              relationTo: 'media',
              required: true,
              admin: {
                description: 'Upload certificate or recognition document',
              },
            },
          ],
          admin: {
            description: 'Certificates or documents related to this project',
          },
        },
      ],
    },

    // Awards and Recognitions
    {
      name: 'awards',
      type: 'array',
      label: 'Awards & Recognitions',
      admin: {
        description: 'Awards, certificates, and special recognitions received',
      },
      fields: [
        {
          name: 'awardName',
          type: 'text',
          required: true,
          admin: {
            description: 'Name of the award or recognition',
          },
        },
        {
          name: 'awardType',
          type: 'select',
          required: true,
          options: [
            { label: 'Club Level', value: 'club' },
            { label: 'District Level', value: 'district' },
            { label: 'International Level', value: 'international' },
          ],
          admin: {
            description: 'Scope of the award (club, district, or international)',
          },
        },
        {
          name: 'year',
          type: 'number',
          required: true,
          admin: {
            description: 'Year the award was received',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          required: false,
          admin: {
            description: 'Description of the award and criteria met',
          },
        },
        {
          name: 'certificate',
          type: 'upload',
          relationTo: 'media',
          required: false,
          admin: {
            description: 'Upload award certificate or document',
          },
        },
      ],
    },

    // === PRIVACY & CONSENT CONTROLS ===

    // Privacy Settings Group
    {
      name: 'privacySettings',
      type: 'group',
      label: 'Privacy & Consent Settings',
      admin: {
        description: 'Control what information is visible and how it can be used',
      },
      fields: [
        {
          name: 'isPublicProfile',
          type: 'checkbox',
          defaultValue: false,
          label: 'Allow public profile (Member Directory)',
          admin: {
            description: 'Allow profile to appear in public member directory - default: private',
          },
        },
        {
          name: 'shareContactDetails',
          type: 'checkbox',
          defaultValue: true,
          label: 'Share contact information',
          admin: {
            description: 'Allow sharing phone/email with other verified club members',
          },
        },
        {
          name: 'sharePhotos',
          type: 'checkbox',
          defaultValue: true,
          label: 'Allow photos in publications',
          admin: {
            description: 'Allow use of photos in club newsletters, events, and publications',
          },
        },
        {
          name: 'marketingConsent',
          type: 'checkbox',
          defaultValue: false,
          label: 'Receive marketing emails',
          admin: {
            description: 'Opt-in to receive promotional emails from Rotary Club Tunis Doyen',
          },
        },
        {
          name: 'dataSharingConsent',
          type: 'checkbox',
          defaultValue: false,
          label: 'Share data with partners',
          admin: {
            description: 'Allow sharing contact details with district partners for collaboration',
          },
        },
      ],
    },

    // Communication Preferences Group
    {
      name: 'communicationPreferences',
      type: 'group',
      label: 'Communication Preferences',
      admin: {
        description: 'Control what notifications and communications you receive',
      },
      fields: [
        {
          name: 'emailNotifications',
          type: 'checkbox',
          defaultValue: true,
          label: 'Event updates and announcements',
          admin: {
            description: 'Receive email notifications about upcoming events and club announcements',
          },
        },
        {
          name: 'newsletterSubscription',
          type: 'checkbox',
          defaultValue: true,
          label: 'Monthly club newsletter',
          admin: {
            description: 'Receive monthly club newsletter with member updates and news',
          },
        },
        {
          name: 'meetingReminders',
          type: 'checkbox',
          defaultValue: true,
          label: 'Meeting and event reminders',
          admin: {
            description: 'Receive reminders for scheduled meetings and events',
          },
        },
        {
          name: 'committeeUpdates',
          type: 'checkbox',
          defaultValue: true,
          label: 'Committee activity updates',
          admin: {
            description: 'Receive updates about committee activities when you are a member',
          },
        },
      ],
    },

    // === MEMBERSHIP & LEGACY INFORMATION ===

    // Legacy: Member who sponsored this member's induction
    {
      name: 'sponsorName',
      type: 'text',
      required: false,
      admin: {
        description: 'Name of the member who sponsored your Rotary membership (optional)',
      },
    },

    // === SYSTEM FIELDS (Automatically Managed) ===

    // Profile Completion Percentage (Calculated field)
    {
      name: 'profileCompletion',
      type: 'number',
      required: false,
      min: 0,
      max: 100,
      admin: {
        description: 'Profile completion percentage (automatically calculated)',
        readOnly: true,
        position: 'sidebar',
      },
    },

    // Last Login Tracking
    {
      name: 'lastLogin',
      type: 'date',
      required: false,
      admin: {
        description: 'Last authenticated login timestamp',
        readOnly: true,
        position: 'sidebar',
      },
    },

    // Security Tracking (for admins)
    {
      name: 'passwordResetAttempts',
      type: 'number',
      required: false,
      defaultValue: 0,
      min: 0,
      admin: {
        description: 'Recent password reset attempts (for security monitoring)',
        readOnly: true,
        position: 'sidebar',
      },
    },
  ],

  // Hooks for profile completion and validation
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        // Automatically calculate profile completion percentage
        if (operation === 'create' || operation === 'update') {
          const completionScore = calculateProfileCompletion(data)
          data.profileCompletion = completionScore

          // Set lastLogin if not present (for new users or admins creating profiles)
          if (!data.lastLogin) {
            data.lastLogin = new Date().toISOString()
          }
        }

        // Validation for Rotary ID format
        if (data.rotaryId && !isValidRotaryId(data.rotaryId)) {
          throw new Error('Invalid Rotary ID format')
        }

        return data
      },
    ],
  },

  timestamps: true,
}

// Helper Functions
function calculateProfileCompletion(userData: {
  name?: string
  classification?: string
  rotaryId?: string
  rotaryDistrict?: string
  joiningDate?: string
  rotaryClub?: string
  phonePersonal?: string
  phoneWork?: string
  committees?: Array<{
    committee: string
    startDate: string
    endDate?: string
    isActive: boolean
  }>
  leadershipRoles?: Array<{
    position: string
    scope: 'club' | 'district' | 'international'
    startYear: number
    endYear?: number
    isCurrent: boolean
  }>
  serviceProjects?: Array<{
    projectName: string
    projectType: 'community' | 'international' | 'vocational' | 'youth'
    year: number
    description?: string
    hours?: number
    certificates?: Array<{
      certificate: {
        relationTo: 'media'
        value: string
      }
    }>
  }>
  awards?: Array<{
    awardName: string
    awardType: 'club' | 'district' | 'international'
    year: number
    description?: string
    certificate?: {
      relationTo: 'media'
      value: string
    }
  }>
  communicationPreferences?: Record<string, unknown>
  sponsorName?: string
}): number {
  const totalFields = 16 // Total number of profile fields
  let completedFields = 0

  // Basic Information (4/6 required)
  if (userData.name) completedFields++
  if (userData.classification) completedFields++
  if (userData.rotaryId) completedFields++
  if (userData.rotaryDistrict) completedFields++
  if (userData.joiningDate) completedFields++
  if (userData.rotaryClub) completedFields++

  // Optional Contact Information
  if (userData.phonePersonal || userData.phoneWork) completedFields++

  // Service & Leadership (Optional but valuable)
  if (Array.isArray(userData.committees) && userData.committees.length > 0) completedFields++
  if (Array.isArray(userData.leadershipRoles) && userData.leadershipRoles.length > 0)
    completedFields++
  if (Array.isArray(userData.serviceProjects) && userData.serviceProjects.length > 0)
    completedFields++
  if (Array.isArray(userData.awards) && userData.awards.length > 0) completedFields++

  // Privacy Settings

  // Communication Preferences
  if (userData.communicationPreferences) completedFields++

  // Legacy/Additional Info
  if (userData.sponsorName) completedFields++

  // Calculate percentage (0-100)
  return Math.round((completedFields / totalFields) * 100)
}

function isValidRotaryId(rotaryId: string): boolean {
  // Basic validation - can be enhanced with actual Rotary ID format
  return (
    Boolean(rotaryId) &&
    rotaryId.length >= 3 &&
    rotaryId.length <= 20 &&
    /^[A-Za-z0-9\-_\s]+$/.test(rotaryId)
  )
}
