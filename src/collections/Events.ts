import type { CollectionConfig } from 'payload'
import { authenticated } from '../access/authenticated'
import { authenticatedOrPublished } from '../access/authenticatedOrPublished'
import { slugField } from '@/fields/slug'
import { AnalyticsHooks } from '../utilities/eventAnalytics'



export const Events: CollectionConfig = {
  slug: 'events',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  admin: {
    defaultColumns: ['title', 'eventDate', 'eventType', 'capacity', 'attendeeCount', 'registrationStatus', 'status', 'updatedAt'],
    useAsTitle: 'title',
    group: 'Events',
    description: 'Manage events and track registrations',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      localized: true,
    },
    {
      name: 'eventType',
      type: 'select',
      required: true,
      options: [
        { label: 'Meeting', value: 'meeting' },
        { label: 'Workshop', value: 'workshop' },
        { label: 'Seminar', value: 'seminar' },
        { label: 'Conference', value: 'conference' },
        { label: 'Social Event', value: 'social' },
        { label: 'Fundraiser', value: 'fundraiser' },
        { label: 'Community Service', value: 'service' },
        { label: 'Other', value: 'other' },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
        { label: 'Cancelled', value: 'cancelled' },
        { label: 'Completed', value: 'completed' },
      ],
    },
    {
      name: 'eventDate',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'description',
      type: 'textarea',
      localized: true,
      required: true,
    },
    {
      name: 'location',
      type: 'text',
      localized: true,
      required: true,
    },
    {
      name: 'capacity',
      type: 'number',
      admin: {
        description: 'Maximum number of attendees (leave empty for unlimited)',
      },
    },
    {
      name: 'registrationRequired',
      type: 'checkbox',
      defaultValue: false,
    },
    // Registration Form Configuration
    {
      name: 'registrationForm',
      type: 'group',
      admin: {
        description: 'Configure the registration form for this event',
        condition: (data) => data?.registrationRequired === true,
      },
      fields: [
        {
          name: 'formFields',
          type: 'array',
          label: 'Custom Registration Fields',
          fields: [
            {
              name: 'fieldName',
              type: 'text',
              required: true,
              admin: {
                description: 'Internal field name (e.g., "dietary_restrictions")',
              },
            },
            {
              name: 'label',
              type: 'text',
              localized: true,
              required: true,
              admin: {
                description: 'Field label displayed to users',
              },
            },
            {
              name: 'type',
              type: 'select',
              required: true,
              options: [
                { label: 'Text', value: 'text' },
                { label: 'Textarea', value: 'textarea' },
                { label: 'Email', value: 'email' },
                { label: 'Phone', value: 'phone' },
                { label: 'Select', value: 'select' },
                { label: 'Checkbox', value: 'checkbox' },
                { label: 'Radio', value: 'radio' },
                { label: 'Date', value: 'date' },
                { label: 'File Upload', value: 'file' },
              ],
            },
            {
              name: 'required',
              type: 'checkbox',
              defaultValue: false,
              label: 'Required Field',
            },
            {
              name: 'placeholder',
              type: 'text',
              localized: true,
              admin: {
                description: 'Placeholder text for the field',
              },
            },
            {
              name: 'options',
              type: 'array',
              fields: [
                {
                  name: 'label',
                  type: 'text',
                  localized: true,
                  required: true,
                },
                {
                  name: 'value',
                  type: 'text',
                  required: true,
                },
              ],
              admin: {
                condition: (data) => ['select', 'radio'].includes(data?.type),
                description: 'Options for select/radio fields',
              },
            },
            {
              name: 'helpText',
              type: 'text',
              localized: true,
              admin: {
                description: 'Help text displayed below the field',
              },
            },
          ],
        },
        {
          name: 'maxRegistrations',
          type: 'number',
          admin: {
            description: 'Maximum number of registrations allowed (leave empty for unlimited)',
          },
        },
        {
          name: 'registrationDeadline',
          type: 'date',
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
            },
            description: 'Deadline for event registration',
          },
        },
        {
          name: 'allowWaitlist',
          type: 'checkbox',
          defaultValue: false,
          label: 'Allow Waitlist',
          admin: {
            description: 'Allow registrations beyond capacity limit to join waitlist',
          },
        },
        {
          name: 'confirmationMessage',
          type: 'textarea',
          localized: true,
          admin: {
            description: 'Message shown after successful registration',
          },
        },
      ],
    },
    // Organizer Contact Information
    {
      name: 'organizer',
      type: 'group',
      admin: {
        description: 'Event organizer contact information for notifications',
      },
      fields: [
        {
          name: 'name',
          type: 'text',
          localized: true,
          admin: {
            description: 'Organizer name',
          },
        },
        {
          name: 'email',
          type: 'email',
          required: true,
          admin: {
            description: 'Organizer email for registration notifications',
          },
        },
        {
          name: 'phone',
          type: 'text',
          admin: {
            description: 'Organizer phone number (optional)',
          },
        },
        {
          name: 'sendNotifications',
          type: 'checkbox',
          defaultValue: true,
          label: 'Send registration notifications',
          admin: {
            description: 'Send email notifications for new registrations',
          },
        },
      ],
    },
    // Attendee Management
    {
      name: 'attendees',
      type: 'array',
      admin: {
        description: 'Event Registration Management - View and manage event registrations',
        readOnly: true,
        initCollapsed: false,
      },
      fields: [
        {
          name: 'userId',
          type: 'text',
          admin: {
            description: 'User ID of the registrant',
          },
        },
        {
          name: 'userEmail',
          type: 'email',
          admin: {
            description: 'Email address of the registrant',
          },
        },
        {
          name: 'userName',
          type: 'text',
          admin: {
            description: 'Name of the registrant',
          },
        },
        {
          name: 'registrationDate',
          type: 'date',
          required: true,
          admin: {
            description: 'When the registration was submitted',
          },
        },
        {
          name: 'status',
          type: 'select',
          required: true,
          defaultValue: 'registered',
          options: [
            { label: 'Registered', value: 'registered' },
            { label: 'Waitlisted', value: 'waitlisted' },
            { label: 'Cancelled', value: 'cancelled' },
            { label: 'Attended', value: 'attended' },
          ],
        },
        {
          name: 'customFields',
          type: 'json',
          admin: {
            description: 'Custom registration field responses',
          },
        },
        {
          name: 'uploadedFiles',
          type: 'array',
          fields: [
            {
              name: 'fieldName',
              type: 'text',
              required: true,
            },
            {
              name: 'file',
              type: 'upload',
              relationTo: 'media',
              required: true,
            },
          ],
          admin: {
            description: 'Files uploaded during registration',
          },
        },
      ],
    },
    ...slugField(),
  ],
  versions: {
    drafts: {
      autosave: {
        interval: 100,
      },
      schedulePublish: true,
    },
    maxPerDoc: 50,
  },

  // Integrate analytics cache invalidation hooks
  hooks: {
    afterChange: [
      AnalyticsHooks.afterChange
    ],
    afterDelete: [
      AnalyticsHooks.afterChange
    ]
  },
}
