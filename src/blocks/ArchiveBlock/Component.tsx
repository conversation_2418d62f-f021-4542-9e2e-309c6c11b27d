import type { Post, Event, ArchiveBlock as ArchiveBlockProps } from '@/payload-types'

import payloadConfig from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import RichText from '@/components/RichText'

import { CollectionArchive } from '@/components/CollectionArchive'
import { EventArchive } from '@/components/EventArchive'
import { EventCardData } from '@/components/EventCard'

export const ArchiveBlock: React.FC<
  ArchiveBlockProps & {
    id?: string
  }
> = async (props) => {
  const { id, categories, introContent, limit: limitFromProps, populateBy, selectedDocs } = props
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const relationTo = (props as any).relationTo || 'posts'

  const limit = limitFromProps || 3
  const collectionToUse = relationTo || 'posts'

  let posts: Post[] = []
  let events: EventCardData[] = []

  if (populateBy === 'collection') {
    const payload = await getPayload({ config: payloadConfig })

    const flattenedCategories = categories?.map((category) => {
      if (typeof category === 'object') return category.id
      else return category
    })

    if (collectionToUse === 'events') {
      const fetchedEvents = await payload.find({
        collection: 'events',
        depth: 1,
        limit,
        // Only show published events
        where: {
          status: {
            equals: 'published',
          },
          ...(flattenedCategories && flattenedCategories.length > 0
            ? {
                // For events, we might filter by categories differently in the future
                // For now, just show all published events
              }
            : {}),
        },
      })

      events = (fetchedEvents.docs as Event[]).map(event => ({
        id: event.id,
        slug: event.slug,
        title: event.title,
        eventDate: event.eventDate,
        location: event.location,
        eventType: event.eventType,
        description: event.description,
        capacity: event.capacity,
        attendees: event.attendees,
        status: event.status,
        registrationRequired: event.registrationRequired,
      }))
    } else {
      // Default to posts
      const fetchedPosts = await payload.find({
        collection: 'posts',
        depth: 1,
        limit,
        ...(flattenedCategories && flattenedCategories.length > 0
          ? {
              where: {
                categories: {
                  in: flattenedCategories,
                },
              },
            }
          : {}),
      })

      posts = fetchedPosts.docs
    }
  } else {
    if (selectedDocs?.length) {
      if (collectionToUse === 'events') {
        const filteredSelectedEvents = selectedDocs
          .map((doc) => {
            if (typeof doc.value === 'object' && doc.value) {
              const event = doc.value as unknown as Event
              return {
                id: event.id,
                slug: event.slug,
                title: event.title,
                eventDate: event.eventDate,
                location: event.location,
                eventType: event.eventType,
                description: event.description,
                capacity: event.capacity,
                attendees: event.attendees,
                status: event.status,
                registrationRequired: event.registrationRequired,
              } as EventCardData
            }
            return null
          })
          .filter((event): event is EventCardData => event !== null)

        events = filteredSelectedEvents
      } else {
        const filteredSelectedPosts = selectedDocs.map((post) => {
          if (typeof post.value === 'object') return post.value as Post
          return null
        }).filter(Boolean) as Post[]

        posts = filteredSelectedPosts
      }
    }
  }

  return (
    <div className="my-16" id={`block-${id}`}>
      {introContent && (
        <div className="container mb-16">
          <RichText className="ms-0 max-w-3xl" data={introContent} enableGutter={false} />
        </div>
      )}
      {collectionToUse === 'events' ? (
        <EventArchive events={events} showEventType={false} showFilters={false} />
      ) : (
        <CollectionArchive posts={posts} />
      )}
    </div>
  )
}
