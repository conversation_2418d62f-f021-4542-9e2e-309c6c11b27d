/**
 * Extended User Types for Rotary Club Tunis Doyen CMS
 * Defines comprehensive user profile structure with Rotary-specific fields
 */

export interface PrivacySettings {
  isPublicProfile: boolean      // Show profile in public member directory
  shareContactDetails: boolean  // Allow sharing phone/email with other members
  sharePhotos: boolean          // Allow photos in club publications/events
  marketingConsent: boolean     // Consent for club promotional emails
  dataSharingConsent?: boolean  // Consent for sharing with district partners
}

export interface CommunicationPreferences {
  emailNotifications: boolean        // Event updates and announcements
  newsletterSubscription: boolean    // Monthly club newsletter
  meetingReminders: boolean          // Event attendance reminders
  committeeUpdates: boolean          // Committee activity notifications
}

export interface CommitteeMembership {
  committee: 'president-elect' | 'director-nominating' | 'community-service' | 'vocational-service' | 'international-service' | 'youth-service' | 'membership' | 'club-service'
  startDate: Date
  endDate?: Date
  isActive: boolean
}

export interface LeadershipRole {
  position: LocalizedString    // Position title (localizable)
  scope: 'club' | 'district' | 'international'
  startYear: number
  endYear?: number
  isCurrent: boolean
}

export interface ServiceProject {
  projectName: string
  projectType: 'community' | 'international' | 'vocational' | 'youth'
  year: number
  description?: string
  hours?: number
  certificates?: UploadedFile[]
}

export interface Award {
  awardName: string
  awardType: 'club' | 'district' | 'international'
  year: number
  description?: string
  certificate?: UploadedFile
}

export interface LocalizationRecord {
  en?: string
  fr?: string
  ar?: string
}

export type LocalizedString = string | LocalizationRecord

export interface ExtendedUser {
  // Core Payload CMS Auth Fields
  id: string
  name: LocalizedString
  email: string
  createdAt: Date
  updatedAt: Date

  // Basic Contact Information
  phonePersonal?: string         // Personal phone for club communications
  phoneWork?: string             // Work phone (optional)

  // Professional Information
  classification: LocalizedString  // Professional classification (Doctor, Engineer, etc.)
  joiningDate: Date               // Date joined Rotary Club

  // Rotary Membership Details
  rotaryId: string               // Rotary International Member ID
  rotaryDistrict: '1930' | '1890' | 'other'  // District affiliation
  rotaryClub: string             // Local club name/affiliation
  sponsorName?: string           // Member who sponsored this member's induction

  // Service & Leadership
  committees: CommitteeMembership[]  // Committee memberships
  leadershipRoles: LeadershipRole[]  // Leadership positions held
  serviceProjects: ServiceProject[]  // Community service projects participated in
  awards: Award[]                 // Certificates, awards, recognitions

  // Privacy & Consent Controls
  privacySettings: PrivacySettings
  communicationPreferences: CommunicationPreferences

  // System Fields (Managed by CMS)
  profileCompletion?: number      // Percentage of profile completed (0-100)
  lastLogin?: Date               // Last authenticated login
  passwordResetAttempts?: number // Security tracking
}

// Form submission and validation types
export interface UserRegistrationData {
  name: string
  email: string
  phonePersonal?: string
  classification: string
  rotaryId: string
  rotaryDistrict: '1930' | '1890' | 'other'
  sponsorName?: string
  privacySettings?: Partial<PrivacySettings>
}

export interface UserProfileUpdate {
  name?: LocalizedString
  phonePersonal?: string
  phoneWork?: string
  classification?: LocalizedString
  committees?: CommitteeMembership[]
  leadershipRoles?: LeadershipRole[]
  privacySettings?: Partial<PrivacySettings>
  communicationPreferences?: Partial<CommunicationPreferences>
}

// Directory visibility types
export interface PublicUserProfile {
  id: string
  name: LocalizedString
  classification: LocalizedString
  rotaryId: string
  joiningDate: Date
  leadershipRoles: LeadershipRole[]
  profileImage?: UploadedFile
}

// Upload file type (references Payload Media collection)
export interface UploadedFile {
  id: string
  filename: string
  url: string
  mimeType: string
  filesize: number
}

// Helper types for form validation
export type UserFormStep = 'basic' | 'rotary' | 'privacy' | 'complete'

export interface FormValidationError {
  field: string
  message: LocalizedString
  code: string
}

// API response types
export interface UserApiResponse {
  success: boolean
  user?: ExtendedUser
  errors?: FormValidationError[]
  message?: LocalizedString
}

// Directory filter types
export interface DirectoryFilters {
  classification?: string
  rotaryDistrict?: string
  committees?: string[]
  leadershipScope?: 'club' | 'district' | 'international'
  joiningYear?: number
}

// Search and pagination types
export interface DirectorySearchParams {
  query?: string        // General search term
  filters?: DirectoryFilters
  page?: number
  limit?: number
  sortBy?: 'name' | 'classification' | 'joiningDate' | 'leadership'
  sortOrder?: 'asc' | 'desc'
}

export interface DirectorySearchResults {
  users: PublicUserProfile[]
  total: number
  page: number
  totalPages: number
  hasMore: boolean
}
