/**
 * TypeScript definitions for Event Analytics
 * Defines data structures used throughout the analytics system
 */

export interface EventAnalyticsData {
  overview: {
    totalEvents: number
    totalRegistrations: number
    averageCapacityUtilization: number
    topEventType: string
  }
  trends: {
    dailyRegistrations: Array<{ date: string, count: number }>
    eventTypeBreakdown: Array<{ type: string, count: number, percentage: number }>
    capacityUtilization: number
  }
  filters: {
    dateRange: { start: Date, end: Date }
    eventTypes?: string[]
    status?: string[]
  }
}

export interface EventRegistrationStats {
  total: number
  registered: number
  waitlisted: number
  cancelled: number
  attended: number
}

export interface EventAnalyticsResult {
  eventId: string
  eventTitle: string
  analytics: {
    totalRegistrations: number
    attendanceRate: number
    registrationTrend: Array<{ date: string, count: number }>
    statusBreakdown: EventRegistrationStats
  }
}

export type DateRange = { start: Date, end: Date }

export type EventStatus = 'draft' | 'published' | 'cancelled' | 'completed'

export type EventType = 'meeting' | 'workshop' | 'seminar' | 'conference' | 'social' | 'fundraiser' | 'service' | 'other'
