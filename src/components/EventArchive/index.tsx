'use client'
import { cn } from '@/utilities/ui'
import React, { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

import { EventCard, EventCardData } from '@/components/EventCard'

export type EventArchiveProps = {
  events: EventCardData[]
  showEventType?: boolean
  showFilters?: boolean
}

export const EventArchive: React.FC<EventArchiveProps> = (props) => {
  const { events, showEventType = true, showFilters = true } = props

  const [activeFilter, setActiveFilter] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState<string>('')

  // Get unique event types for filtering
  const eventTypes = Array.from(new Set(events?.map(event => event.eventType)?.filter(Boolean)))

  const filteredEvents = useMemo(() => {
    let filtered = events || []

    if (activeFilter !== 'all') {
      filtered = filtered.filter(event => event.eventType === activeFilter)
    }

    if (searchQuery) {
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filtered
  }, [events, activeFilter, searchQuery])

  // Filter events based on selected type
  const handleFilterChange = (filterType: string) => {
    setActiveFilter(filterType)
  }

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value)
  }

  return (
    <div className="mb-16">
      {/* Filters and Search */}
      {showFilters && (
        <div className="container mb-8">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleFilterChange('all')}
                className="mb-2"
              >
                All Events ({events?.length || 0})
              </Button>
              {eventTypes.map(type => {
                const count = events?.filter(event => event.eventType === type).length || 0
                const typeLabel = type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')
                return (
                  <Button
                    key={type}
                    variant={activeFilter === type ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange(type)}
                    className="mb-2"
                  >
                    {typeLabel} ({count})
                  </Button>
                )
              })}
            </div>
            <div className="w-full sm:w-auto sm:ml-auto">
              <Input
                type="text"
                placeholder="Search by event title..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="max-w-sm"
              />
            </div>
          </div>
        </div>
      )}

      {/* Events Grid */}
      <div className={cn('container')}>
        {filteredEvents.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
            {filteredEvents.map((event, index) => {
              if (typeof event === 'object' && event !== null && (event.id || event.slug)) {
                return (
                  <EventCard
                    key={event.id || event.slug || index}
                    className="h-full"
                    doc={event}
                    relationTo="events"
                    showEventType={showEventType}
                  />
                )
              }
              return <div key={index} />
            })}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? 'No events match your search' : (activeFilter === 'all' ? 'No events found' : 'No events in this category')}
              </h3>
              <p className="text-gray-500">
                {searchQuery ? 'Try a different search term.' : (activeFilter === 'all'
                  ? 'There are currently no events scheduled.'
                  : `There are no events of type "${activeFilter}" currently scheduled.`)}
              </p>
              {activeFilter !== 'all' && (
                <button
                  onClick={() => handleFilterChange('all')}
                  className="mt-4 text-blue-600 hover:text-blue-500 font-medium"
                >
                  View all events →
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
