'use client'

import { useState } from 'react'
import { DateRange } from '@/types/eventAnalytics'

interface DateRangeFilterProps {
  onDateRangeChange: (dateRange: DateRange) => void
  currentRange?: DateRange
}

export default function DateRangeFilter({ onDateRangeChange, currentRange }: DateRangeFilterProps) {
  const [quickSelection, setQuickSelection] = useState<string>('30')

  const handleQuickSelect = (days: string) => {
    setQuickSelection(days)
    const endDate = new Date()
    const startDate = new Date()

    if (days === 'all') {
      // Default to last year if "all" is selected
      startDate.setFullYear(startDate.getFullYear() - 1)
    } else {
      startDate.setDate(startDate.getDate() - parseInt(days))
    }

    onDateRangeChange({ start: startDate, end: endDate })
  }

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const startDate = new Date(e.target.value)
    if (currentRange?.end) {
      onDateRangeChange({
        start: startDate,
        end: currentRange.end
      })
    }
  }

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const endDate = new Date(e.target.value)
    if (currentRange?.start) {
      onDateRangeChange({
        start: currentRange.start,
        end: endDate
      })
    }
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border mb-6">
      <div className="flex flex-col md:flex-row md:items-end gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Date Range Filter
          </label>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleQuickSelect('7')}
              className={`px-3 py-1 text-sm border rounded-md transition-colors ${
                quickSelection === '7'
                  ? 'bg-rotary-blue text-white border-rotary-blue'
                  : 'bg-white text-gray-600 border-gray-300 hover:border-gray-400'
              }`}
            >
              Last 7 days
            </button>
            <button
              onClick={() => handleQuickSelect('30')}
              className={`px-3 py-1 text-sm border rounded-md transition-colors ${
                quickSelection === '30'
                  ? 'bg-rotary-blue text-white border-rotary-blue'
                  : 'bg-white text-gray-600 border-gray-300 hover:border-gray-400'
              }`}
            >
              Last 30 days
            </button>
            <button
              onClick={() => handleQuickSelect('90')}
              className={`px-3 py-1 text-sm border rounded-md transition-colors ${
                quickSelection === '90'
                  ? 'bg-rotary-blue text-white border-rotary-blue'
                  : 'bg-white text-gray-600 border-gray-300 hover:border-gray-400'
              }`}
            >
              Last 3 months
            </button>
            <button
              onClick={() => handleQuickSelect('365')}
              className={`px-3 py-1 text-sm border rounded-md transition-colors ${
                quickSelection === '365'
                  ? 'bg-rotary-blue text-white border-rotary-blue'
                  : 'bg-white text-gray-600 border-gray-300 hover:border-gray-400'
              }`}
            >
              Last year
            </button>
            <button
              onClick={() => handleQuickSelect('all')}
              className={`px-3 py-1 text-sm border rounded-md transition-colors ${
                quickSelection === 'all'
                  ? 'bg-rotary-blue text-white border-rotary-blue'
                  : 'bg-white text-gray-600 border-gray-300 hover:border-gray-400'
              }`}
            >
              All time
            </button>
          </div>
        </div>

        <div className="flex gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              From
            </label>
            <input
              type="date"
              value={currentRange?.start.toISOString().split('T')[0] || ''}
              onChange={handleStartDateChange}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rotary-blue focus:border-transparent"
            />
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              To
            </label>
            <input
              type="date"
              value={currentRange?.end.toISOString().split('T')[0] || ''}
              onChange={handleEndDateChange}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rotary-blue focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {currentRange && (
        <div className="mt-4 text-sm text-gray-600">
          <span>Current range: </span>
          <span className="font-medium">
            {currentRange.start.toLocaleDateString()} - {currentRange.end.toLocaleDateString()}
          </span>
          <span className="ml-2 text-xs">
            ({Math.ceil((currentRange.end.getTime() - currentRange.start.getTime()) / (1000 * 60 * 60 * 24))} days)
          </span>
        </div>
      )}
    </div>
  )
}
