'use client'

import { useState, useEffect } from 'react'
import { EventAnalyticsData } from '@/types/eventAnalytics'
import MetricsCards from './MetricsCards'
import DateRangeFilter from './Filters/DateRangeFilter'

/**
 * Event Analytics Dashboard
 * Main dashboard component displaying event metrics and visualizations
 */

interface DateRange {
  start: Date
  end: Date
}

interface AnalyticsFilters {
  dateRange: DateRange
  eventTypes?: string[]
}

export interface EventAnalyticsDashboardProps {
  /** Initial date range if provided */
  initialDateRange?: DateRange
  /** Allow external date range control */
  onDateRangeChange?: (range: DateRange) => void
}

export default function EventAnalyticsDashboard({
  initialDateRange,
  onDateRangeChange,
}: EventAnalyticsDashboardProps) {
  const [data, setData] = useState<EventAnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<AnalyticsFilters>({
    dateRange: initialDateRange || {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      end: new Date(),
    },
  })

  const fetchAnalyticsData = async (filters: AnalyticsFilters) => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        metric: 'overview',
        startDate: filters.dateRange.start.toISOString(),
        endDate: filters.dateRange.end.toISOString(),
      })

      if (filters.eventTypes?.length) {
        params.append('eventTypes', filters.eventTypes.join(','))
      }

      const response = await fetch(`/api/events/analytics?${params}`)
      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.status}`)
      }

      const analyticsData = await response.json()
      setData(analyticsData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  // Fetch data when filters change
  useEffect(() => {
    fetchAnalyticsData(filters)
  }, [filters])

  // Update external state when date range changes
  useEffect(() => {
    if (onDateRangeChange) {
      onDateRangeChange(filters.dateRange)
    }
  }, [filters.dateRange, onDateRangeChange])

  const handleDateRangeChange = (newRange: DateRange) => {
    setFilters((prev) => ({
      ...prev,
      dateRange: newRange,
    }))
  }

  const _handleEventTypeFilter = (_eventTypes: string[]) => {
    // TODO: Implement event type filtering
    // setFilters((prev) => ({
    //   ...prev,
    //   eventTypes,
    // }))
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-6">
        <h3 className="text-sm font-semibold text-red-800 mb-2">Analytics Error</h3>
        <p className="text-sm text-red-700">{error}</p>
        <button
          onClick={() => fetchAnalyticsData(filters)}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Event Analytics</h2>
          <p className="text-sm text-gray-600">Track event performance and registration trends</p>
        </div>

        <DateRangeFilter currentRange={filters.dateRange} onDateRangeChange={handleDateRangeChange} />
      </div>

      {/* Metrics Cards */}
      <MetricsCards data={data?.overview || null} loading={loading} />

      {/* Additional Analytics Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        {/* Registration Trends Placeholder */}
        <div className="rounded-lg border border-gray-200 bg-white p-6">
          <h3 className="text-sm font-semibold text-gray-900 mb-4">Registration Trends</h3>
          {loading ? (
            <div className="h-64 bg-gray-100 animate-pulse rounded"></div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500 text-sm">
              📊 Chart visualization coming soon...
            </div>
          )}
        </div>

        {/* Event Type Breakdown Placeholder */}
        <div className="rounded-lg border border-gray-200 bg-white p-6">
          <h3 className="text-sm font-semibold text-gray-900 mb-4">Event Type Distribution</h3>
          {loading ? (
            <div className="h-64 bg-gray-100 animate-pulse rounded"></div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500 text-sm">
              🥧 Chart visualization coming soon...
            </div>
          )}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="flex items-center space-x-2 text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm">Loading analytics...</span>
          </div>
        </div>
      )}
    </div>
  )
}
