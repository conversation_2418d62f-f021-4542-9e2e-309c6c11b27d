'use client'

import { useEffect, useState } from 'react'
import { EventAnalyticsData } from '@/types/eventAnalytics'

interface MetricsCardsProps {
  data: EventAnalyticsData['overview'] | null
  loading: boolean
}

export default function MetricsCards({ data, loading }: MetricsCardsProps) {
  const [animatedData, setAnimatedData] = useState(data)

  useEffect(() => {
    setAnimatedData(data)
  }, [data])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-white rounded-lg p-6 shadow-sm border animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-16 mb-1"></div>
            <div className="h-3 bg-gray-100 rounded w-32"></div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      {/* Total Events */}
      <div className="bg-white rounded-lg p-6 shadow-sm border">
        <div className="text-sm font-medium text-gray-500 mb-2">
          Total Events
        </div>
        <div className="text-3xl font-bold text-rotary-blue mb-1">
          {animatedData?.totalEvents || 0}
        </div>
        <div className="text-xs text-gray-400">
          Completed events
        </div>
      </div>

      {/* Total Registrations */}
      <div className="bg-white rounded-lg p-6 shadow-sm border">
        <div className="text-sm font-medium text-gray-500 mb-2">
          Total Registrations
        </div>
        <div className="text-3xl font-bold text-green-600 mb-1">
          {animatedData?.totalRegistrations || 0}
        </div>
        <div className="text-xs text-gray-400">
          Across all events
        </div>
      </div>

      {/* Average Capacity Utilization */}
      <div className="bg-white rounded-lg p-6 shadow-sm border">
        <div className="text-sm font-medium text-gray-500 mb-2">
          Capacity Utilization
        </div>
        <div className="text-3xl font-bold text-orange-600 mb-1">
          {animatedData?.averageCapacityUtilization || 0}%
        </div>
        <div className="text-xs text-gray-400">
          Average rate
        </div>
      </div>

      {/* Top Event Type */}
      <div className="bg-white rounded-lg p-6 shadow-sm border">
        <div className="text-sm font-medium text-gray-500 mb-2">
          Popular Event Type
        </div>
        <div className="text-3xl font-bold text-purple-600 mb-1">
          {animatedData?.topEventType || 'None'}
        </div>
        <div className="text-xs text-gray-400">
          Most requested
        </div>
      </div>
    </div>
  )
}
