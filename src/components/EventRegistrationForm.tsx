'use client'

import React, { useState } from 'react'
import { Event } from '@/payload-types'

interface EventRegistrationFormProps {
  event: Event
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onRegistrationSuccess?: (data: any) => void
  onRegistrationError?: (error: string) => void
}

interface FormField {
  fieldName: string
  label: string
  type: 'text' | 'textarea' | 'email' | 'phone' | 'select' | 'checkbox' | 'radio' | 'date' | 'file'
  required?: boolean | null | undefined
  placeholder?: string | null | undefined
  options?: Array<{ label: string; value: string }> | null | undefined
  helpText?: string | null | undefined
}

interface RegistrationFormData {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

export const EventRegistrationForm: React.FC<EventRegistrationFormProps> = ({
  event,
  onRegistrationSuccess,
  onRegistrationError,
}) => {
  const [formData, setFormData] = useState<RegistrationFormData>({
    eventId: event.id,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  // Get custom form fields from event configuration
  const customFields: FormField[] = (event.registrationForm?.formFields || []) as FormField[]

  // Create all form fields (standard + custom) for dynamic rendering
  const allFields: Array<{ fieldName: string; label: string; type: string; required: boolean; isStandard: boolean }> = [
    { fieldName: 'name', label: 'Full Name', type: 'text', required: true, isStandard: true },
    { fieldName: 'email', label: 'Email Address', type: 'email', required: true, isStandard: true },
    { fieldName: 'phone', label: 'Phone Number', type: 'phone', required: false, isStandard: true },
    ...customFields.map(field => ({
      fieldName: field.fieldName,
      label: field.label,
      type: field.type,
      required: !!field.required,
      isStandard: false,
    })),
  ]

  const handleInputChange = (fieldName: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value,
    }))
    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: '',
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    // Validate all fields
    allFields.forEach(field => {
      if (field.required && !formData[field.fieldName]) {
        newErrors[field.fieldName] = `${field.label} is required`
      }
      if (field.type === 'email' && formData[field.fieldName] && !/\S+@\S+\.\S+/.test(formData[field.fieldName])) {
        newErrors[field.fieldName] = 'Email is invalid'
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      onRegistrationError?.('Please correct the errors below')
      return
    }

    setIsSubmitting(true)

    try {
      // Use FormData to properly handle files and text data
      const formDataToSend = new FormData()
      formDataToSend.append('eventId', event.id)
      formDataToSend.append('formData', JSON.stringify(formData))

      // Append file if it exists
      if (formData.file) {
        formDataToSend.append('file', formData.file)
      }

      const response = await fetch('/api/event-registration', {
        method: 'POST',
        body: formDataToSend, // Use FormData instead of JSON
      })

      const result = await response.json()

      if (response.ok && result.success) {
        onRegistrationSuccess?.(result)
      } else {
        onRegistrationError?.(result.error || 'Registration failed')
      }
    } catch {
      onRegistrationError?.('Network error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (field: FormField) => {
    const { fieldName, label, type, required, placeholder, options, helpText } = field
    const value = formData[fieldName]
    const error = errors[fieldName]

    return (
      <div key={fieldName} className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>

        {type === 'textarea' && (
          <textarea
            value={value || ''}
            onChange={(e) => handleInputChange(fieldName, e.target.value)}
            placeholder={placeholder ?? undefined}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? 'border-red-500' : 'border-gray-300'
            }`}
            rows={4}
          />
        )}

        {type === 'select' && (
          <select
            value={value || ''}
            onChange={(e) => handleInputChange(fieldName, e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select {label.toLowerCase()}</option>
            {options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )}

        {type === 'checkbox' && (
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={value || false}
              onChange={(e) => handleInputChange(fieldName, e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">
              {label}
            </label>
          </div>
        )}

        {type === 'radio' && options && (
          <div className="space-y-2">
            {options.map(option => (
              <div key={option.value} className="flex items-center">
                <input
                  type="radio"
                  value={option.value}
                  checked={value === option.value}
                  onChange={(e) => handleInputChange(fieldName, e.target.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        )}

        {type === 'file' && (
          <input
            type="file"
            onChange={(e) => handleInputChange(fieldName, e.target.files?.[0])}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? 'border-red-500' : 'border-gray-300'
            }`}
          />
        )}

        {(type === 'text' || type === 'email' || type === 'phone' || type === 'date') && (
          <input
            type={type === 'phone' ? 'tel' : type}
            value={value || ''}
            onChange={(e) => handleInputChange(fieldName, e.target.value)}
            placeholder={placeholder ?? undefined}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? 'border-red-500' : 'border-gray-300'
            }`}
          />
        )}

        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}

        {helpText && (
          <p className="mt-1 text-sm text-gray-500">{helpText}</p>
        )}
      </div>
    )
  }

  if (!event.registrationRequired) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-md p-4">
        <p className="text-green-800">
          Registration is not required for this event. You can attend without registering.
        </p>
      </div>
    )
  }

  return (
    <div className="bg-white shadow-lg rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Register for {event.title}
        </h2>
        <p className="text-gray-600">
          {event.eventDate && new Date(event.eventDate).toLocaleDateString()}
          {event.location && ` • ${event.location}`}
        </p>
      </div>

      <form onSubmit={handleSubmit} action="/api/event-registration" method="POST" className="space-y-6">
        {/* Dynamic Fields */}
        {allFields.map(field => {
          const { fieldName, label, type, required } = field
          const value = formData[fieldName]
          const error = errors[fieldName]

          return (
            <div key={fieldName} className={field.isStandard ? 'grid grid-cols-1 md:grid-cols-2 gap-4' : ''}>
              {field.isStandard && field.fieldName === 'name' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {label} {required && <span className="text-red-500">*</span>}
                  </label>
                  <input
                    type={type}
                    value={value || ''}
                    onChange={(e) => handleInputChange(fieldName, e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      error ? 'border-red-500' : 'border-gray-300'
                    }`}
                    required={required}
                  />
                  {error && (
                    <p className="mt-1 text-sm text-red-600">{error}</p>
                  )}
                </div>
              )}

              {field.isStandard && field.fieldName === 'email' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {label} {required && <span className="text-red-500">*</span>}
                  </label>
                  <input
                    type={type}
                    value={value || ''}
                    onChange={(e) => handleInputChange(fieldName, e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      error ? 'border-red-500' : 'border-gray-300'
                    }`}
                    required={required}
                  />
                  {error && (
                    <p className="mt-1 text-sm text-red-600">{error}</p>
                  )}
                </div>
              )}

              {field.isStandard && field.fieldName === 'phone' && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {label}
                  </label>
                  <input
                    type="tel"
                    value={value || ''}
                    onChange={(e) => handleInputChange(fieldName, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              {!field.isStandard && renderField(customFields.find(cf => cf.fieldName === field.fieldName)!)}
            </div>
          )
        })}

        {/* Confirmation Message */}
        {event.registrationForm?.confirmationMessage && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <p className="text-blue-800 text-sm">
              {event.registrationForm.confirmationMessage}
            </p>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Registering...' : 'Register for Event'}
          </button>
        </div>
      </form>
    </div>
  )
}