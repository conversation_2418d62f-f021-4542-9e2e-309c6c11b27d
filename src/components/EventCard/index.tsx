'use client'
import { cn } from '@/utilities/ui'
import useClickableCard from '@/utilities/useClickableCard'
import Link from 'next/link'
import React from 'react'

import type { Event } from '@/payload-types'

export type EventCardData = Pick<Event, 'id' | 'slug' | 'title' | 'eventDate' | 'location' | 'eventType' | 'description' | 'capacity' | 'attendees' | 'status' | 'registrationRequired'>

export const EventCard: React.FC<{
  alignItems?: 'center'
  className?: string
  doc?: EventCardData
  relationTo?: 'events'
  showEventType?: boolean
  title?: string
}> = (props) => {
  const { card, link } = useClickableCard({})
  const { className, doc, relationTo, showEventType, title: titleFromProps } = props

  const {
    slug,
    title,
    eventDate,
    location,
    eventType,
    description,
    capacity,
    attendees,
    status,
    registrationRequired
  } = doc || {}

  const titleToUse = titleFromProps || title
  const sanitizedDescription = description?.replace(/\s/g, ' ')?.slice(0, 150) + '...'

  const href = `/${relationTo}/${slug}`

  // Calculate registration info
  const attendeeCount = attendees?.length || 0
  const isFull = capacity && attendeeCount >= capacity
  const isPast = eventDate && new Date(eventDate) < new Date()

  // Format date
  const formattedEventDate = eventDate ? new Date(eventDate).toLocaleDateString('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }) : 'TBD'

  // Format location
  const displayLocation = location || 'TBD'

  // Event type colors
  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'meeting': return 'bg-blue-100 text-blue-800'
      case 'workshop': return 'bg-green-100 text-green-800'
      case 'seminar': return 'bg-purple-100 text-purple-800'
      case 'conference': return 'bg-orange-100 text-orange-800'
      case 'social': return 'bg-pink-100 text-pink-800'
      case 'fundraiser': return 'bg-red-100 text-red-800'
      case 'service': return 'bg-teal-100 text-teal-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getEventTypeLabel = (type: string) => {
    switch (type) {
      case 'meeting': return 'Meeting'
      case 'workshop': return 'Workshop'
      case 'seminar': return 'Seminar'
      case 'conference': return 'Conference'
      case 'social': return 'Social Event'
      case 'fundraiser': return 'Fundraiser'
      case 'service': return 'Community Service'
      default: return 'Other'
    }
  }

  return (
    <article
      className={cn(
        'border border-border rounded-lg overflow-hidden bg-card hover:cursor-pointer transition-all duration-200 hover:shadow-lg',
        isPast && 'opacity-60',
        className,
      )}
      ref={card.ref}
    >
      <div className="relative w-full bg-linear-to-br from-blue-50 to-indigo-50 p-4 sm:p-6">
        {/* Status indicators */}
        <div className="absolute top-4 right-4 flex flex-col gap-1">
          {showEventType && eventType && (
            <span className={cn(
              'px-2 py-1 rounded-full text-xs font-medium',
              getEventTypeColor(eventType)
            )}>
              {getEventTypeLabel(eventType)}
            </span>
          )}
          {status && (
            <span className={cn(
              'px-2 py-1 rounded-full text-xs font-medium',
              status === 'published' ? 'bg-green-100 text-green-800' :
              status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
              status === 'cancelled' ? 'bg-red-100 text-red-800' :
              status === 'completed' ? 'bg-gray-100 text-gray-800' : 'bg-gray-100 text-gray-800'
            )}>
              {status === 'published' ? 'Active' : status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
          )}
        </div>

        {/* Placeholder for event image */}
        <div className="w-full h-32 bg-linear-to-br from-blue-100 to-indigo-100 rounded-lg mb-4 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-sm">Event Image</p>
          </div>
        </div>
      </div>

      <div className="p-4 sm:p-6">
        <div className="flex items-start justify-between gap-2 mb-3">
          {titleToUse && (
            <div className="prose flex-1">
              <h3 className="text-lg font-semibold mb-1">
                <Link className="not-prose text-gray-900 hover:text-blue-600 transition-colors" href={href} ref={link.ref}>
                  {titleToUse}
                </Link>
              </h3>
            </div>
          )}
        </div>

        {/* Event details */}
        <div className="space-y-2 text-sm text-gray-600 mb-4">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{formattedEventDate}</span>
          </div>

          <div className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>{displayLocation}</span>
          </div>

          {registrationRequired && (
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              {capacity ? (
                <span className={cn(
                  'font-medium',
                  isFull ? 'text-red-600' : 'text-green-600'
                )}>
                  {attendeeCount} / {capacity} registered
                </span>
              ) : (
                <span className="text-green-600 font-medium">Registration Open</span>
              )}
            </div>
          )}
        </div>

        {description && (
          <p className="text-gray-700 text-sm leading-relaxed mb-4">
            {sanitizedDescription}
          </p>
        )}

        {/* Call to action */}
        <div className="flex items-center justify-between">
          <Link
            href={href}
            className={cn(
              'inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors',
              isPast
                ? 'text-gray-500 bg-gray-100 cursor-default'
                : isFull && registrationRequired
                ? 'text-gray-500 bg-gray-100 cursor-default'
                : 'text-blue-700 bg-blue-50 hover:bg-blue-100'
            )}
            ref={!isPast ? link.ref : undefined}
          >
            {isPast ? 'Event Ended' :
             isFull && registrationRequired ? 'Event Full' : 'View Details'}
            {!isPast && (!isFull || !registrationRequired) && (
              <svg
                className="ml-2 -mr-1 w-4 h-4"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            )}
          </Link>
        </div>
      </div>
    </article>
  )
}