'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import type { Event } from '@/payload-types'

// Import types from new types.ts file
import { FormState, ValidationResult } from './types';

// Import sub-components
import { BasicInfoSection } from './components/BasicInfoSection';
import { ConsentSection } from './components/ConsentSection';
import { EventStatusDisplay } from './components/EventStatusDisplay';
import { ProfileLoadingDisplay } from './components/ProfileLoadingDisplay';
import { RegistrationSuccessDisplay } from './components/RegistrationSuccessDisplay';
import { get, post } from '@/utilities/apiClient'; // Import API client

// Types for enterprise-grade type safety
interface EventRegistrationFormProps {
  event: Event
  onRegistrationSuccess: (data: RegistrationSuccessData) => void
  onRegistrationError: (error: RegistrationError) => void
}

interface RegistrationSuccessData {
  eventId: string
  memberId: string
  attendeeInfo: AttendeeData
  timestamp: string
  registrationId: string
}

interface RegistrationError {
  code: string
  message: string
  details?: Record<string, unknown>
}

interface AttendeeData {
  email: string
  name: string
  phone: string
  classification: string
  rotaryId?: string
  consentDataProcessing: boolean
  marketingConsent: boolean
}

// Custom hook for debouncing values
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Member profile data from Task 1.2.4 API
interface MemberProfile {
  id: string
  name?: Record<string, string>
  classification?: Record<string, string>
  phonePersonal?: string
  phoneWork?: string
  rotaryId?: string
  privacySettings?: {
    marketingConsent?: boolean
  }
}



// Enterprise validation schema following Task 1.2.4 patterns
export const validationRules = {
  email: (email: string): boolean =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),

  phone: (phone: string): boolean =>
    /^\+?[\d\s\-\(\)]{10,20}$/.test(phone) && phone.replace(/\D/g, '').length >= 10,

  required: (value: string | boolean): boolean =>
    typeof value === 'string' ? (value.trim().length > 0) : false,

  rotaryId: (rotaryId: string): boolean => {
    if (!rotaryId.trim()) return true // Optional field
    return /^\d{1,8}$/.test(rotaryId.replace(/^RI/i, ''))
  }
}

// Enhanced form validation following Task 1.2.4 patterns
export const validateForm = (currentFormState: any, currentEvent: any): any => { // Modified to accept formState and event as arguments
  const errors: string[] = []
  const warnings: string[] = []

  // Required field validation
  if (!validationRules.required(currentFormState.name)) {
    errors.push('nameRequired')
  }

  if (!validationRules.required(currentFormState.email)) {
    errors.push('emailRequired')
  }
  else if (!validationRules.email(currentFormState.email)) {
    errors.push('emailInvalid')
  }

  if (!validationRules.required(currentFormState.phone)) {
    errors.push('phoneRequired')
  }
  else if (!validationRules.phone(currentFormState.phone)) {
    errors.push('phoneInvalid')
  }

  if (!validationRules.required(currentFormState.classification)) {
    errors.push('classificationRequired')
  }

  // Rotary ID validation (optional)
  if (currentFormState.rotaryId && !validationRules.rotaryId(currentFormState.rotaryId)) {
    errors.push('rotaryIdInvalid')
  }

  // GDPR compliance validation
  if (!currentFormState.consentDataProcessing) {
    errors.push('consentRequired')
  }

  // Enhanced duplicate registration check with case-insensitive comparison
  if (currentFormState.email && currentEvent.attendees?.some((a: any) => 
    a.userEmail && a.userEmail.toLowerCase() === currentFormState.email.toLowerCase()
  )) {
    errors.push('duplicateRegistration')
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

// Rate limiting state for client-side spam protection
class RegistrationRateLimiter {
  private static lastSubmission = 0
  private static readonly COOLDOWN_MS = 5000 // 5 second cooldown

  static canSubmit(): boolean {
    const now = Date.now()
    return (now - this.lastSubmission) >= this.COOLDOWN_MS
  }

  static recordSubmission(): void {
    this.lastSubmission = Date.now()
  }
}

export const EventRegistrationForm: React.FC<EventRegistrationFormProps> = ({
  event,
  onRegistrationSuccess,
  onRegistrationError
}) => {
  // Form state with enhanced validation
  const [formState, setFormState] = useState<FormState>({
    email: '',
    name: '',
    phone: '',
    classification: '',
    rotaryId: '',
    consentDataProcessing: false,
    marketingConsent: false,
  })

  // Member profile pre-population
  const [memberProfile, setMemberProfile] = useState<MemberProfile | null>(null)
  const [loadingProfile, setLoadingProfile] = useState(true)
  const [profileError, setProfileError] = useState<string>('')

  // Registration processing state
  const [submitting, setSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [registrationSuccess, setRegistrationSuccess] = useState(false)

  // Event-specific calculations (following Task 1.2.4 patterns)
  const eventCapacity = event.capacity || 0
  const currentAttendees = event.attendees?.length || 0
  const isEventFull = eventCapacity > 0 && currentAttendees >= eventCapacity
  const spotsRemaining = eventCapacity > 0 ? Math.max(0, eventCapacity - currentAttendees) : null
  
  // Enhanced capacity validation
  const canRegister = eventCapacity === 0 || currentAttendees < eventCapacity

  // Load member profile for pre-population
  useEffect(() => {
    let isMounted = true; // Flag to track if component is mounted

    const loadMemberProfile = async () => {
      try {
        const result = await get<{ user: MemberProfile }>('/api/users/profile');

        if (isMounted) { // Only update state if component is still mounted
          if (result.success && result.data) {
            const user = result.data.user;
            setMemberProfile(user);

            // Pre-populate form with member data (95%+ success target)
            // Handle multilingual fields properly
            const primaryName = user.name?.en || user.name?.fr || user.name?.ar || '';
            const primaryClassification = user.classification?.en || user.classification?.fr || user.classification?.ar || '';
            
            setFormState(prev => ({
              ...prev,
              name: primaryName,
              classification: primaryClassification,
              phone: user.phonePersonal || user.phoneWork || prev.phone,
              rotaryId: user.rotaryId || prev.rotaryId,
              marketingConsent: user.privacySettings?.marketingConsent !== undefined 
                ? user.privacySettings.marketingConsent 
                : prev.marketingConsent,
            }));
          } else if (result.code === 'UNAUTHORIZED') {
            // Handle unauthenticated users gracefully
            setProfileError('Please log in to pre-fill your information');
          } else {
            console.warn('Profile loading failed, proceeding with empty form:', result.error);
            setProfileError('Failed to load profile information. Please try again later or proceed with manual entry.');
          }
        }
      } catch (error) {
        if (isMounted) { // Only log if component is still mounted
          console.warn('Profile loading failed, proceeding with empty form:', error);
          setProfileError('Failed to load profile information. Please try again later or proceed with manual entry.');
          // Graceful degradation - form works without profile data
        }
      } finally {
        if (isMounted) { // Only update state if component is still mounted
          setLoadingProfile(false);
        }
      }
    };

    loadMemberProfile()

    return () => {
      isMounted = false; // Set flag to false when component unmounts
    };
  }, [])

  // Use the exported validateForm function

  // Real-time validation effect
  const debouncedFormState = useDebounce(formState, 300); // Debounce for 300ms

  useEffect(() => {
    if (Object.keys(debouncedFormState).some(key => debouncedFormState[key as keyof FormState]?.toString()?.trim())) {
      const validation = validateForm(debouncedFormState, event)
      const errorMessages: Record<string, string> = {}

      validation.errors.forEach(errorCode => {
        errorMessages[errorCode] = getErrorMessage(errorCode)
      })

      setValidationErrors(errorMessages)
    }
  }, [debouncedFormState, event])

  // Form submission handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Rate limiting check
    if (!RegistrationRateLimiter.canSubmit()) {
      onRegistrationError({
        code: 'RATE_LIMIT',
        message: 'Please wait a moment before submitting again'
      })
      return
    }

    // Enhanced validation
    const validation = validateForm(formState, event)
    if (!validation.valid) {
      onRegistrationError({
        code: 'VALIDATION_FAILED',
        message: 'Please correct the following errors',
        details: { errors: validation.errors }
      })
      return
    }

    // Check event capacity with enhanced validation
    if (isEventFull || !canRegister) {
      onRegistrationError({
        code: 'EVENT_FULL',
        message: 'This event is currently at capacity'
      })
      return
    }

    setSubmitting(true)
    RegistrationRateLimiter.recordSubmission()

    try {
      // Prepare registration data with enterprise security
      const registrationData = {
        eventId: event.id,
        attendee: {
          email: sanitizeInput(formState.email, { email: true }),
          name: sanitizeInput(formState.name),
          phone: sanitizeInput(formState.phone),
          classification: sanitizeInput(formState.classification),
          rotaryId: formState.rotaryId ? sanitizeInput(formState.rotaryId) : undefined,
          consentDataProcessing: formState.consentDataProcessing,
          marketingConsent: formState.marketingConsent,
          timestamp: new Date().toISOString(),
        }
      }

      // Submit registration
      const result = await post<{ registrationId: string }>('/api/events/register', registrationData);

      if (result.success) {
        setRegistrationSuccess(true);

        const successData: RegistrationSuccessData = {
          eventId: event.id,
          memberId: memberProfile?.id || 'anonymous',
          attendeeInfo: registrationData.attendee,
          timestamp: new Date().toISOString(),
          registrationId: result.data?.registrationId || `reg_${event.id}_${Date.now()}` // Fallback ID
        };

        onRegistrationSuccess(successData);
      } else {
        throw new Error(result.error || 'Registration failed');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed'
      onRegistrationError({
        code: 'SUBMISSION_ERROR',
        message: errorMessage
      })
    } finally {
      setSubmitting(false)
    }
  }

  // Form field update handler
  const updateField = (field: keyof FormState, value: string | boolean) => {
    setFormState(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear validation error when field is updated
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // Input sanitization following Task 1.2.4 security patterns
  const sanitizeInput = (input: string, options: { email?: boolean, phone?: boolean } = {}): string => {
    if (typeof input !== 'string') return ''

    let sanitized = input.trim()

    if (options.email) {
      // Email-specific sanitization
      sanitized = sanitized.toLowerCase()
    } else if (options.phone) {
      // Phone-specific sanitization
      sanitized = sanitized.replace(/[^\d\+\-\s\(\)]/g, '')
    } else {
      // General text sanitization
      sanitized = sanitized.replace(/[<>'"&]/g, '')
    }

    return sanitized
  }

  // Error message translations
  const getErrorMessage = (errorCode: string): string => {
    const messages: Record<string, string> = {
      emailRequired: 'Email address is required',
      emailInvalid: 'Please enter a valid email address',
      nameRequired: 'Full name is required',
      phoneRequired: 'Phone number is required',
      phoneInvalid: 'Please enter a valid phone number',
      classificationRequired: 'Professional classification is required',
      rotaryIdInvalid: 'Please enter a valid Rotary ID number',
      consentRequired: 'You must consent to data processing to register',
      duplicateRegistration: 'You are already registered for this event'
    }
    return messages[errorCode] || 'This field is required'
  }

  // Conditional rendering states
  if (registrationSuccess) {
    return (
      <RegistrationSuccessDisplay
        eventTitle={event.title}
        registeredEmail={formState.email}
      />
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6" noValidate>
      <ProfileLoadingDisplay
        loadingProfile={loadingProfile}
        profileError={profileError}
      />

      <EventStatusDisplay
        eventCapacity={eventCapacity}
        isEventFull={isEventFull}
        spotsRemaining={spotsRemaining}
        currentAttendees={currentAttendees}
      />

      <BasicInfoSection
        formState={formState}
        updateField={updateField}
        validationErrors={validationErrors}
        submitting={submitting}
      />

      <ConsentSection
        formState={formState}
        updateField={updateField}
        validationErrors={validationErrors}
        submitting={submitting}
      />

      {/* Submit Button with Enterprise Features */}
      <div className="flex items-center justify-center pt-4">
        <Button
          type="submit"
          disabled={submitting || isEventFull || !canRegister || loadingProfile}
          className="w-full sm:w-auto min-w-[200px]"
        >
          {submitting ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Registering...</span>
            </div>
          ) : isEventFull ? (
            'Event Full'
          ) : (
            'Register for Event'
          )}
        </Button>
      </div>

      {/* Processing status */}
      {submitting && (
        <div className="text-center text-sm text-gray-600">
          Processing your registration securely...
        </div>
      )}

      {/* Accessibility information */}
      <div className="sr-only" aria-live="polite">
        {Object.keys(validationErrors).length > 0 && 'Form contains validation errors. Please review and correct the highlighted fields.'}
      </div>
    </form>
  )
}

// Export for type imports in other files
export type { AttendeeData, RegistrationSuccessData, RegistrationError }