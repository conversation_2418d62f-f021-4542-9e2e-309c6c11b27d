import React from 'react';
import type { FormState } from '../index'; // Assuming FormState is exported from index.tsx

interface ConsentSectionProps {
  formState: FormState;
  updateField: (field: keyof FormState, value: string | boolean) => void;
  validationErrors: Record<string, string>;
  submitting: boolean;
}

export const ConsentSection: React.FC<ConsentSectionProps> = ({
  formState,
  updateField,
  validationErrors,
  submitting,
}) => {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Data Processing Consent</h3>

      {/* GDPR Consent */}
      <div className="flex items-start space-x-3 mb-4">
        <input
          type="checkbox"
          id="consentDataProcessing"
          checked={formState.consentDataProcessing}
          onChange={(e) => updateField('consentDataProcessing', e.target.checked)}
          className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          required
          disabled={submitting}
        />
        <div className="flex-1">
          <label htmlFor="consentDataProcessing" className="text-sm font-medium text-gray-700">
            I consent to the processing of my personal data for event registration purposes *
          </label>
          <p className="text-xs text-gray-500 mt-1">
            We process your data according to our privacy policy and GDPR regulations to manage event registration and communication.
          </p>
        </div>
      </div>

      {/* Marketing Consent */}
      <div className="flex items-start space-x-3">
        <input
          type="checkbox"
          id="marketingConsent"
          checked={formState.marketingConsent}
          onChange={(e) => updateField('marketingConsent', e.target.checked)}
          className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          disabled={submitting}
        />
        <div className="flex-1">
          <label htmlFor="marketingConsent" className="text-sm font-medium text-gray-700">
            I would like to receive Rotary newsletters and event updates
          </label>
          <p className="text-xs text-gray-500 mt-1">
            Optional: Stay informed about future Rotary activities and community service opportunities.
          </p>
        </div>
      </div>

      {validationErrors.consentRequired && (
        <p className="mt-2 text-sm text-red-600">{validationErrors.consentRequired}</p>
      )}
    </div>
  );
};