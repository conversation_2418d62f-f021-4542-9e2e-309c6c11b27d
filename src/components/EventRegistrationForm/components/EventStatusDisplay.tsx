import React from 'react';

interface EventStatusDisplayProps {
  eventCapacity: number;
  isEventFull: boolean;
  spotsRemaining: number | null;
  currentAttendees: number;
}

export const EventStatusDisplay: React.FC<EventStatusDisplayProps> = ({
  eventCapacity,
  isEventFull,
  spotsRemaining,
  currentAttendees,
}) => {
  if (eventCapacity <= 0) { // Handle unlimited capacity explicitly
    return null;
  }

  return (
    <div className={`rounded-lg p-4 ${isEventFull ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`}>
      <p className={`font-medium ${isEventFull ? 'text-red-800' : 'text-green-800'}`}>
        {isEventFull ? 'This event is currently full' : `${spotsRemaining} spots remaining`}
      </p>
      <p className={`text-sm ${isEventFull ? 'text-red-700' : 'text-green-700'}`}>
        {currentAttendees} of {eventCapacity} registered
      </p>
    </div>
  );
};