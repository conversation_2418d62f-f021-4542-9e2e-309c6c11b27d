import React from 'react';

interface ProfileLoadingDisplayProps {
  loadingProfile: boolean;
  profileError: string;
}

export const ProfileLoadingDisplay: React.FC<ProfileLoadingDisplayProps> = ({
  loadingProfile,
  profileError,
}) => {
  return (
    <>
      {/* Loading state for profile pre-population */}
      {loadingProfile && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <p className="text-blue-700">Loading your profile information...</p>
          </div>
        </div>
      )}

      {/* Profile loading error - graceful degradation */}
      {profileError && !loadingProfile && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="text-yellow-600">⚠️</div>
            <p className="text-yellow-700">{profileError}</p>
          </div>
        </div>
      )}
    </>
  );
};