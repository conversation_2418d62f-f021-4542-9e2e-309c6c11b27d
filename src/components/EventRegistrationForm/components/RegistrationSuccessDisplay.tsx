import React from 'react';

interface RegistrationSuccessDisplayProps {
  eventTitle: string;
  registeredEmail: string;
}

export const RegistrationSuccessDisplay: React.FC<RegistrationSuccessDisplayProps> = ({
  eventTitle,
  registeredEmail,
}) => {
  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
      <div className="text-green-600 mb-4">
        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h3 className="text-xl font-semibold text-green-800 mb-2">Registration Successful!</h3>
      <p className="text-green-700">
        You have been registered for {eventTitle}. A confirmation email will be sent to {registeredEmail}.
      </p>
    </div>
  );
};