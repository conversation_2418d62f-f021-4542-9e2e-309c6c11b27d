import React from 'react';
import type { FormState } from '../index'; // Assuming FormState is exported from index.tsx

interface BasicInfoSectionProps {
  formState: FormState;
  updateField: (field: keyof FormState, value: string | boolean) => void;
  validationErrors: Record<string, string>;
  submitting: boolean;
}

export const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  formState,
  updateField,
  validationErrors,
  submitting,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>

      {/* Name Field */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
          Full Name *
        </label>
        <input
          type="text"
          id="name"
          value={formState.name}
          onChange={(e) => updateField('name', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            validationErrors.nameRequired || validationErrors.name ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter your full name"
          required
          disabled={submitting}
        />
        {validationErrors.nameRequired && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.nameRequired}</p>
        )}
        {validationErrors.name && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.name}</p>
        )}
      </div>

      {/* Email Field */}
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
          Email Address *
        </label>
        <input
          type="email"
          id="email"
          value={formState.email}
          onChange={(e) => updateField('email', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            validationErrors.emailRequired || validationErrors.emailInvalid ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter your email address"
          required
          disabled={submitting}
        />
        {validationErrors.emailRequired && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.emailRequired}</p>
        )}
        {validationErrors.emailInvalid && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.emailInvalid}</p>
        )}
        {validationErrors.duplicateRegistration && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.duplicateRegistration}</p>
        )}
      </div>

      {/* Phone Field */}
      <div>
        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
          Phone Number *
        </label>
        <input
          type="tel"
          id="phone"
          value={formState.phone}
          onChange={(e) => updateField('phone', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            validationErrors.phoneRequired || validationErrors.phoneInvalid ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="+216 123 456 789"
          required
          disabled={submitting}
        />
        {validationErrors.phoneRequired && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.phoneRequired}</p>
        )}
        {validationErrors.phoneInvalid && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.phoneInvalid}</p>
        )}
      </div>

      {/* Classification Field */}
      <div>
        <label htmlFor="classification" className="block text-sm font-medium text-gray-700 mb-1">
          Professional Classification *
        </label>
        <input
          type="text"
          id="classification"
          value={formState.classification}
          onChange={(e) => updateField('classification', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            validationErrors.classificationRequired ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="e.g., Business Owner, Doctor, Teacher"
          required
          disabled={submitting}
        />
        {validationErrors.classificationRequired && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.classificationRequired}</p>
        )}
      </div>

      {/* Optional Rotary ID Field */}
      <div>
        <label htmlFor="rotaryId" className="block text-sm font-medium text-gray-700 mb-1">
          Rotary ID Number
        </label>
        <input
          type="text"
          id="rotaryId"
          value={formState.rotaryId}
          onChange={(e) => updateField('rotaryId', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            validationErrors.rotaryIdInvalid ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="e.g., 12345"
          disabled={submitting}
        />
        {validationErrors.rotaryIdInvalid && (
          <p className="mt-1 text-sm text-red-600">{validationErrors.rotaryIdInvalid}</p>
        )}
      </div>
    </div>
  );
};