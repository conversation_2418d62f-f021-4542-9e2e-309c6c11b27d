import type { GlobalConfig } from 'payload'

export const ImpactStats: GlobalConfig = {
  slug: 'impact-stats',
  label: 'Impact Statistics',
  access: {
    read: () => true, // Public read access for frontend
    update: ({ req }) => req.user?.collection === 'users', // Only authenticated users can update
  },
  fields: [
    {
      name: 'members',
      type: 'number',
      label: 'Total Members',
      admin: {
        description: 'Total number of Rotary members (e.g., 1.2M)',
      },
    },
    {
      name: 'membersFormatted',
      type: 'text',
      label: 'Members Display Text',
      defaultValue: '1.2M+ Members',
      admin: {
        description: 'How members should be displayed (e.g., "1.2M+", "1,200,000+")',
      },
    },
    {
      name: 'volunteerHours',
      type: 'number',
      label: 'Volunteer Hours',
      admin: {
        description: 'Total volunteer hours contributed (e.g., 47M)',
      },
    },
    {
      name: 'volunteerHoursFormatted',
      type: 'text',
      label: 'Volunteer Hours Display Text',
      defaultValue: '47M+ Hours',
      admin: {
        description: 'How volunteer hours should be displayed',
      },
    },
    {
      name: 'projectsCompleted',
      type: 'number',
      label: 'Projects Completed',
      admin: {
        description: 'Number of projects completed',
      },
    },
    {
      name: 'projectsCompletedFormatted',
      type: 'text',
      label: 'Projects Display Text',
      defaultValue: '10,000+ Projects',
      admin: {
        description: 'How projects should be displayed',
      },
    },
    {
      name: 'funding',
      type: 'number',
      label: 'Funding Amount',
      admin: {
        description: 'Total funding in USD (e.g., 291000000 for $291M)',
        step: 1000000,
      },
    },
    {
      name: 'fundingFormatted',
      type: 'text',
      label: 'Funding Display Text',
      defaultValue: '$291M+ Funded',
      admin: {
        description: 'How funding should be displayed',
      },
    },
    {
      name: 'countries',
      type: 'number',
      label: 'Countries Served',
      admin: {
        description: 'Number of countries where Rotary operates',
      },
    },
    {
      name: 'countriesFormatted',
      type: 'text',
      label: 'Countries Display Text',
      defaultValue: '200+ Countries',
      admin: {
        description: 'How countries should be displayed',
      },
    },
    {
      name: 'lastUpdated',
      type: 'date',
      label: 'Last Updated',
      admin: {
        description: 'When these statistics were last updated',
        date: {
          pickerAppearance: 'dayOnly',
        },
      },
      hooks: {
        beforeChange: [
          () => {
            return new Date()
          },
        ],
      },
    },
    {
      name: 'source',
      type: 'text',
      label: 'Data Source',
      admin: {
        description: 'Source of these statistics (e.g., "Rotary International 2024 Report")',
      },
    },
  ],
}