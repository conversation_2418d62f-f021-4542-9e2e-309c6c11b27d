import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import { resolve } from 'path'

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./vitest.setup.ts'],
    include: [
      'src/app/**/*.test.{ts,tsx}',
      'src/utilities/__tests__/**/*.test.ts',
      'tests/int/**/*.int.spec.ts',
      'tests/**/*.test.ts'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['json', 'html', 'text'],
      include: ['src/utilities/*.ts'],
      reportsDirectory: './coverage'
    },
    // GDPR test specific configuration
    globals: true,
    deps: {
      // Ensure proper import handling for Payload CMS and GDPR modules
      interopDefault: true,
      moduleDirectories: ['node_modules']
    },
  },
})
