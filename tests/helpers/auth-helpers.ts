/**
 * Authentication Helpers for Playwright E2E Tests
 * Provides utilities for user management and authentication in tests
 */

import { Page } from '@playwright/test'
import { getPayload } from 'payload'
import payloadConfig from '../../src/payload.config'

interface TestUserData {
  email: string
  name: {
    en: string
    fr?: string
    ar?: string
  }
  classification: {
    en: string
    fr?: string
    ar?: string
  }
  phonePersonal?: string
  phoneWork?: string
  rotaryDistrict?: '1930' | '1890' | 'other'
  rotaryClub?: string
}

interface TestUserCredentials {
  id: string
  email: string
  currentPassword: string
}

// In-memory store for test users to avoid creating duplicates
const createdTestUsers = new Map<string, TestUserCredentials>()

/**
 * Create a test user with complete profile data
 */
interface CreateUserResult {
  success: boolean
  credentials?: TestUserCredentials
  error?: string
  dbError?: boolean
}

export async function createTestUser(userData: TestUserData): Promise<TestUserCredentials> {
  // Check if user already exists
  const existingUser = createdTestUsers.get(userData.email)
  if (existingUser) {
    return existingUser
  }

  try {
    const payload = await getPayload({ config: payloadConfig })

    // Validate required fields before attempting creation
    if (!userData.email || !userData.name?.en) {
      throw new Error('Missing required fields: email and name.en are required')
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(userData.email)) {
      throw new Error(`Invalid email format: ${userData.email}`)
    }

    // Create user in database with validated data
    const payloadData = {
      email: userData.email,
      name: userData.name.en, // Use string for now, localization handled separately
      classification: userData.classification.en,
      ...(userData.phonePersonal && { phonePersonal: userData.phonePersonal }),
      ...(userData.phoneWork && { phoneWork: userData.phoneWork }),
      ...(userData.rotaryDistrict && { rotaryDistrict: userData.rotaryDistrict }),
      ...(userData.rotaryClub && { rotaryClub: userData.rotaryClub }),
      // Set default privacy settings
      privacySettings: {
        isPublicProfile: false,
        shareContactDetails: true,
        sharePhotos: true,
        marketingConsent: false,
        dataSharingConsent: false,
      } as const,
      // Set default communication preferences
      communicationPreferences: {
        emailNotifications: true,
        newsletterSubscription: true,
        meetingReminders: true,
        committeeUpdates: true,
      } as const,
      // Store localized data separately if needed
      localizationData: {
        name: userData.name,
        classification: userData.classification,
      },
    }

    const user = await payload.create({
      collection: 'users',
      data: payloadData,
    })

    if (!user?.id) {
      throw new Error('User creation failed: no ID returned from database')
    }

    const userCredentials: TestUserCredentials = {
      id: user.id,
      email: userData.email,
      currentPassword: 'DefaultTestPass123!',
    }

    createdTestUsers.set(userData.email, userCredentials)
    return userCredentials

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown database error'

    // Log the actual error but provide structured response
    console.error('Test user creation failed:', {
      email: userData.email,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    })

    // Provide detailed error information instead of silently returning mock data
    throw new Error(`Failed to create test user ${userData.email}: ${errorMessage}`)
  }
}

/**
 * Clean up test user from database
 */
export async function cleanupTestUser(userId: string): Promise<void> {
  try {
    const payload = await getPayload({ config: payloadConfig })
    await payload.delete({
      collection: 'users',
      id: userId,
    })
  } catch (error) {
    console.error(`Failed to cleanup user ${userId}:`, error)
  }
}

/**
 * Authenticate test user in Playwright page
 */
export async function authenticateTestUser(page: Page, credentials: TestUserCredentials): Promise<void> {
  // Fill in login form
  await page.locator('input[type="email"]').fill(credentials.email)
  await page.locator('input[type="password"]').fill(credentials.currentPassword)

  // Submit login form
  await page.locator('button:has-text("Login"), input[type="submit"]').first().click()

  // Wait for authentication to complete
  await page.waitForLoadState('networkidle')

  // Verify login success
  const currentUrl = page.url()
  if (currentUrl.includes('/login') || currentUrl.includes('/auth')) {
    throw new Error(`Login failed for user ${credentials.email}`)
  }
}

/**
 * Get stored test user credentials
 */
export function getTestUserCredentials(email: string): TestUserCredentials | undefined {
  return createdTestUsers.get(email)
}

/**
 * Wait for profile page components to load
 */
export async function waitForProfilePageLoad(page: Page): Promise<void> {
  await page.waitForSelector('h1:has-text("My Profile Settings")', { timeout: 10000 })
  await page.waitForLoadState('domcontentloaded')
}

/**
 * Validate profile form fields
 */
export async function validateProfileFormFields(page: Page, expectedData: Partial<TestUserData>): Promise<void> {
  if (expectedData.name?.en) {
    await page.waitForSelector(`input[value="${expectedData.name.en}"]`)
  }

  if (expectedData.phonePersonal) {
    await page.waitForSelector(`input[value="${expectedData.phonePersonal}"]`)
  }

  if (expectedData.rotaryClub) {
    await page.waitForSelector(`text=${expectedData.rotaryClub}`)
  }
}

/**
 * Fill and submit profile update form
 */
export async function updateProfileData(page: Page, updateData: Partial<TestUserData>): Promise<void> {
  // Switch to basic information tab if not already active
  await page.click('text=Basic Information')

  if (updateData.name?.en) {
    const englishNameInput = page.locator('input[placeholder*="Full name in English"]')
    await englishNameInput.clear()
    await englishNameInput.fill(updateData.name.en)
  }

  if (updateData.classification?.en) {
    const englishClassInput = page.locator('input[placeholder*="Your profession in English"]')
    await englishClassInput.clear()
    await englishClassInput.fill(updateData.classification.en)
  }

  if (updateData.phonePersonal) {
    const personalPhoneInput = page.locator('input[placeholder="+216 123 456 789"]')
    await personalPhoneInput.clear()
    await personalPhoneInput.fill(updateData.phonePersonal)
  }

  // Save changes
  await page.click('text=Save Basic Information')
  await page.waitForSelector('text=Profile information saved successfully!', { timeout: 5000 })
}

/**
 * Cleanup all test users created during test run
 */
export async function cleanupAllTestUsers(): Promise<void> {
  console.log('\n🧹 Cleaning up test users...')

  for (const [email, credentials] of createdTestUsers) {
    try {
      await cleanupTestUser(credentials.id)
      console.log(`✓ Cleaned up test user: ${email}`)
    } catch (error) {
      console.error(`✗ Failed to cleanup test user: ${email}`, error)
    }
  }

  createdTestUsers.clear()
}

// Global cleanup on process exit
process.on('exit', () => {
  cleanupAllTestUsers()
})

process.on('SIGINT', () => {
  cleanupAllTestUsers()
  process.exit(0)
})
