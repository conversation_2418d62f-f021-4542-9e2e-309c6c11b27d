/**
 * Enhanced Authentication Helpers with Better Error Handling
 */

import { Page } from '@playwright/test'
import { getPayload } from 'payload'
import payloadConfig from '../../src/payload.config'

// Type-safe interfaces with proper validation
interface TestUserData {
  email: string
  name: { en: string; fr?: string; ar?: string }
  classification: { en: string; fr?: string; ar?: string }
  phonePersonal?: string
  phoneWork?: string
  rotaryDistrict?: '1930' | '1890' | 'other'
  rotaryClub?: string
  rotaryId?: string
  joiningDate?: string
}

interface CreateUserResult {
  success: boolean
  credentials?: TestUserCredentials
  error?: string
  dbError?: boolean
}

// Error classification for better debugging
enum AuthErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  DUPLICATE_USER = 'DUPLICATE_USER',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

class AuthHelperError extends Error {
  constructor(
    message: string,
    public type: AuthErrorType,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'AuthHelperError'
  }
}

// Enhanced user creation with comprehensive error handling
export async function createTestUserReliable(userData: TestUserData): Promise<TestUserCredentials> {
  // Input validation before database operations
  const validation = validateUserInput(userData)
  if (!validation.valid) {
    throw new AuthHelperError(
      `Validation failed: ${validation.errors.join(', ')}`,
      AuthErrorType.VALIDATION_ERROR
    )
  }

  // Check if user already exists
  const existingUser = getTestUserCredentials(userData.email)
  if (existingUser) {
    console.log(`✓ Reusing existing test user: ${userData.email}`)
    return existingUser
  }

  try {
    const payload = await getPayload({ config: payloadConfig })

    // Prepare minimal required data structure
    const userDataMinimal = {
      email: userData.email,
      name: userData.name.en, // Use English as primary
      classification: userData.classification.en,
      password: generateSecurePassword(),
      rotaryId: userData.rotaryId || generateTestRotaryId(),
      joiningDate: userData.joiningDate || new Date().toISOString(),
      ...(userData.rotaryDistrict && { rotaryDistrict: userData.rotaryDistrict }),
      ...(userData.rotaryClub && { rotaryClub: userData.rotaryClub }),
      ...(userData.phonePersonal && { phonePersonal: userData.phonePersonal }),
      ...(userData.phoneWork && { phoneWork: userData.phoneWork }),
      privacySettings: getDefaultPrivacySettings(),
      communicationPreferences: getDefaultCommunicationPreferences(),
    }

    const user = await payload.create({
      collection: 'users',
      data: userDataMinimal,
    })

    if (!user?.id) {
      throw new AuthHelperError(
        'User creation returned invalid response',
        AuthErrorType.DATABASE_ERROR
      )
    }

    const credentials = {
      id: user.id,
      email: userData.email,
      currentPassword: userDataMinimal.password,
    }

    createdTestUsers.set(userData.email, credentials)
    console.log(`✓ Created test user: ${userData.email}`)
    return credentials

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'

    // Classify errors for better handling
    let errorType = AuthErrorType.DATABASE_ERROR

    if (errorMessage.includes('duplicate') || errorMessage.includes('unique')) {
      errorType = AuthErrorType.DUPLICATE_USER
    } else if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      errorType = AuthErrorType.NETWORK_ERROR
    }

    console.error(`✗ Test user creation failed:`, {
      email: userData.email,
      errorType,
      error: errorMessage,
      timestamp: new Date().toISOString()
    })

    throw new AuthHelperError(
      `Failed to create test user: ${errorMessage}`,
      errorType,
      error instanceof Error ? error : undefined
    )
  }
}

// Input validation for test user data
function validateUserInput(data: TestUserData): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // Required field validation
  if (!data.email?.trim()) {
    errors.push('Email is required')
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      errors.push('Invalid email format')
    }
  }

  if (!data.name?.en?.trim()) {
    errors.push('Name (English) is required')
  }

  if (!data.classification?.en?.trim()) {
    errors.push('Classification (English) is required')
  }

  // Optional field validation
  if (data.phonePersonal && !isValidPhoneNumber(data.phonePersonal)) {
    errors.push('Invalid phonePersonal format')
  }

  if (data.rotaryId && !isValidRotaryId(data.rotaryId)) {
    errors.push('Invalid Rotary ID format')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// Enhanced authentication with retry logic
export async function authenticateTestUserResilient(
  page: Page,
  credentials: TestUserCredentials,
  timeoutMs: number = 30000
): Promise<boolean> {
  const startTime = Date.now()

  try {
    // Wait for and fill email field
    await page.waitForSelector('input[type="email"]', { timeout: 5000 })
    await page.locator('input[type="email"]').clear()
    await page.locator('input[type="email"]').fill(credentials.email)

    // Wait for and fill password field
    await page.waitForSelector('input[type="password"]', { timeout: 5000 })
    await page.locator('input[type="password"]').clear()
    await page.locator('input[type="password"]').fill(credentials.currentPassword)

    // Submit login form
    await page.locator('button:has-text("Login"), input[type="submit"]')
      .first()
      .click()

    // Wait for navigation or success indicator
    await Promise.race([
      page.waitForURL('**/members/**', { timeout: 10000 }),
      page.waitForSelector('text=Dashboard', { timeout: 10000 }),
      page.waitForSelector('text=Profile', { timeout: 10000 }),
    ])

    // Verify successful authentication
    const currentUrl = page.url()
    const authenticationSuccessful = currentUrl.includes('/members/') ||
                                   currentUrl.includes('/dashboard') ||
                                   currentUrl.includes('/profile')

    if (authenticationSuccessful) {
      console.log(`✓ Authentication successful: ${credentials.email}`)
      return true
    } else {
      console.error(`✗ Authentication failed: Sticking at ${currentUrl}`)
      return false
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error(`✗ Authentication error after ${Date.now() - startTime}ms:`, errorMessage)

    // Take screenshot for debugging if Playwright supports it
    try {
      await page.screenshot({
        path: `auth-error-${Date.now()}.png`,
        fullPage: true
      })
    } catch {
      // Screenshot not supported
    }

    return false
  }
}

// Utility functions
function generateSecurePassword(): string {
  return `TestPass${Date.now()}!`
}

function generateTestRotaryId(): string {
  return `TEST${Date.now().toString().slice(-6)}`
}

function getDefaultPrivacySettings() {
  return {
    isPublicProfile: false,
    shareContactDetails: true,
    sharePhotos: true,
    marketingConsent: false,
    dataSharingConsent: false,
  }
}

function getDefaultCommunicationPreferences() {
  return {
    emailNotifications: true,
    newsletterSubscription: true,
    meetingReminders: true,
    committeeUpdates: true,
  }
}

function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

function isValidRotaryId(rotaryId: string): boolean {
  // Rotary ID format validation
  return /^\d{1,8}$/.test(rotaryId.replace(/^RI/i, ''))
}

// Type-safe user credentials storage
const createdTestUsers = new Map<string, TestUserCredentials>()

interface TestUserCredentials {
  id: string
  email: string
  currentPassword: string
}

// Global cleanup
export async function cleanupAllTestUsersEnhanced(): Promise<void> {
  console.log('\n🧹 Enhanced cleanup starting...')

  const cleanupPromises = Array.from(createdTestUsers.entries()).map(async ([email, credentials]) => {
    try {
      const payload = await getPayload({ config: payloadConfig })
      await payload.delete({
        collection: 'users',
        id: credentials.id,
      })
      console.log(`✓ Cleaned up: ${email}`)
      return true
    } catch (error) {
      console.error(`✗ Failed to cleanup: ${email}`, error)
      return false
    }
  })

  const results = await Promise.all(cleanupPromises)
  const successCount = results.filter(result => result).length

  console.log(`\n🧹 Cleanup complete: ${successCount}/${results.length} users removed`)
  createdTestUsers.clear()
}

export function getTestUserCredentials(email: string): TestUserCredentials | undefined {
  return createdTestUsers.get(email)
}