import { describe, it, expect } from 'vitest';
import { validationRules, validateForm } from '../../src/components/EventRegistrationForm/index'; // Adjust path as needed

// Mock types for testing validateForm
interface MockFormState {
  email: string;
  name: string;
  phone: string;
  classification: string;
  rotaryId: string;
  consentDataProcessing: boolean;
  marketingConsent: boolean;
}

interface MockEvent {
  id: string;
  capacity?: number;
  attendees?: { userEmail: string }[];
  registrationForm?: {
    registrationDeadline?: string;
  };
}

describe('validationRules', () => {
  it('should validate email correctly', () => {
    expect(validationRules.email('<EMAIL>')).toBe(true);
    expect(validationRules.email('invalid-email')).toBe(false);
    expect(validationRules.email('')).toBe(false);
  });

  it('should validate phone correctly', () => {
    expect(validationRules.phone('+1234567890')).toBe(true);
    expect(validationRules.phone('************')).toBe(true);
    expect(validationRules.phone('short')).toBe(false);
    expect(validationRules.phone('')).toBe(false);
  });

  it('should validate required fields', () => {
    expect(validationRules.required('some value')).toBe(true);
    expect(validationRules.required('')).toBe(false);
    expect(validationRules.required(false)).toBe(false); // Assuming boolean required fields are handled elsewhere or are always true
  });

  it('should validate rotaryId correctly', () => {
    expect(validationRules.rotaryId('12345')).toBe(true);
    expect(validationRules.rotaryId('RI12345')).toBe(true);
    expect(validationRules.rotaryId('invalid')).toBe(false);
    expect(validationRules.rotaryId('')).toBe(true); // Optional field
  });
});

describe('validateForm', () => {
  const mockEvent: MockEvent = {
    id: 'event123',
    capacity: 10,
    attendees: [],
  };

  it('should return valid for a complete and valid form', () => {
    const formState: MockFormState = {
      email: '<EMAIL>',
      name: 'John Doe',
      phone: '+1234567890',
      classification: 'Engineer',
      rotaryId: '12345',
      consentDataProcessing: true,
      marketingConsent: false,
    };
    const result = validateForm(formState, mockEvent);
    expect(result.valid).toBe(true);
    expect(result.errors).toEqual([]);
  });

  it('should return errors for missing required fields', () => {
    const formState: MockFormState = {
      email: '',
      name: '',
      phone: '',
      classification: '',
      rotaryId: '',
      consentDataProcessing: false,
      marketingConsent: false,
    };
    const result = validateForm(formState, mockEvent);
    expect(result.valid).toBe(false);
    expect(result.errors).toContain('nameRequired');
    expect(result.errors).toContain('emailRequired');
    expect(result.errors).toContain('phoneRequired');
    expect(result.errors).toContain('classificationRequired');
    expect(result.errors).toContain('consentRequired');
  });

  it('should return error for invalid email format', () => {
    const formState: MockFormState = {
      email: 'invalid-email',
      name: 'John Doe',
      phone: '+1234567890',
      classification: 'Engineer',
      rotaryId: '12345',
      consentDataProcessing: true,
      marketingConsent: false,
    };
    const result = validateForm(formState, mockEvent);
    expect(result.valid).toBe(false);
    expect(result.errors).toContain('emailInvalid');
  });

  it('should return error for duplicate registration', () => {
    const eventWithAttendee: MockEvent = {
      ...mockEvent,
      attendees: [{ userEmail: '<EMAIL>' }],
    };
    const formState: MockFormState = {
      email: '<EMAIL>',
      name: 'John Doe',
      phone: '+1234567890',
      classification: 'Engineer',
      rotaryId: '12345',
      consentDataProcessing: true,
      marketingConsent: false,
    };
    const result = validateForm(formState, eventWithAttendee);
    expect(result.valid).toBe(false);
    expect(result.errors).toContain('duplicateRegistration');
  });
});