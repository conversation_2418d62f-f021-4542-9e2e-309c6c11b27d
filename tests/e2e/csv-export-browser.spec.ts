import { test, expect, Page } from '@playwright/test'
import { promises as fs } from 'fs'

// ✅ Test Configuration and Constants
const TEST_TIMEOUT = 30000
const DOWNLOAD_TIMEOUT = 15000
const CLEANUP_TIMEOUT = 5000

// Environment variables for secure authentication
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>'
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'test-password'

// ✅ Test Data Fixtures
interface TestEvent {
  id: string
  title: string
  slug: string
  exportFilename: string
}

const TEST_EVENTS: TestEvent[] = [
  {
    id: 'test-event-001',
    title: 'Test Rotary Event',
    slug: 'test-rotary-event',
    exportFilename: 'test-rotary-event-attendees'
  },
  {
    id: 'special-chars-event',
    title: 'Müller-Schmidt Event',
    slug: 'special-chars-event',
    exportFilename: 'muller-schmidt-event-attendees'
  }
]

// ✅ Helper Functions
class CSVExportTestHelper {
  constructor(private page: Page) {}

  /**
   * Authenticates user with admin credentials
   */
  async authenticate(): Promise<void> {
    try {
      await this.page.goto('/admin', { waitUntil: 'domcontentloaded' })

      // Use data-testid for reliable element selection
      await this.page.waitForSelector('[data-testid="email"]', { timeout: 10000 })
      await this.page.fill('[data-testid="email"]', ADMIN_EMAIL)

      await this.page.waitForSelector('[data-testid="password"]', { timeout: 5000 })
      await this.page.fill('[data-testid="password"]', ADMIN_PASSWORD)

      await this.page.click('[data-testid="login-button"]')

      // Wait for successful login
      await this.page.waitForURL(/\/admin/, { timeout: 10000 })
      await expect(this.page).toHaveURL(/\/admin/)

    } catch (error) {
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Navigates to specific event admin page
   */
  async navigateToEvent(eventSlug: string): Promise<void> {
    try {
      await this.page.goto(`/admin/collections/events`, { waitUntil: 'domcontentloaded' })

      // Wait for events table to load
      await this.page.waitForSelector('table tbody tr', { timeout: 10000 })

      // Find and click the event row
      await this.page.click(`tr:has-text("${TEST_EVENTS.find(e => e.slug === eventSlug)?.title || eventSlug}")`)

      // Wait for event detail page to load
      await this.page.waitForURL(/\/admin\/collections\/events\//, { timeout: 10000 })

    } catch (error) {
      throw new Error(`Navigation to event failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Triggers CSV export and waits for download
   */
  async triggerCSVExport(): Promise<{ download: any; filePath: string }> {
    try {
      // Start listening for download before triggering
      const downloadPromise = this.page.waitForEvent('download', { timeout: DOWNLOAD_TIMEOUT })

      // Click export button - use multiple selector strategies
      const exportSelector = [
        'button:has-text("Export Attendees")',
        '[data-testid="export-attendees-btn"]',
        'button[aria-label="Export attendees to CSV"]',
        '.export-csv-btn'
      ]

      let exportClicked = false
      for (const selector of exportSelector) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 })
          await this.page.click(selector)
          exportClicked = true
          break
        } catch {
          // Try next selector
        }
      }

      if (!exportClicked) {
        // Fallback: look for any button containing "export"
        await this.page.click('button:has-text("Export")')
      }

      const download = await downloadPromise
      const filePath = await download.path()

      return { download, filePath }

    } catch (error) {
      throw new Error(`CSV export trigger failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Validates CSV content structure and data
   */
  async validateCSVContent(filePath: string, expectedFilename: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      expect(content).toBeTruthy()

      // Validate filename pattern
      const download = await this.page.waitForEvent('download', { timeout: 1000 })
      expect(download.suggestedFilename()).toMatch(new RegExp(expectedFilename.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')))

      // Validate CSV structure
      const lines = content.split('\n')
      expect(lines.length).toBeGreaterThan(1) // At least header + one data row

      // Validate CSV headers
      const headers = lines[0].split(',')
      expect(headers).toContain('Attendee Name')
      expect(headers).toContain('Email Address')

      // Validate data rows (if any)
      if (lines.length > 1) {
        for (let i = 1; i < lines.length; i++) {
          if (lines[i].trim()) {
            const columns = lines[i].split(',')
            expect(columns).toHaveLength(headers.length)
          }
        }
      }

    } catch (error) {
      throw new Error(`CSV validation failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Cleans up downloaded files
   */
  async cleanupDownload(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath)
    } catch (error) {
      console.warn(`Failed to cleanup file ${filePath}: ${error instanceof Error ? error.message : String(error)}`)
    }
  }
}

// ✅ Test Suite Configuration
test.describe.configure({
  mode: 'parallel',
  timeout: TEST_TIMEOUT
})

test.describe('CSV Export - Cross Browser Compatibility', () => {
  let helper: CSVExportTestHelper
  let testEvent: TestEvent
  let specialCharsEvent: TestEvent

  test.beforeAll(async ({ browser }) => {
    // Setup test data in the database via API
    const context = await browser.newContext()
    const page = await context.newPage()

    // Create test events via API calls
    testEvent = TEST_EVENTS[0]
    specialCharsEvent = TEST_EVENTS[1]

    await context.close()
  })

  test.beforeEach(async ({ page }) => {
    helper = new CSVExportTestHelper(page)

    // Authenticate before each test
    await helper.authenticate()
  })

  test.afterEach(async ({ page }) => {
    // Cleanup any lingering downloads or state
    try {
      await page.close()
    } catch (error) {
      console.warn(`Page cleanup failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  })

  // ✅ CORE EXPORT FUNCTIONALITY TESTS
  test.describe('Core Export Functionality', () => {

    test('should successfully export attendees when data exists', async ({ page }) => {
      await helper.navigateToEvent(testEvent.slug)

      const { download, filePath } = await helper.triggerCSVExport()

      // Validate download
      expect(download.suggestedFilename()).toMatch(/test-rotary-event-attendees-\d{4}-\d{2}-\d{2}\.csv/)

      try {
        await helper.validateCSVContent(filePath, testEvent.exportFilename)
      } finally {
        await helper.cleanupDownload(filePath)
      }
    })

    test('should handle empty attendee list gracefully', async ({ page }) => {
      // Navigate to event with no attendees (would need specific test event)
      await helper.navigateToEvent('empty-event')

      const { filePath } = await helper.triggerCSVExport()

      try {
        const content = await fs.readFile(filePath, 'utf-8')
        expect(content).toContain('No attendees registered')
        expect(content.split('\n')).toHaveLength(1)
      } finally {
        await helper.cleanupDownload(filePath)
      }
    })

    test('should fail gracefully on non-existent event', async ({ page }) => {
      try {
        await helper.navigateToEvent('non-existent-event')
        // If navigation succeeds, try export and expect it to fail
        await expect(helper.triggerCSVExport()).rejects.toThrow()
      } catch (error) {
        expect((error as Error).message).toContain('Navigation')
      }
    })

  })

  // ✅ BROWSER SPECIFIC TESTS
  test.describe('Browser-Specific Tests', () => {

    test('Firefox: should handle special characters in CSV export', async ({ page, browserName }) => {
      test.skip(browserName !== 'firefox', 'Firefox-specific test')

      await helper.navigateToEvent(specialCharsEvent.slug)

      const { download, filePath } = await helper.triggerCSVExport()

      try {
        await helper.validateCSVContent(filePath, specialCharsEvent.exportFilename)

        // Specific Firefox validations
        const content = await fs.readFile(filePath, 'utf-8')
        expect(content).toContain('Müller') // Special character handling
        expect(content).toContain('<EMAIL>')

      } finally {
        await helper.cleanupDownload(filePath)
      }
    })

    test('Safari: should work with WebKit-specific download handling', async ({ page, browserName }) => {
      test.skip(browserName !== 'webkit', 'Safari/WebKit-specific test')

      await helper.navigateToEvent(testEvent.slug)

      const { download, filePath } = await helper.triggerCSVExport()

      try {
        await helper.validateCSVContent(filePath, testEvent.exportFilename)

        // Safari-specific validations
        expect(download.suggestedFilename()).toBeTruthy()
        expect(await fs.stat(filePath)).toBeTruthy() // File exists

      } finally {
        await helper.cleanupDownload(filePath)
      }
    })

    test('Mobile: should handle CSV export on mobile viewport', async ({ page, isMobile }) => {
      test.skip(!isMobile, 'Mobile-specific test')

      // Set mobile viewport if not already set by browser config
      if (page.viewportSize()?.width === 1280) {
        await page.setViewportSize({ width: 375, height: 667 }) // iPhone SE
      }

      await helper.navigateToEvent(testEvent.slug)

      const { download, filePath } = await helper.triggerCSVExport()

      try {
        await helper.validateCSVContent(filePath, testEvent.exportFilename)

        // Mobile-specific validations
        const viewport = page.viewportSize()
        expect(viewport?.width).toBeLessThanOrEqual(428)

      } finally {
        await helper.cleanupDownload(filePath)
      }
    })

  })

  // ✅ EDGE CASES AND ERROR HANDLING
  test.describe('Edge Cases and Error Handling', () => {

    test('should handle network timeouts gracefully', async ({ page }) => {
      await helper.navigateToEvent(testEvent.slug)

      // Simulate network delay or failure
      await page.route('**/export/**', async route => {
        await new Promise(resolve => setTimeout(resolve, 20000))
        await route.fulfill()
      })

      await expect(helper.triggerCSVExport()).rejects.toThrow(/timeout|download/i)
    })

    test('should handle corrupted download responses', async ({ page }) => {
      await helper.navigateToEvent(testEvent.slug)

      // Mock corrupted response
      await page.route('**/export/**', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'text/csv',
          body: 'invalid,csv,content,without,proper,headers'
        })
      })

      const { filePath } = await helper.triggerCSVExport()

      try {
        const content = await fs.readFile(filePath, 'utf-8')
        // Should still validate but expect different content
        expect(content).toBeTruthy()
        expect(content).not.toContain('Attendee Name') // Missing expected headers
      } finally {
        await helper.cleanupDownload(filePath)
      }
    })

    test('should handle large datasets efficiently', async ({ page }) => {
      // This test would require a test event with many attendees
      // Skipped for now as it requires specific test data setup
      test.skip(true, 'Requires test event with large attendee dataset')

      await helper.navigateToEvent('large-dataset-event')

      const startTime = Date.now()
      const { filePath } = await helper.triggerCSVExport()
      const endTime = Date.now()

      try {
        // Should complete within reasonable time (30 seconds for large dataset)
        expect(endTime - startTime).toBeLessThan(30000)

        const content = await fs.readFile(filePath, 'utf-8')
        const lines = content.split('\n')
        expect(lines.length).toBeGreaterThan(1000) // Large dataset

      } finally {
        await helper.cleanupDownload(filePath)
      }
    })

  })

  // ✅ PERFORMANCE TESTS
  test.describe('Performance Tests', () => {

    test('should export within acceptable time limits', async ({ page }) => {
      await helper.navigateToEvent(testEvent.slug)

      const startTime = Date.now()

      try {
        const { filePath } = await helper.triggerCSVExport()
        const exportTime = Date.now() - startTime

        // Should complete within 15 seconds
        expect(exportTime).toBeLessThan(15000)

        await helper.cleanupDownload(filePath)

      } catch (error) {
        const totalTime = Date.now() - startTime
        console.warn(`Export took ${totalTime}ms before failure`)

        if (totalTime > 15000) {
          throw new Error(`Export timed out after ${totalTime}ms`)
        }

        throw error
      }
    })

    test('should handle concurrent downloads without conflicts', async ({ page }) => {
      await helper.navigateToEvent(testEvent.slug)

      // Start multiple export operations
      const exportPromises = [
        helper.triggerCSVExport(),
        helper.triggerCSVExport(),
        helper.triggerCSVExport()
      ]

      const results = await Promise.allSettled(exportPromises)

      // At least one should succeed
      const successfulExports = results.filter(r => r.status === 'fulfilled')
      expect(successfulExports.length).toBeGreaterThan(0)

      // Cleanup all downloads
      for (const result of successfulExports) {
        const { filePath } = result.value
        await helper.cleanupDownload(filePath)
      }
    })

  })

})
