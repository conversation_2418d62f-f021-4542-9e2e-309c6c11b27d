/**
 * End-to-End Test for Task 1.2.4: Self-Service Profile Management
 * Comprehensive test covering complete user workflow from authentication to profile editing
 */

import { test, expect, Page } from '@playwright/test'
import {
  createTestUser,
  cleanupTestUser,
  authenticateTestUser,
  getTestUserCredentials
} from '../helpers/auth-helpers'

test.describe('Task 1.2.4: Profile Management End-to-End', () => {
  let page: Page
  let testUserCredentials: any

  // Setup test user before all tests
  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage()

    // Create test user with complete profile data
    testUserCredentials = await createTestUser({
      email: '<EMAIL>',
      name: { en: 'Test User', fr: 'Utilisateur Test', ar: 'مستخدم اختبار' },
      classification: { en: 'Business Owner', fr: 'Propriétaire d\'entreprise', ar: 'مالك عمل' },
      phonePersonal: '+216 12 345 678',
      phoneWork: '+216 98 765 432',
      rotaryDistrict: '1930',
      rotaryClub: 'Rotary Club Tunis Doyen'
    })

    // Set default privacy settings and communication preferences
    await setupInitialUserPreferences(testUserCredentials.id)
  })

  // Clean up after all tests
  test.afterAll(async () => {
    await cleanupTestUser(testUserCredentials.id)
    await page.close()
  })

  test('complete user profile management workflow', async ({ page }) => {
    // Step 1: Authenticate and access profile page
    await test.step('Authentication & Page Access', async () => {
      // Navigate to login page and authenticate
      await page.goto('/login')
      await expect(page.getByRole('heading', { name: 'Member Login' })).toBeVisible()

      // Sign in with test user
      await authenticateTestUser(page, testUserCredentials)
      await expect(page).toHaveURL(/\/members/, { timeout: 10000 })
    })

    // Step 2: Navigate to Profile Management
    await test.step('Profile Page Navigation', async () => {
      await page.goto('/members/profile')
      await expect(page.getByRole('heading', { name: 'My Profile Settings' })).toBeVisible()
      await expect(page.getByText('Manage your personal information and privacy preferences')).toBeVisible()
    })

    // Step 3: Validate initial profile data display
    await test.step('Initial Profile Data Display', async () => {
      // Switch to basic information tab
      await page.click('text=Basic Information')

      // Validate multilingual name fields
      await expect(page.locator('input[placeholder*="Full name in English"]')).toHaveValue('Test User')
      await expect(page.locator('input[placeholder*="Full name in Français"]')).toHaveValue('Utilisateur Test')
      await expect(page.locator('input[placeholder*="الاسم الكامل بالعربية"]')).toHaveValue('مستخدم اختبار')

      // Validate phone number fields
      await expect(page.locator('input[placeholder="+216 123 456 789"]')).toHaveValue('+216 12 345 678')
      await expect(page.locator('input[placeholder="+216 987 654 321"]')).toHaveValue('+216 98 765 432')

      // Validate Rotary information (read-only)
      await expect(page.locator('text=Rotary Club Tunis Doyen')).toBeVisible()
      await expect(page.locator('text=1930')).toBeVisible()

      // Validate profile completion indicator
      const completionIndicator = await page.locator('[class*="text-green"]').textContent()
      expect(parseInt(completionIndicator || '0')).toBeGreaterThan(0)
    })

    // Step 4: Update profile information
    await test.step('Profile Information Update', async () => {
      // Update English name
      const englishNameInput = page.locator('input[placeholder*="Full name in English"]')
      await englishNameInput.clear()
      await englishNameInput.fill('Updated Test User')

      // Update classification
      const englishClassInput = page.locator('input[placeholder*="Your profession in English"]')
      await englishClassInput.clear()
      await englishClassInput.fill('Software Engineer')

      // Update phone numbers
      const personalPhoneInput = page.locator('input[placeholder="+216 123 456 789"]')
      await personalPhoneInput.clear()
      await personalPhoneInput.fill('+216 22 333 444')

      // Save changes
      await page.click('text=Save Basic Information')
      await expect(page.getByText('Profile information saved successfully!')).toBeVisible()

      // Verify changes persisted by refreshing page
      await page.reload()
      await expect(page.locator('input[placeholder*="Full name in English"]')).toHaveValue('Updated Test User')
    })

    // Step 5: Privacy Settings Management
    await test.step('Privacy Settings Update', async () => {
      // Switch to Privacy tab
      await page.click('text=Privacy')

      // Validate privacy controls are visible
      await expect(page.getByText('Public Profile')).toBeVisible()
      await expect(page.getByText('Share Contact Details')).toBeVisible()
      await expect(page.getByText('Share Photos')).toBeVisible()
      await expect(page.getByText('Marketing Emails')).toBeVisible()
      await expect(page.getByText('Share with Partners')).toBeVisible()

      // Change privacy settings
      const publicProfileCheckbox = await page.locator('input[type="checkbox"]').first()
      await publicProfileCheckbox.check()

      const marketingCheckbox = await page.getByText('Marketing Emails').locator('..').locator('input[type="checkbox"]')
      await marketingCheckbox.uncheck()

      // Save privacy settings
      await page.click('text=Save Privacy Settings')
      await expect(page.getByText('Privacy settings saved successfully!')).toBeVisible()

      // Verify changes persisted
      await page.reload()
      const isPublicChecked = await publicProfileCheckbox.isChecked()
      expect(isPublicChecked).toBe(true)
    })

    // Step 6: Communication Preferences Update
    await test.step('Communication Preferences Update', async () => {
      // Switch to Communications tab
      await page.click('text=Communications')

      // Validate communication preferences
      await expect(page.getByText('Event Updates')).toBeVisible()
      await expect(page.getByText('Meeting Reminders')).toBeVisible()
      await expect(page.getByText('Newsletter Subscriptions')).toBeVisible()

      // Update communication preferences
      const newsletterCheckbox = await page.getByText('Monthly club newsletter').locator('..').locator('input[type="checkbox"]')
      await newsletterCheckbox.uncheck()

      // Save preferences
      await page.click('text=Save Communication Preferences')
      await expect(page.getByText('Communication preferences saved successfully!')).toBeVisible()
    })

    // Step 7: Password Change Test
    await test.step('Password Change Process', async () => {
      // Switch to Password tab
      await page.click('text=Password')

      // Validate password form elements
      await expect(page.getByPlaceholder('Current Password')).toBeVisible()
      await expect(page.getByPlaceholder('New Password')).toBeVisible()
      await expect(page.getByPlaceholder('Confirm New Password')).toBeVisible()

      // Test password validation
      await page.fill('input[type="password"]', '123') // Too short
      await page.click('text=Change Password')
      await expect(page.getByText('Password must be at least 8 characters')).toBeVisible()

      // Valid password change (mock)
      await page.fill('input[placeholder="Current Password"]', testUserCredentials.currentPassword)
      await page.fill('input[placeholder="New Password"]', 'NewSecurePass123!')
      await page.fill('input[placeholder="Confirm New Password"]', 'NewSecurePass123!')

      // Simulate successful password change
      await page.click('text=Change Password')
      await expect(page.getByText('Password changed successfully!')).toBeVisible()
    })

    // Step 8: API Response Validation
    await test.step('API Response Validation', async () => {
      // Intercept API calls and validate responses
      await page.route('**/api/users/profile', async route => {
        if (route.request().method() === 'GET') {
          const response = await route.fetch()
          const responseBody = await response.json()
          expect(responseBody).toHaveProperty('data')
          expect(responseBody).toHaveProperty('data.user')
          expect(responseBody.data.user).toHaveProperty('name')
          expect(responseBody.data.user).toHaveProperty('privacySettings')
          return response
        }
        return route.continue()
      })

      // Reload page to trigger API interception
      await page.reload()
      await expect(page.getByRole('heading', { name: 'My Profile Settings' })).toBeVisible()
    })

    // Step 9: Mobile Responsiveness Validation
    await test.step('Mobile Responsiveness Validation', async () => {
      // Test tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 })
      await expect(page.getByRole('heading', { name: 'My Profile Settings' })).toBeVisible()
      await expect(page.getByText('Save Basic Information')).toBeVisible()

      // Switch between tabs on mobile
      await page.click('text=Privacy')
      await expect(page.getByText('Share Contact Details')).toBeVisible()

      // Test small mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      await expect(page.getByText('Save Privacy Settings')).toBeVisible()
    })

    // Step 10: Accessibility Validation
    await test.step('Accessibility Validation', async () => {
      // Test keyboard navigation
      await page.keyboard.press('Tab')
      const firstFocusableElement = await page.evaluate(() => document.activeElement?.tagName)
      expect(['INPUT', 'BUTTON', 'A']).toContain(firstFocusableElement)

      // Test ARIA attributes
      const ariaLabels = await page.locator('[aria-label]').allTextContents()
      expect(ariaLabels.length).toBeGreaterThan(0)

      // Test semantic elements
      const headings = await page.locator('h1, h2, h3').allTextContents()
      expect(headings).toContain('My Profile Settings')
    })
  })

  test('error handling and validation', async ({ page }) => {
    await test.step('Error Handling Validation', async () => {
      // Navigate to profile page without authentication
      await page.goto('/members/profile')

      // Should redirect to login or show error
      if (page.url().includes('/login')) {
        await expect(page.getByText(/login/i)).toBeVisible()
      } else {
        await expect(page.getByText(/authentication.*required/i)).toBeVisible()
      }

      // Authenticate and continue
      await authenticateTestUser(page, testUserCredentials)
      await page.goto('/members/profile')

      // Test form validation errors
      await page.click('text=Basic Information')
      const englishNameInput = page.locator('input[placeholder*="Full name in English"]')
      await englishNameInput.clear()
      await page.click('text=Save Basic Information')

      // Should show validation error
      await expect(page.getByText('failed')).toBeVisible()
    })
  })

  test('privacy settings enforcement', async ({ page }) => {
    await test.step('Privacy Settings Integration Validation', async () => {
      // Authenticate and go to profile
      await authenticateTestUser(page, testUserCredentials)
      await page.goto('/members/profile')

      // Set public profile to false
      await page.click('text=Privacy')
      const publicProfileCheckbox = await page.locator('input[type="checkbox"]').first()
      await publicProfileCheckbox.uncheck()
      await page.click('text=Save Privacy Settings')

      // Simulate API call to member directory
      const directoryResponse = await page.request.get('/api/members')
      const memberData = await directoryResponse.json()

      // User should not appear in public directory
      const userVisible = memberData.members?.filter((member: any) =>
        member.name.en === 'Updated Test User'
      )
      expect(userVisible?.length || 0).toBe(0)
    })
  })
})

// Helper function for setting up initial user preferences
async function setupInitialUserPreferences(userId: string) {
  // This would typically be done in the test setup or via API
  // For now, we'll just ensure the user has the required structure
  // In a real scenario, you'd use the Payload CMS API directly
  console.log(`Setting up initial preferences for user: ${userId}`)
}
