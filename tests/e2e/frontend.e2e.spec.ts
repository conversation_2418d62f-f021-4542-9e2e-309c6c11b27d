import { test, expect } from '@playwright/test'

test.describe('Frontend', () => {

  test('can go on homepage', async ({ page }) => {
    // Navigate to homepage
    await page.goto('/', { waitUntil: 'domcontentloaded' })

    // Verify page title (adjust as needed for your actual app)
    await expect(page).toHaveTitle(/Rotary/i)

    // Verify main heading exists
    const heading = page.locator('h1').first()
    await expect(heading).toBeVisible()

    // Verify we're on the correct page
    await expect(page).toHaveURL(/\/$/)
  })

  test('can navigate to events page', async ({ page }) => {
    await page.goto('/', { waitUntil: 'domcontentloaded' })

    // Try multiple selectors for navigation
    const eventsLink = page.locator('a[href*="events"]').first()
    if (await eventsLink.isVisible()) {
      await eventsLink.click()
      await expect(page).toHaveURL(/\/events/)
    } else {
      // Skip if no events link found (may be expected in some setups)
      console.log('Events link not found, skipping navigation test')
    }
  })

  test('page loads within performance budget', async ({ page }) => {
    const startTime = Date.now()

    await page.goto('/', { waitUntil: 'domcontentloaded' })

    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(5000) // Should load within 5 seconds
  })
})
