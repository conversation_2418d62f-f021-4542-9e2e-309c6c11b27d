# Test Environment Variables
NODE_ENV=test

# Admin credentials for testing
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=test-password

# Base URL for tests
BASE_URL=http://localhost:3000

# Database settings for tests (if using separate test database)
# DATABASE_URL=postgresql://localhost:5432/test_rotary_cms

# Disable email sending in tests
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_SECURE=false

# Test-specific settings
TEST_TIMEOUT=30000
DOWNLOAD_TIMEOUT=15000
CLEANUP_TIMEOUT=5000