
import { render } from '@react-email/render';
import * as path from 'path';
import * as fs from 'fs';
import { RegistrationEmail } from '../src/emails/RegistrationConfirmation';

const emailNameToComponent = {
  'registration-confirmation': RegistrationEmail,
};

const emailName = process.argv[2];

if (!emailName || !emailNameToComponent[emailName]) {
  console.error(`Please provide a valid email name. Available emails: ${Object.keys(emailNameToComponent).join(', ')}`);
  process.exit(1);
}

const Component = emailNameToComponent[emailName];

const props = {
  userName: 'Test User',
  eventTitle: 'Test Event',
  eventDate: '2025-09-30',
  eventLocation: 'Test Location',
};

const html = await render(Component(props));

const outputPath = path.join(process.cwd(), 'emails');

if (!fs.existsSync(outputPath)) {
  fs.mkdirSync(outputPath);
}

fs.writeFileSync(path.join(outputPath, `${emailName}.html`), html);

console.log(`Email ${emailName} rendered to emails/${emailName}.html`);
