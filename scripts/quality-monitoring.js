#!/usr/bin/env node

/**
 * Quality Assurance Monitoring Script
 * Provides real-time quality metrics and alerting for production deployments
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

class QualityMonitor {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.coveragePath = path.join(this.projectRoot, 'coverage');
    this.latestReport = this.getLatestCoverageReport();
  }

  /**
   * Get the latest coverage report data
   */
  getLatestCoverageReport() {
    // Look for coverage data in actual location
    const possiblePaths = [
      path.join(this.coveragePath, 'coverage-final.json')
    ];

    for (const coverageFile of possiblePaths) {
      try {
        if (fs.existsSync(coverageFile)) {
          const data = JSON.parse(fs.readFileSync(coverageFile, 'utf-8'));
          return this.parseCoverageData(data);
        }
      } catch (error) {
        // Try next path
        continue;
      }
    }

    console.warn('⚠️  Could not read coverage file from any expected location');
    return null;
  }

  /**
   * Parse raw coverage data into structured metrics
   */
  parseCoverageData(data) {
    if (!data || typeof data !== 'object') {
      return null;
    }

    // Initialize counters for all files
    let totalStatements = 0;
    let coveredStatements = 0;
    let totalBranches = 0;
    let coveredBranches = 0;
    let totalFunctions = 0;
    let coveredFunctions = 0;
    let totalLines = 0;
    let coveredLines = 0;

    // Aggregate data from all files
    for (const [filePath, fileData] of Object.entries(data)) {
      if (!fileData || typeof fileData !== 'object') continue;

      // Calculate statements coverage
      const statements = fileData.s || {};
      const statementCount = Object.keys(statements).length;
      const coveredStatementCount = Object.values(statements).filter(count => count > 0).length;

      // Calculate branches coverage
      const branches = fileData.b || {};
      const branchCount = Object.keys(branches).length;
      const coveredBranchCount = Object.values(branches).filter(arr => arr.length > 1 ? arr.some(val => val > 0) : arr[0] > 0).length;

      // Calculate functions coverage
      const functions = fileData.f || {};
      const functionCount = Object.keys(functions).length;
      const coveredFunctionCount = Object.values(functions).filter(count => count > 0).length;

      // Calculate lines coverage (approximate from statements)
      totalLines += statementCount;
      coveredLines += coveredStatementCount;

      totalStatements += statementCount;
      coveredStatements += coveredStatementCount;

      totalBranches += branchCount;
      coveredBranches += coveredBranchCount;

      totalFunctions += functionCount;
      coveredFunctions += coveredFunctionCount;
    }

    // Calculate percentages
    const statementsPct = totalStatements > 0 ? (coveredStatements / totalStatements) * 100 : 0;
    const branchesPct = totalBranches > 0 ? (coveredBranches / totalBranches) * 100 : 0;
    const functionsPct = totalFunctions > 0 ? (coveredFunctions / totalFunctions) * 100 : 0;
    const linesPct = totalLines > 0 ? (coveredLines / totalLines) * 100 : 0;

    return {
      timestamp: new Date().toISOString(),
      lines: Math.round(linesPct),
      functions: Math.round(functionsPct),
      branches: Math.round(branchesPct),
      statements: Math.round(statementsPct),
      totalLines,
      coveredLines
    };
  }

  /**
   * Generate quality status report
   */
  generateQualityReport() {
    const report = {
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      coverage: this.latestReport,
      qualityMetrics: this.calculateQualityMetrics(),
      recommendations: this.generateRecommendations()
    };

    return report;
  }

  /**
   * Calculate comprehensive quality metrics
   */
  calculateQualityMetrics() {
    const coverage = this.latestReport;

    if (!coverage) {
      return {
        status: 'UNKNOWN',
        score: 0,
        warnings: ['No coverage data available']
      };
    }

    const avgCoverage = (coverage.lines + coverage.functions + coverage.branches + coverage.statements) / 4;
    const status = this.determineStatus(avgCoverage);

    return {
      status,
      score: Math.round(avgCoverage),
      coverage,
      warnings: this.identifyWarnings(coverage),
      thresholds: {
        warning: 40,
        critical: 20
      }
    };
  }

  /**
   * Determine quality status based on coverage scores
   */
  determineStatus(score) {
    if (score >= 75) return 'EXCELLENT';
    if (score >= 60) return 'GOOD';
    if (score >= 40) return 'WARNING';
    if (score >= 25) return 'CRITICAL';
    return 'UNACCEPTABLE';
  }

  /**
   * Identify quality warnings and issues
   */
  identifyWarnings(coverage) {
    const warnings = [];

    if (!coverage || coverage.lines < 40) {
      warnings.push('❌ Overall test coverage below acceptable threshold (<40%)');
    }

    if (coverage && coverage.functions < 25) {
      warnings.push('⚠️  Function coverage extremely low - high risk of untested business logic');
    }

    if (coverage && coverage.branches < 30) {
      warnings.push('⚠️  Branch coverage low - potential gaps in conditional logic testing');
    }

    const coverageHtmlFile = path.join(this.coveragePath, 'index.html');  // Directly in coverage directory
    if (!fs.existsSync(coverageHtmlFile)) {
      warnings.push('❌ Coverage HTML report not generated - visual inspection not available');
    }

    return warnings.length > 0 ? warnings : ['✅ No critical quality issues detected'];
  }

  /**
   * Generate actionable recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    const coverage = this.latestReport;

    if (!coverage || coverage.lines < 50) {
      recommendations.push({
        priority: 'HIGH',
        action: 'Increase test coverage in critical business logic modules',
        impact: 'Reduces production bug risk by up to 70%'
      });
    }

    if (coverage && coverage.branches < 40) {
      recommendations.push({
        priority: 'MEDIUM',
        action: 'Add test cases for edge cases and conditional branches',
        impact: 'Improves reliability for complex user flows'
      });
    }

    recommendations.push({
      priority: 'LOW',
      action: 'Schedule regular coverage reviews (weekly/monthly)',
      impact: 'Maintains long-term code quality standards'
    });

    return recommendations;
  }

  /**
   * Display formatted quality report
   */
  displayReport() {
    const report = this.generateQualityReport();

    console.log('\n' + '='.repeat(60));
    console.log('🎯 ROTARY CMS QUALITY MONITORING REPORT');
    console.log('='.repeat(60));
    console.log(`📅 Timestamp: ${new Date(report.timestamp).toLocaleString()}`);
    console.log(`🏗️  Version: ${report.version}`);
    console.log(`🌍 Environment: ${report.environment}`);

    // Coverage Section
    if (report.coverage) {
      console.log('\n📊 TEST COVERAGE METRICS');
      console.log('-'.repeat(30));
      console.log(`📝 Lines:      ${report.coverage.lines}%`);
      console.log(`🔧 Functions:  ${report.coverage.functions}%`);
      console.log(`🌿 Branches:   ${report.coverage.branches}%`);
      console.log(`📊 Statements: ${report.coverage.statements}%`);
      console.log(`📈 Average:    ${Math.round((report.coverage.lines + report.coverage.functions + report.coverage.branches + report.coverage.statements) / 4)}%`);
    }

    // Quality Status
    const metrics = report.qualityMetrics;
    console.log('\n🎯 QUALITY STATUS');
    console.log('-'.repeat(30));
    console.log(`📊 Score: ${metrics.score}/100`);
    console.log(`🏷️  Status: ${metrics.status}`);

    if (metrics.warnings && metrics.warnings.length > 0) {
      console.log('\n⚠️  QUALITY ALERTS');
      console.log('-'.repeat(30));
      metrics.warnings.forEach(warning => console.log(` ${warning}`));
    }

    // Recommendations
    console.log('\n💡 QUALITY IMPROVEMENT RECOMMENDATIONS');
    console.log('-'.repeat(30));
    report.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. [${rec.priority}] ${rec.action}`);
      console.log(`   🎯 Impact: ${rec.impact}\n`);
    });

    console.log('='.repeat(60));
    console.log('✅ Quality monitoring completed successfully');
    console.log('='.repeat(60));
  }

  /**
   * Export report to JSON file
   */
  exportReport() {
    const report = this.generateQualityReport();
    const outputPath = path.join(this.projectRoot, 'quality-report.json');

    fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
    console.log(`📄 Quality report exported to: ${outputPath}`);
  }
}

// Command line interface
function main() {
  const args = process.argv.slice(2);
  const monitor = new QualityMonitor();

  if (args.includes('--export') || args.includes('-e')) {
    monitor.exportReport();
  } else {
    monitor.displayReport();
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default QualityMonitor;