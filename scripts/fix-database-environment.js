/**
 * Database Environment Setup Script
 * Fixes MongoDB connection issues for Task 1.2.4 testing
 */

import { spawn } from 'child_process'
import fs from 'fs'
import path, { dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

async function setupEnvironment() {
  console.log('🔧 Setting up Task 1.2.4 Database Environment...\n')

  // Step 1: Check .env file
  console.log('1. Checking environment configuration...')
  const envPath = path.join(process.cwd(), '.env')
  const testEnvPath = path.join(process.cwd(), 'test.env')

  let envFile = testEnvPath
  if (!fs.existsSync(testEnvPath)) {
    envFile = envPath
  }

  if (!fs.existsSync(envFile)) {
    console.log('❌ No .env file found!')
    console.log('Expected one of:')
    console.log('  - .env')
    console.log('  - test.env')
    console.log('\nPlease create one with:\n')
    console.log('DATABASE_URI=mongodb://localhost:27017/rotary')
    console.log('PAYLOAD_SECRET=a-very-long-random-secret-at-least-32-characters')
    console.log('NODE_ENV=development')
    process.exit(1)
  }

  // Read and validate .env file
  const envContent = fs.readFileSync(envFile, 'utf8')
  const requiredVars = ['DATABASE_URI', 'PAYLOAD_SECRET']

  console.log(`✓ Found ${envFile}`)
  console.log('Checking required environment variables...')

  for (const varName of requiredVars) {
    if (!envContent.includes(`${varName}=`)) {
      console.log(`❌ Missing required environment variable: ${varName}`)
    } else {
      console.log(`✅ ${varName} is configured`)
    }
  }

  // Step 2: Check MongoDB connection
  console.log('\n2. Checking MongoDB connection...')
  const mongoUri = extractEnvVar(envContent, 'DATABASE_URI')

  return new Promise((resolve, reject) => {
    const mongoTest = spawn('mongosh', ['--eval', 'db.adminCommand("ping")', mongoUri], {
      stdio: 'pipe'
    })

    let output = ''
    let errorOutput = ''

    mongoTest.stdout.on('data', (data) => {
      output += data.toString()
    })

    mongoTest.stderr.on('data', (data) => {
      errorOutput += data.toString()
    })

    mongoTest.on('close', (code) => {
      if (code === 0) {
        console.log('✅ MongoDB connection successful')
        console.log('✅ Database is accessible')
      } else {
        console.log('❌ MongoDB connection failed')
        console.log('Error details:')
        console.log(errorOutput)
        console.log('\nTroubleshooting:')
        console.log('1. Make sure MongoDB is installed and running')
        console.log('2. Start MongoDB service:')
        console.log('   macOS: brew services start mongodb-community')
        console.log('   Linux/Windows: sudo systemctl start mongod')
        console.log('3. Create database if needed: mongosh --eval "use rotary"')
      }

      // Step 3: Check/TypeScript compilation
      console.log('\n3. Checking TypeScript compilation...')
      try {
        // Check if tsx is available (our TypeScript runner)
        const tsxCheck = spawn('which', ['tsx'], { stdio: 'pipe' })
        tsxCheck.on('close', (code) => {
          if (code === 0) {
            console.log('✅ TypeScript execution (tsx) is available')
          } else {
            console.log('⚠️ tsx not found - will use npx tsx for migration')
          }
        })
      } catch (e) {
        console.log('⚠️ TypeScript tools verification failed - will proceed anyway')
      }

      // Step 4: Verify project structure
      console.log('\n4. Verifying project structure...')
      const requiredFiles = [
        'src/payload.config.ts',
        'src/collections/Users/<USER>',
        'src/app/(payload)/api/users/profile/route.ts',
        'src/app/(payload)/api/users/change-password/route.ts',
        'scripts/migrate-user-data.ts'
      ]

      for (const file of requiredFiles) {
        if (fs.existsSync(file)) {
          console.log(`✅ ${file} exists`)
        } else {
          console.log(`❌ ${file} is missing`)
        }
      }

      console.log('\n🔧 Environment setup complete!')
      console.log('\nNext steps:')
      console.log('1. Fix any reported issues above')
      console.log('2. Run migration: npm run migrate-data')
      console.log('3. Test APIs: npm run test tests/performance-baseline.test.ts')

      resolve()
    })
  })
}

function extractEnvVar(content, varName) {
  const lines = content.split('\n')
  for (const line of lines) {
    if (line.startsWith(`${varName}=`)) {
      return line.split('=')[1].trim()
    }
  }
  return null
}

// Run if called directly (ES module version)
if (import.meta.url === `file://${process.argv[1]}`) {
  setupEnvironment().catch(console.error)
}

export { setupEnvironment }
