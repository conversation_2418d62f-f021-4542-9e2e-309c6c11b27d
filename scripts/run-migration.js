/**
 * Migration Runner Script
 * Loads environment variables and runs the migration
 */

import { readFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { spawn } from 'child_process'

// Load environment variables from test.env
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const envFile = join(__dirname, '..', 'test.env')

try {
  const envContent = readFileSync(envFile, 'utf8')
  const envVars = {}

  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=')
    if (key && valueParts.length > 0) {
      envVars[key.trim()] = valueParts.join('=').trim()
    }
  })

  // Run the migration script with environment variables
  const migrateScript = join(__dirname, 'migrate-user-data.ts')

  const tsx = spawn('npx', ['tsx', migrateScript], {
    stdio: 'inherit',
    env: { ...process.env, ...envVars },
    cwd: join(__dirname, '..')
  })

  tsx.on('close', (code) => {
    process.exit(code)
  })

  tsx.on('error', (error) => {
    console.error('Migration runner failed:', error)
    process.exit(1)
  })

} catch (error) {
  console.error('Failed to load environment:', error)
  process.exit(1)
}
