/**
 * Simple Validation Test - Verify Environment and Basic Functionality
 * This tests core imports and environment setup without complex E2E workflows
 */

// Load environment variables from test.env
import { readFileSync } from 'fs'
import { join } from 'path'

const envFile = join(import.meta.dirname, 'test.env')
try {
  const envContent = readFileSync(envFile, 'utf8')
  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=')
    if (key && valueParts.length > 0) {
      const value = valueParts.join('=').trim().replace(/^["'`]|["'`]$/g, '')
      if (key.startsWith('NODE_OPTIONS')) return // Skip Node options
      process.env[key] = value
    }
  })
} catch (error) {
  console.log('⚠️  Warning: Could not load test.env file')
}

import { getPayload } from 'payload'
import configPromise from './src/payload.config.ts'

async function testBasicEnvironment() {
  console.log('🧪 Testing Basic Environment Setup...\n')

  // Test 1: Environment Variables
  console.log('1. Environment Variables:')
  console.log(`   DATABASE_URI: ${process.env.DATABASE_URI ? '✅ SET' : '❌ MISSING'}`)
  if (process.env.DATABASE_URI) {
    console.log(`   DATABASE_URI value: "${process.env.DATABASE_URI.substring(0, 50)}..."`)
  }
  console.log(`   PAYLOAD_SECRET: ${process.env.PAYLOAD_SECRET ? '✅ SET (length: ' + process.env.PAYLOAD_SECRET.length + ')' : '❌ MISSING'}`)

  // Test 2: Payload Configuration
  console.log('\n2. Payload Configuration:')
  try {
    const config = await configPromise
    console.log('   ✅ Payload config loaded successfully')

    // Debug the database configuration
    const dbConfig = config.db
    console.log(`   Database config exists: ${dbConfig ? '✅ Present' : '❌ Missing'}`)

    const dbUrl = config.db?.url
    console.log(`   Database URL from config.db.url: ${dbUrl ? '✅ Configured' : '❌ Missing'}`)

    // Debug the full database config structure
    if (dbConfig) {
      if (dbUrl) {
        console.log(`   Database URL value: "${dbUrl.substring(0, 50)}..."`)
      } else {
        console.log(`   Database config keys: ${Object.keys(dbConfig).join(', ')}`)
        console.log(`   Raw DATABASE_URI in process.env: ${process.env.DATABASE_URI}`)
      }
    } else {
      console.log('   Config.db is undefined/unaccessible')
    }

  } catch (error) {
    console.log(`   ❌ Payload config error: ${error.message}`)
  }

  // Test 3: Payload CMS Initialization
  console.log('\n3. Payload CMS Initialization:')
  try {
    const payload = await getPayload({ config: configPromise })
    console.log('   ✅ Payload CMS initialized successfully')

    // Test 4: Database Connection
    console.log('\n4. Database Collections:')
    try {
      const collections = payload.config.collections
      const userCollection = collections?.find(c => c.slug === 'users')
      console.log(`   Users collection: ${userCollection ? '✅ Found' : '❌ Missing'}`)
      console.log(`   Total collections: ${collections?.length || 0}`)

      // Test 5: User Schema Validation
      if (userCollection) {
        const fields = userCollection.fields || []
        const profileFields = fields.filter(f => f.name === 'profileCompletion')
        const lastLoginFields = fields.filter(f => f.name === 'lastLogin')

        console.log('\n5. User Schema Validation:')
        console.log(`   profileCompletion field: ${profileFields.length > 0 ? '✅ Present' : '❌ Missing'}`)
        console.log(`   lastLogin field: ${lastLoginFields.length > 0 ? '✅ Present' : '❌ Missing'}`)
        console.log(`   Total fields: ${fields.length}`)
      }

    } catch (dbError) {
      console.log(`   ❌ Database error: ${dbError.message}`)
    }

    // Test 6: API Routes (Basic)
    console.log('\n6. API Routes Structure:')
    const fs = await import('fs')
    const path = await import('path')

    const apiRoutesPath = './src/app/(payload)/api'
    if (fs.existsSync(apiRoutesPath)) {
      console.log('   ✅ API routes directory exists')
      const apiContents = fs.readdirSync(apiRoutesPath, { recursive: true })

      const hasProfileRoute = apiContents.some(item => item.includes('profile'))
      const hasChangePasswordRoute = apiContents.some(item => item.includes('change-password'))

      console.log(`   Profile API route: ${hasProfileRoute ? '✅ Found' : '❌ Missing'}`)
      console.log(`   Change Password API route: ${hasChangePasswordRoute ? '✅ Found' : '❌ Missing'}`)

    } else {
      console.log('   ❌ API routes directory missing')
    }

  } catch (error) {
    console.log(`   ❌ Payload CMS initialization failed: ${error.message}`)
    console.log('   Details:', error.stack)
  }

  console.log('\n🎯 VALIDATION COMPLETE\n')
}

// Run the test
testBasicEnvironment()
  .then(() => {
    console.log('✅ Basic validation completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Basic validation failed:', error)
    process.exit(1)
  })
