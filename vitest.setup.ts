// Any setup scripts you might need go here

// Load .env files
import 'dotenv/config'

// Configure global mocks and test utilities
import { vi } from 'vitest'

// Setup hooks need to be configured differently when using vitest
// These should be in the vitest config file instead
console.log('[VITEST] Setup file loaded - coverage collection enabled')

// GDPR compliance test utilities
export const gdprTestUtils = {
  // Mock request object for GDPR testing
  createMockRequest: (body?: any, headers?: Record<string, string>) => ({
    json: () => Promise.resolve(body || {}),
    headers: new Map(Object.entries(headers || {})),
    method: 'POST',
    url: 'http://localhost:3000/api/test'
  }),

  // Mock user data with GDPR compliance requirements
  mockGDPRCompliantUser: {
    id: 'gdpr-test-user-001',
    email: '<EMAIL>',
    name: {
      en: 'Test User',
      fr: 'Utilisateur de Test',
      ar: 'مستخدم اختبار'
    },
    privacySettings: {
      dataProcessingConsent: true,
      shareContactDetails: false,
      allowAnalytics: false,
      marketingEmails: false
    },
    consentRecords: [
      {
        type: 'data-processing',
        granted: true,
        grantedAt: new Date().toISOString(),
        source: 'gdpr-test'
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // GDPR assertion utilities
  assertGDPRCompliantUser: (user: any) => {
    expect(user).toHaveProperty('id')
    expect(user).toHaveProperty('email')
    expect(user).toHaveProperty('privacySettings')
    expect(user.privacySettings).toHaveProperty('dataProcessingConsent')
    expect(user.privacySettings).toHaveProperty('marketingEmails')
  },

  // Audit trail verification
  assertAuditTrail: (operation: any) => {
    expect(operation).toHaveProperty('timestamp')
    expect(operation).toHaveProperty('userId')
    expect(operation).toHaveProperty('operation')
    expect(operation.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
  }
}