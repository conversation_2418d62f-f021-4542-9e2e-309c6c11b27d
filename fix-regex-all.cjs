const fs = require('fs');

// Read the file
let content = fs.readFileSync('src/utilities/emailTemplates.ts', 'utf8');

// Manually fix each pattern
content = content.replace(/'font-family': \[\/\^\[\\\\w\\\\s,-\]\+\$\/\]/g, `'font-family': [new RegExp('^[\\\\w\\\\s,-]+$')]`);
content = content.replace(/'max-width': \[\/\^\\d\+\(px\|em\|\%\)\$\/\]/g, `'max-width': [new RegExp('^\\d+(px|em|%)$')]`);
content = content.replace(/'margin': \[\/\^\\d\+\(px\|em\|\%\)\(\\s\\d\+\(px\|em\|\%\)\)\{0,3\}\$\/\]/g, `'margin': [new RegExp('^\\d+(px|em|%)(\\s\\d+(px|em|%)){0,3}$')]`);
content = content.replace(/'padding': \[\/\^\\d\+\(px\|em\|\%\)\(\\s\\d\+\(px\|em\|\%\)\)\{0,3\}\$\/\]/g, `'padding': [new RegExp('^\\d+(px|em|%)(\\s\\d+(px|em|%)){0,3}$')]`);
content = content.replace(/'background': \[\/\^\[\\w\\s\\\$$\\\)\\#,.-\]\+\$\/\]/g, `'background': [new RegExp('^[\\w\\s\\(\\)#,.-]+$')]`);
content = content.replace(/'color': \[\/\^\[\\w\\s\\\$$\\\)\\#,.-\]\+\$\/\]/g, `'color': [new RegExp('^[\\w\\s\\(\\)#,.-]+$')]`);
content = content.replace(/'font-size': \[\/\^\\d\+\(px\|em\|\%\)\$\/\]/g, `'font-size': [new RegExp('^\\d+(px|em|%)$')]`);
content = content.replace(/'border-radius': \[\/\^\\d\+\(px\|em\|\%\)\$\/\]/g, `'border-radius': [new RegExp('^\\d+(px|em|%)$')]`);
content = content.replace(/'border-left': \[\/\^\(\\d\+px\\s\\w\+\\s\$$\w\#\]\+\)\$\/\]/g, `'border-left': [new RegExp('^(\\d+px\\s\\w+\\s[\\w#]+)$')]`);
content = content.replace(/'border-right': \[\/\^\(\\d\+px\\s\\w\+\\s\$$\w\#\]\+\)\$\/\]/g, `'border-right': [new RegExp('^(\\d+px\\s\\w+\\s[\\w#]+)$')]`);
content = content.replace(/'width': \[\/\^\\d\+\(px\|em\|\%\)\$\/\]/g, `'width': [new RegExp('^\\d+(px|em|%)$')]`);

// Write the fixed content back to the file
fs.writeFileSync('src/utilities/emailTemplates.ts', content);

console.log('Fixed all regex patterns in emailTemplates.ts');