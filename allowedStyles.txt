    // You might want to configure allowed styles more strictly based on your needs
    // For example, only allowing specific CSS properties
    allowedStyles: {
      '*': {
        'font-family': [new RegExp('^[\\w\\s,-]+$')],
        'max-width': [new RegExp('^\\d+(px|em|%)$')],
        'margin': [new RegExp('^\\d+(px|em|%)(\\s\\d+(px|em|%)){0,3}$')],
        'padding': [new RegExp('^\\d+(px|em|%)(\\s\\d+(px|em|%)){0,3}$')],
        'text-align': ['left', 'right', 'center', 'justify'],
        'background': [new RegExp('^[\\w\\s\\(\\)#,.-]+$')],
        'color': [new RegExp('^[\\w\\s\\(\\)#,.-]+$')],
        'font-size': [new RegExp('^\\d+(px|em|%)$')],
        'border-radius': [new RegExp('^\\d+(px|em|%)$')],
        'border-left': [new RegExp('^(\\d+px\\s\\w+\\s[\\w#]+)$')],
        'border-right': [new RegExp('^(\\d+px\\s\\w+\\s[\\w#]+)$')],
        'direction': ['ltr', 'rtl'],
        'width': [new RegExp('^\\d+(px|em|%)$')],
      }
    }