# Database connection string
DATABASE_URI=mongodb://127.0.0.1/your-database-name

# Or use a PG connection string
#DATABASE_URI=postgresql://127.0.0.1:5432/your-database-name

# Used to encrypt JWT tokens
PAYLOAD_SECRET=YOUR_SECRET_HERE

# Used to configure CORS, format links and more. No trailing slash
NEXT_PUBLIC_SERVER_URL=http://localhost:3000

# Secret used to authenticate cron jobs
CRON_SECRET=YOUR_CRON_SECRET_HERE

# Used to validate preview requests
PREVIEW_SECRET=YOUR_SECRET_HERE

# Resend API configuration
RESEND_API_KEY=your-resend-api-key-here

# Email configuration for SMTP
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email connection pool settings (optional)
SMTP_MAX_CONNECTIONS=5
SMTP_MAX_MESSAGES=100
SMTP_RATE_DELTA=1000
SMTP_RATE_LIMIT=5
