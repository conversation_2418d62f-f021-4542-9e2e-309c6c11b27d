/**
 * Dedicated Vitest configuration for GDPR compliance testing
 * Provides HTML coverage reporting and specialized GDPR test environment
 */
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  plugins: [tsconfigPaths({ root: '.' }), react()],
  test: {
    name: 'gdpr-tests',
    environment: 'node', // GDPR tests can run in node environment for better performance
    setupFiles: ['./vitest.setup.ts'],
    include: [
      'tests/gdpr/**/*.test.ts',
      // Include GDPR-related source files for coverage
      'src/app/(payload)/api/users/**/*.ts',
      'src/app/(payload)/api/users/**/*.tsx',
      'src/utilities/**/*.ts'
    ],
    exclude: [
      // Exclude non-GDPR related tests
      'tests/**/*.spec.ts',
      'tests/**/*.int.spec.ts',
      'tests/e2e/**',
      'tests/accessibility/**',
      'tests/performance/**',
      'tests/unit/**',
      // Exclude all other test files
      '**/*.test.ts',
      '!tests/gdpr/**/*.test.ts'
    ],
    coverage: {
      provider: 'v8',
      reporter: [
        'text',
        'json',
        'html',
        'lcov',
        'clover'  // Additional format for CI/CD integration
      ],
      all: false, // Only track files executed by GDPR tests
      include: [
        'src/app/(payload)/api/users/**/*.ts',
        'src/app/(payload)/api/users/**/*.tsx',
        'src/utilities/**/*.ts',
        'src/types/**/*.ts',
        'tests/gdpr/**/*.test.ts'
      ],
      exclude: [
        // Exclude generated files
        'src/payload-types.ts',
        // Exclude configuration files
        '**/*.config.*',
        '**/*.setup.*',
        // Exclude UI components (GDPR tests focus on data/API layers)
        'src/app/(frontend)/**',
        // Exclude documentation and test fixtures
        '**/*.md',
        'tests/**',
        '!tests/gdpr/**'
      ],
      reportsDirectory: './coverage/gdpr',
      // HTML reports will be generated automatically with the 'html' reporter
      // Detailed threshold configuration for GDPR compliance
      thresholds: {
        global: {
          statements: 95,  // High bar for GDPR compliance
          branches: 90,
          functions: 95,
          lines: 95
        },
        'src/app/(payload)/api/users/**/*.ts': {
          statements: 100,  // Critical GDPR compliance code
          branches: 95,
          functions: 100,
          lines: 100
        }
      },
      reportOnFailure: true,  // Always generate reports even if tests fail
    },
    // GDPR-specific test settings
    globals: true,
    deps: {
      interopDefault: true,
      fallbackCJS: true,  // Handle both ESM and CommonJS
      moduleDirectories: ['node_modules', 'src']
    },
    // Timeout settings for GDPR compliance tests
    testTimeout: 10000, // 10 seconds for GDPR tests (may involve database operations)
    hookTimeout: 5000,
    // Pool options for GDPR tests
    pool: 'threads',  // Use thread pool for better resource isolation
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true  // Isolate each test for GDPR compliance
      }
    }
  },
  // Resolve configuration for GDPR-specific imports
  resolve: {
    alias: {
      // Ensure proper path resolution for GDPR-related modules
      '@/utilities': './src/utilities',
      '@payload-config': './src/payload.config.ts'
    }
  },
  // Optimize for GDPR test performance
  optimizeDeps: {
    include: [
      'vitest',
      '@vitest/runner',
      'vitest/globals',
      '@payloadcms/payload',
      'payload/utilities',
      '@payload-config'
    ],
    exclude: [
      'src/app/(frontend)/**'  // Exclude frontend components to reduce bundle size
    ]
  }
})