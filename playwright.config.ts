import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  // ✅ Web server configuration (optional - comment out if you start dev server manually)
  // webServer: {
  //   command: 'pnpm run dev',
  //   url: 'http://localhost:3000',
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120 * 1000, // 2 minutes
  // },
  
  // ✅ Global config for consistent testing
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 10000, // 10 seconds for individual actions
    navigationTimeout: 30000, // 30 seconds for navigation
  },

  // ✅ Test directory configuration
  testDir: './tests',
  testMatch: '**/e2e/**/*.spec.ts',
  timeout: 30 * 1000, // 30 seconds per test (more reasonable)
  expect: {
    timeout: 5000, // 5 seconds for assertions
  },
  
  // ✅ Browser configurations for comprehensive testing
  projects: [
    {
      name: 'Chrome Headless',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'Firefox Headless',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'Safari Headless',
      use: { 
        ...devices['Desktop Safari'],
        // Handle WebKit-specific issues
        launchOptions: {
          args: ['--no-sandbox']
        }
      },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  
  // ✅ Retry and output configuration
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  // ✅ Reporter configuration
  reporter: process.env.CI 
    ? [['github'], ['allure-playwright', {}]]
    : [['list'], ['html']],

  // ✅ Global setup/cleanup
  globalSetup: './tests/global-setup.ts',
  globalTeardown: './tests/global-teardown.ts',
})